package tasks

import (
	"fmt"
	"os"
	"strings"
	"time"
	"wlhy/model"
	"wlhy/toolbox"
	"wlhy/toolbox/email"
	"wlhy/toolbox/logger"

	"github.com/mozillazg/go-pinyin"
	"github.com/xuri/excelize/v2"
)

// Insurance 保险投保（东海）
func Insurance() {
	rows, err := model.DB.Table("tms_transport_note_insurance_record AS a").
		Joins("JOIN tms_transport_note AS b ON a.transport_note_number = b.transportation_number").
		Joins("JOIN tms_order_history AS c ON b.order_id = c.id AND b.order_version_no = c.version_no").
		Joins("JOIN tms_shipper AS d ON b.create_company_id = d.company_id").
		Select([]string{"b.transportation_number", "b.loading_time", "b.transportation_plate", "c.loading_name",
			"c.loading_address", "c.unload_name", "c.unload_address", "c.goods_type", "b.freight_amount",
			"b.payee_id", "d.company_name", "c.goods_name"}).
		Where("a.insurance_status = ?", 0).
		Where("a.insurance_company = ?", 2).
		Where("a.create_time >= ?", "2024-11-26 09:00:00").
		Where("b.loading_number > ?", 0).
		Where("d.shipper_type = ? AND d.is_delete = ?", 1, 0).
		Rows()
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	type InsuranceT struct {
		SubmitDate           string // 申报日期
		LoadingDate          string // 起运日期
		Insured              string // 被保险人全称
		Plate                string // 车牌号
		LoadingAddress       string // 起运地
		UnloadAddress        string // 目的地
		GoodsType            string // 标的类型
		FreightAmount        string // 运费
		Limits               string // 限额
		Premium              string // 保费
		TransportationNumber string // 运单号(或有）
		Remark               string // 备注
		PayeeID              string
		CompanyName          string
	}

	var insurances []InsuranceT
	for rows.Next() {
		var transportationNumber string
		var loadingTime time.Time
		var transportationPlate string
		var loadingName string
		var loadingAddress string
		var unloadName string
		var unloadAddress string
		var goodsType string
		var freightAmount float64
		var payeeID string
		var companyName string
		var goodsName string

		if err := rows.Scan(&transportationNumber, &loadingTime, &transportationPlate, &loadingName,
			&loadingAddress, &unloadName, &unloadAddress, &goodsType, &freightAmount, &payeeID,
			&companyName, &goodsName); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}

		if strings.Contains(loadingAddress, loadingName) {
			loadingAddress = strings.ReplaceAll(loadingAddress, loadingName, "")
			loadingAddress = loadingName + loadingAddress
		}
		if strings.Contains(unloadAddress, unloadName) {
			unloadAddress = strings.ReplaceAll(unloadAddress, unloadName, "")
			unloadAddress = unloadName + unloadAddress
		}

		insuranceGoodsType := goodsName
		if goodsType == "煤炭及制品" {
			insuranceGoodsType = "煤炭"
		}
		if goodsType == "钢铁" {
			insuranceGoodsType = "钢材"
		}

		// 货主支付的运费总金额，不包含服务费
		amount := toolbox.RoundToDecimal(freightAmount, 2)

		// 限额
		limits := ""
		if insuranceGoodsType == "煤炭" {
			limits = "100000.00"
		} else {
			limits = "200000.00"
		}

		// 保费
		premium := ""
		if insuranceGoodsType == "煤炭" {
			premium = "1.00"
		} else {
			tmpPremium := toolbox.RoundToDecimal(amount*0.0016, 2)
			if tmpPremium < 3 {
				premium = "3.00"
			} else {
				premium = fmt.Sprintf("%0.2f", tmpPremium)
			}
		}

		// 特殊规则
		// 内蒙古天壹成信环保科技有限公司 石英砂
		if companyName == "内蒙古天壹成信环保科技有限公司" && insuranceGoodsType == "石英砂" {
			premium = "3.00"
		}

		p := pinyin.NewArgs()
		p.Style = pinyin.FirstLetter
		py := pinyin.Pinyin(companyName, p)
		code := ""
		for _, v := range py {
			code += v[0]
		}

		insurances = append(insurances, InsuranceT{
			SubmitDate:           time.Now().Format("2006-01-02 15:04:05"),
			LoadingDate:          loadingTime.Format("2006-01-02 15:04:05"),
			Insured:              "赤峰现代智慧物流有限公司",
			Plate:                transportationPlate,
			LoadingAddress:       loadingAddress,
			UnloadAddress:        unloadAddress,
			GoodsType:            insuranceGoodsType,
			FreightAmount:        fmt.Sprintf("%0.2f", amount),
			Limits:               limits,
			Premium:              premium,
			TransportationNumber: transportationNumber,
			Remark:               "",
			PayeeID:              payeeID,
			CompanyName:          strings.ToUpper(code),
		})
	}

	f := excelize.NewFile()
	defer f.Close()

	f.SetCellValue("Sheet1", "A1", "申报日期")
	f.SetCellValue("Sheet1", "B1", "起运日期")
	f.SetCellValue("Sheet1", "C1", "被保险人全称")
	f.SetCellValue("Sheet1", "D1", "车牌号")
	f.SetCellValue("Sheet1", "E1", "起运地")
	f.SetCellValue("Sheet1", "F1", "目的地")
	f.SetCellValue("Sheet1", "G1", "标的类型")
	f.SetCellValue("Sheet1", "H1", "运费")
	f.SetCellValue("Sheet1", "I1", "限额")
	f.SetCellValue("Sheet1", "J1", "保费")
	f.SetCellValue("Sheet1", "K1", "运单号(或有)")
	f.SetCellValue("Sheet1", "L1", "备注")
	f.SetCellValue("Sheet1", "M1", "发货人")

	for k, v := range insurances {
		f.SetCellValue("Sheet1", fmt.Sprintf("A%d", k+2), v.SubmitDate)
		f.SetCellValue("Sheet1", fmt.Sprintf("B%d", k+2), v.LoadingDate)
		f.SetCellValue("Sheet1", fmt.Sprintf("C%d", k+2), v.Insured)
		f.SetCellValue("Sheet1", fmt.Sprintf("D%d", k+2), v.Plate)
		f.SetCellValue("Sheet1", fmt.Sprintf("E%d", k+2), v.LoadingAddress)
		f.SetCellValue("Sheet1", fmt.Sprintf("F%d", k+2), v.UnloadAddress)
		f.SetCellValue("Sheet1", fmt.Sprintf("G%d", k+2), v.GoodsType)
		f.SetCellValue("Sheet1", fmt.Sprintf("H%d", k+2), v.FreightAmount)
		f.SetCellValue("Sheet1", fmt.Sprintf("I%d", k+2), v.Limits)
		f.SetCellValue("Sheet1", fmt.Sprintf("J%d", k+2), v.Premium)
		f.SetCellValue("Sheet1", fmt.Sprintf("K%d", k+2), v.TransportationNumber)
		f.SetCellValue("Sheet1", fmt.Sprintf("L%d", k+2), v.Remark)
		f.SetCellValue("Sheet1", fmt.Sprintf("M%d", k+2), v.CompanyName)
	}

	// 设置样式
	s1, _ := f.NewStyle(&excelize.Style{
		Border: []excelize.Border{
			{Type: "left", Color: "#000000", Style: 1},
			{Type: "right", Color: "#000000", Style: 1},
			{Type: "top", Color: "#000000", Style: 1},
			{Type: "bottom", Color: "#000000", Style: 1},
		},
		Font: &excelize.Font{
			Size: 10,
		},
		Alignment: &excelize.Alignment{
			Horizontal: "center",
			Vertical:   "center",
			WrapText:   true,
		},
	})
	f.SetCellStyle("Sheet1", "A1", fmt.Sprintf("M%d", len(insurances)+1), s1)

	f.SetColWidth("Sheet1", "A", "B", 18)
	f.SetColWidth("Sheet1", "C", "C", 20)
	f.SetColWidth("Sheet1", "G", "J", 10)
	f.SetColWidth("Sheet1", "K", "K", 20)
	f.SetColWidth("Sheet1", "M", "M", 35)

	// 更改投保状态
	for _, v := range insurances {
		if err := model.DB.Table("tms_transport_note_insurance_record").
			Where("transport_note_number = ?", v.TransportationNumber).
			Updates(map[string]any{
				"insurance_status":            1,
				"policy_number":               "",
				"insurance_pdf":               "",
				"insurance_time":              time.Now().Format("2006-01-02 15:04:05"),
				"driver_id":                   v.PayeeID,
				"thirdparty_insurance_amount": v.Premium,
			}).Error; err != nil {
			logger.Stdout.Error(err.Error())
			logger.Stdout.Error(fmt.Sprintf("%s投保失败\n", v.TransportationNumber))
		}
	}

	f, err = cancelInsuranceNote(f)
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	filename := fmt.Sprintf("赤峰现代智慧物流有限公司-%s投保信息.xlsx", time.Now().Format("20060102"))
	if err := f.SaveAs(filename); err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	// 发送邮件
	e := &email.Email{
		To:      []string{"<EMAIL>", "<EMAIL>", "<EMAIL>"},
		Subject: fmt.Sprintf("赤峰现代智慧物流有限公司-%s投保信息", time.Now().Format("20060102")),
		Attach:  filename,
	}
	if err := e.SendEmail(); err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	os.Remove(filename)

	logger.Stdout.Info("Insurance Done")
}

func cancelInsuranceNote(f *excelize.File) (*excelize.File, error) {
	cancelNotesRows, err := model.DB.Table("tms_transport_note_insurance_record AS a").
		Joins("JOIN tms_transport_note AS b ON a.transport_note_number = b.transportation_number").
		Select("a.id").
		Where("a.insurance_company = ?", 2).
		Where("a.insurance_status = ?", 1).
		Where("a.is_cancel_insurance = ?", 0).
		Where("b.waybill_status IN (?)", []int{6, 7}).
		Rows()
	if err != nil {
		logger.Stdout.Error(err.Error())
		return nil, err
	}
	var ids []string
	for cancelNotesRows.Next() {
		var id string
		if err := cancelNotesRows.Scan(&id); err != nil {
			continue
		}
		ids = append(ids, id)
	}
	if len(ids) == 0 {
		return f, nil
	}

	if err := model.DB.Table("tms_transport_note_insurance_record").
		Where("id IN ?", ids).
		Update("is_cancel_insurance", 2).Error; err != nil {
		logger.Stdout.Error(err.Error())
		return nil, err
	}

	rows, err := model.DB.Table("tms_transport_note_insurance_record AS a").
		Joins("JOIN tms_transport_note AS b ON a.transport_note_number = b.transportation_number").
		Joins("JOIN tms_order_history AS c ON b.order_id = c.id AND b.order_version_no = c.version_no").
		Joins("JOIN tms_shipper AS d ON b.create_company_id = d.company_id").
		Select([]string{"b.transportation_number", "b.loading_time", "b.transportation_plate", "c.loading_name",
			"c.loading_address", "c.unload_name", "c.unload_address", "c.goods_type", "b.freight_amount",
			"b.payee_id", "d.company_name", "c.goods_name"}).
		Where("a.insurance_status = ?", 1).
		Where("a.insurance_company = ?", 2).
		Where("a.is_cancel_insurance = ?", 2).
		Where("b.waybill_status IN (?)", []int{6, 7}).
		Where("d.shipper_type = ? AND d.is_delete = ?", 1, 0).
		Rows()
	if err != nil {
		logger.Stdout.Error(err.Error())
		return nil, err
	}

	type InsuranceT struct {
		SubmitDate           string // 申报日期
		LoadingDate          string // 起运日期
		Insured              string // 被保险人全称
		Plate                string // 车牌号
		LoadingAddress       string // 起运地
		UnloadAddress        string // 目的地
		GoodsType            string // 标的类型
		FreightAmount        string // 运费
		Limits               string // 限额
		Premium              string // 保费
		TransportationNumber string // 运单号(或有）
		Remark               string // 备注
		PayeeID              string
		CompanyName          string
	}

	var insurances []InsuranceT
	for rows.Next() {
		var transportationNumber string
		var loadingTime time.Time
		var transportationPlate string
		var loadingName string
		var loadingAddress string
		var unloadName string
		var unloadAddress string
		var goodsType string
		var freightAmount float64
		var payeeID string
		var companyName string
		var goodsName string

		if err := rows.Scan(&transportationNumber, &loadingTime, &transportationPlate, &loadingName,
			&loadingAddress, &unloadName, &unloadAddress, &goodsType, &freightAmount, &payeeID,
			&companyName, &goodsName); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}

		if strings.Contains(loadingAddress, loadingName) {
			loadingAddress = strings.ReplaceAll(loadingAddress, loadingName, "")
			loadingAddress = loadingName + loadingAddress
		}
		if strings.Contains(unloadAddress, unloadName) {
			unloadAddress = strings.ReplaceAll(unloadAddress, unloadName, "")
			unloadAddress = unloadName + unloadAddress
		}

		insuranceGoodsType := "其他"
		if goodsType == "煤炭及制品" {
			insuranceGoodsType = "煤炭"
		}
		if goodsType == "钢铁" {
			insuranceGoodsType = "钢材"
		}

		// 货主支付的运费总金额，不包含服务费
		amount := toolbox.RoundToDecimal(freightAmount, 2)

		// 限额
		limits := ""
		if insuranceGoodsType == "煤炭" {
			limits = "100000.00"
		} else {
			limits = "200000.00"
		}

		// 保费
		premium := ""
		if insuranceGoodsType == "煤炭" {
			premium = "1.00"
		} else {
			tmpPremium := toolbox.RoundToDecimal(amount*0.0016, 2)
			if tmpPremium < 3 {
				premium = "3.00"
			} else {
				premium = fmt.Sprintf("%0.2f", tmpPremium)
			}
		}

		// 特殊规则
		// 内蒙古天壹成信环保科技有限公司 石英砂
		if companyName == "内蒙古天壹成信环保科技有限公司" && goodsName == "石英砂" {
			premium = "3.00"
		}

		p := pinyin.NewArgs()
		p.Style = pinyin.FirstLetter
		py := pinyin.Pinyin(companyName, p)
		code := ""
		for _, v := range py {
			code += v[0]
		}

		insurances = append(insurances, InsuranceT{
			SubmitDate:           time.Now().Format("2006-01-02 15:04:05"),
			LoadingDate:          loadingTime.Format("2006-01-02 15:04:05"),
			Insured:              "赤峰现代智慧物流有限公司",
			Plate:                transportationPlate,
			LoadingAddress:       loadingAddress,
			UnloadAddress:        unloadAddress,
			GoodsType:            insuranceGoodsType,
			FreightAmount:        fmt.Sprintf("%0.2f", amount),
			Limits:               limits,
			Premium:              premium,
			TransportationNumber: transportationNumber,
			Remark:               "运单已取消",
			PayeeID:              payeeID,
			CompanyName:          strings.ToUpper(code),
		})
	}

	f.NewSheet("Sheet2")

	f.SetCellValue("Sheet2", "A1", "申报日期")
	f.SetCellValue("Sheet2", "B1", "起运日期")
	f.SetCellValue("Sheet2", "C1", "被保险人全称")
	f.SetCellValue("Sheet2", "D1", "车牌号")
	f.SetCellValue("Sheet2", "E1", "起运地")
	f.SetCellValue("Sheet2", "F1", "目的地")
	f.SetCellValue("Sheet2", "G1", "标的类型")
	f.SetCellValue("Sheet2", "H1", "运费")
	f.SetCellValue("Sheet2", "I1", "限额")
	f.SetCellValue("Sheet2", "J1", "保费")
	f.SetCellValue("Sheet2", "K1", "运单号(或有)")
	f.SetCellValue("Sheet2", "L1", "备注")
	f.SetCellValue("Sheet2", "M1", "发货人")

	for k, v := range insurances {
		f.SetCellValue("Sheet2", fmt.Sprintf("A%d", k+2), v.SubmitDate)
		f.SetCellValue("Sheet2", fmt.Sprintf("B%d", k+2), v.LoadingDate)
		f.SetCellValue("Sheet2", fmt.Sprintf("C%d", k+2), v.Insured)
		f.SetCellValue("Sheet2", fmt.Sprintf("D%d", k+2), v.Plate)
		f.SetCellValue("Sheet2", fmt.Sprintf("E%d", k+2), v.LoadingAddress)
		f.SetCellValue("Sheet2", fmt.Sprintf("F%d", k+2), v.UnloadAddress)
		f.SetCellValue("Sheet2", fmt.Sprintf("G%d", k+2), v.GoodsType)
		f.SetCellValue("Sheet2", fmt.Sprintf("H%d", k+2), v.FreightAmount)
		f.SetCellValue("Sheet2", fmt.Sprintf("I%d", k+2), v.Limits)
		f.SetCellValue("Sheet2", fmt.Sprintf("J%d", k+2), v.Premium)
		f.SetCellValue("Sheet2", fmt.Sprintf("K%d", k+2), v.TransportationNumber)
		f.SetCellValue("Sheet2", fmt.Sprintf("L%d", k+2), v.Remark)
		f.SetCellValue("Sheet2", fmt.Sprintf("M%d", k+2), v.CompanyName)
	}

	// 设置样式
	s1, _ := f.NewStyle(&excelize.Style{
		Border: []excelize.Border{
			{Type: "left", Color: "#000000", Style: 1},
			{Type: "right", Color: "#000000", Style: 1},
			{Type: "top", Color: "#000000", Style: 1},
			{Type: "bottom", Color: "#000000", Style: 1},
		},
		Font: &excelize.Font{
			Size: 10,
		},
		Alignment: &excelize.Alignment{
			Horizontal: "center",
			Vertical:   "center",
			WrapText:   true,
		},
	})
	f.SetCellStyle("Sheet2", "A1", fmt.Sprintf("M%d", len(insurances)+1), s1)

	f.SetColWidth("Sheet2", "A", "B", 18)
	f.SetColWidth("Sheet2", "C", "C", 20)
	f.SetColWidth("Sheet2", "G", "J", 10)
	f.SetColWidth("Sheet2", "K", "K", 20)
	f.SetColWidth("Sheet2", "M", "M", 35)

	// 更改投保状态
	for _, v := range insurances {
		if err := model.DB.Table("tms_transport_note_insurance_record").
			Where("transport_note_number = ?", v.TransportationNumber).
			Updates(map[string]any{
				"is_cancel_insurance": 1,
			}).Error; err != nil {
			logger.Stdout.Error(err.Error())
			logger.Stdout.Error(fmt.Sprintf("%s取消投保失败\n", v.TransportationNumber))
		}
	}

	logger.Stdout.Info("Insurance Cancel Done")

	return f, nil
}
