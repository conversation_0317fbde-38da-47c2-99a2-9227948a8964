package tasks

import (
	"database/sql"
	"fmt"
	"os"
	"regexp"
	"strconv"
	"strings"
	"time"
	"wlhy/model"
	"wlhy/thirdparty/cmb"
	"wlhy/toolbox"
	"wlhy/toolbox/email"
	"wlhy/toolbox/logger"

	"github.com/samber/lo"
	"github.com/xuri/excelize/v2"
)

func ShipperAccount() {
	type ShipperT struct {
		CompanyName         string  `gorm:"column:company_name"`
		AccountBalanceLocal float64 `gorm:"column:account_balance_local"`
	}

	sysOrgCodes := map[string]string{
		"A03": "<EMAIL>",
		"A07": "<EMAIL>",
	}

	for sysOrgCode, emailAddress := range sysOrgCodes {
		var shippers []ShipperT
		if err := model.DB.Table("tms_shipper AS a").
			Joins("JOIN tms_shipper_balance AS b ON a.shipper_id = b.shipper_id").
			Select([]string{"a.company_name", "b.account_balance_local"}).
			Where("a.shipper_type = ?", 1).
			Where("a.is_delete = ?", 0).
			Where("a.sys_org_code = ?", sysOrgCode).
			Find(&shippers).Error; err != nil {
			logger.Stdout.Error(err.Error())
			return
		}

		f := excelize.NewFile()
		f.SetCellValue("Sheet1", "A1", "公司名称")
		f.SetCellValue("Sheet1", "B1", "账户余额")

		for i, shipper := range shippers {
			f.SetCellValue("Sheet1", fmt.Sprintf("A%d", i+2), shipper.CompanyName)
			f.SetCellValue("Sheet1", fmt.Sprintf("B%d", i+2), shipper.AccountBalanceLocal)
		}

		if err := f.SaveAs("客户账户余额.xlsx"); err != nil {
			logger.Stdout.Error(err.Error())
			return
		}

		e := &email.Email{
			To:      []string{emailAddress},
			Subject: time.Now().Format("********") + "客户账户余额",
			Attach:  "客户账户余额.xlsx",
		}

		if err := e.SendEmail(); err != nil {
			logger.Stdout.Error(err.Error())
			return
		}
		os.Remove("客户账户余额.xlsx")
	}

	logger.Stdout.Info("客户账户余额数据邮件发送成功")
}

func FinanceTransportNote() {

	startTime := time.Now().Add(-30*24*time.Hour).Format("2006-01") + "-01 00:00:00"
	endTime := time.Now().Format("2006-01-02") + " 00:00:00"

	db := model.DB.Table("tms_transport_note AS a").
		Joins("JOIN tms_order_history AS b ON a.order_id = b.id AND a.order_version_no = b.version_no").
		Joins("JOIN tms_shipper AS c ON b.create_company_id = c.shipper_id").
		Select([]string{
			"a.id",
			"b.goods_number",
			"a.transportation_number",
			"a.waybill_status",
			"b.radio_value",
			"a.univalent_should",
			"b.owner_unit_price",
			"a.freight_gross",
			"a.freight_amount",
			"a.service_charge",
			"b.goods_type",
			"b.goods_name",
			"b.line_name",
			"b.loading_name",
			"b.loading_address",
			"a.loading_time",
			"a.loading_number",
			"b.unload_name",
			"b.unload_address",
			"a.unload_time",
			"a.unload_number",
			"a.transportation_driver",
			"a.transportation_phone",
			"a.transportation_plate",
			"c.company_name",
			"b.creater_name",
			"b.creater_phone",
			"b.create_time",
			"b.is_road_loss",
			"b.road_loss_mode",
			"b.goods_value",
			"a.road_loss_settlement_method",
			"a.road_loss_state",
			"a.loss_weight_of_goods",
			"deduction_of_loss_expenses",
			"a.freight_accounting_status",
			"b.remarks",
			"a.service_fee_paid",
			"a.pay_time",
			"a.oil_card_online",
			"a.actualmileage",
			"a.freight_paid",
			"a.oil_card_amount",
			"a.etc_fee",
		}).
		Where("a.sys_org_code = ?", "A03").
		Where("a.is_region_out = ?", 0).
		Where("a.is_delete = ?", 0).
		Where("a.waybill_status IN (?)", []int{1, 2, 3, 4, 5, 8, 9}).
		Where("c.shipper_type = ?", 1).
		Where("a.finished_time BETWEEN ? AND ?", startTime, endTime)

	type firstCreateTimeT struct {
		CreateTime time.Time `gorm:"column:create_time"`
	}
	var firstCreateTime firstCreateTimeT
	orderDb := db
	orderDb.Order("a.create_time").Scan(&firstCreateTime)

	rows, err := db.Order("a.finished_time DESC").Rows()
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	// 获取支付计划数据
	planRows, err := model.DB.Table("tms_transport_pay_plan").
		Select([]string{"waybill_No", "paytype", "transport_Amount", "insurance_Amount", "commission_Amount"}).
		Where("create_time BETWEEN ? AND ?", startTime, endTime).
		Where("is_delete = ?", 0).
		Rows()
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	type planT struct {
		PayPlanType      int
		TransportAmount  float64
		InsuranceAmount  float64
		CommissionAmount float64
	}
	plans := make(map[string]planT)
	for planRows.Next() {
		var waybillNo string
		var payType int
		var transportAmount float64
		var insuranceAmount float64
		var commissionAmount float64

		if err := planRows.Scan(&waybillNo, &payType, &transportAmount, &insuranceAmount, &commissionAmount); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}

		plans[waybillNo] = planT{
			PayPlanType:      payType,
			TransportAmount:  transportAmount,
			InsuranceAmount:  insuranceAmount,
			CommissionAmount: commissionAmount,
		}
	}

	// 获取运单开票数据
	invoiceOpen := make(map[string]time.Time)
	invoiceRows, err := model.DB.Table("tms_invoice_list").
		Select([]string{"transportation_ids", "manager_time"}).
		Where("invoice_state = ?", 2).
		Where("is_delete = ?", 0).
		Where("create_time >= ?", startTime).
		Rows()
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	for invoiceRows.Next() {
		var transportationIds string
		var managerTime sql.NullTime
		if err := invoiceRows.Scan(&transportationIds, &managerTime); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}
		if transportationIds != "" {
			notes := strings.Split(transportationIds, ",")
			for _, v := range notes {
				if v == " " || v == "," || v == "" {
					continue
				}
				if managerTime.Valid {
					invoiceOpen[v] = managerTime.Time
				}
			}
		}
	}

	// 获取保险数据
	insuranceData := make(map[string]map[string]any)
	insuranceRows, err := model.DB.Table("tms_transport_note_insurance_record").
		Select([]string{"transport_note_number", "insurance_time", "thirdparty_insurance_amount", "insurance_company"}).
		Where("insurance_status = ?", 1).
		Where("is_delete = ?", 0).
		Where("create_time BETWEEN ? AND ?", firstCreateTime.CreateTime.Format("2006-01-02 15:04:05"), endTime).
		Rows()
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	for insuranceRows.Next() {
		var transportNoteNumber string
		var insuranceTime time.Time
		var thirdpartyInsuranceAmount sql.NullFloat64
		var insuranceCompany int
		if err := insuranceRows.Scan(&transportNoteNumber, &insuranceTime, &thirdpartyInsuranceAmount, &insuranceCompany); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}

		insuranceCompanyDisplay := "太平"
		if insuranceCompany == 2 {
			insuranceCompanyDisplay = "东海"
		}
		insuranceData[transportNoteNumber] = map[string]any{
			"insurance_time":              insuranceTime.Format("2006-01-02 15:04:05"),
			"thirdparty_insurance_amount": thirdpartyInsuranceAmount.Float64,
			"insurance_company":           insuranceCompanyDisplay,
		}
	}

	f := excelize.NewFile()
	defer func() {
		if err := f.Close(); err != nil {
			logger.Stdout.Error(err.Error())
		}
	}()

	// Create a new sheet.
	index, err := f.NewSheet("Sheet1")
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	// Set value of a cell.
	f.SetCellValue("Sheet1", "A1", "货单号")
	f.SetCellValue("Sheet1", "B1", "运单号")
	f.SetCellValue("Sheet1", "C1", "运单状态")
	f.SetCellValue("Sheet1", "D1", "结算方式")
	f.SetCellValue("Sheet1", "E1", "司机单价")
	f.SetCellValue("Sheet1", "F1", "货主单价")
	f.SetCellValue("Sheet1", "G1", "应付总运费")
	f.SetCellValue("Sheet1", "H1", "运费金额")
	f.SetCellValue("Sheet1", "I1", "服务费金额")
	f.SetCellValue("Sheet1", "J1", "货物大类型")
	f.SetCellValue("Sheet1", "K1", "货物小类型")
	f.SetCellValue("Sheet1", "L1", "线路名称")
	f.SetCellValue("Sheet1", "M1", "装货地址")
	f.SetCellValue("Sheet1", "N1", "装货时间")
	f.SetCellValue("Sheet1", "O1", "装货重量")
	f.SetCellValue("Sheet1", "P1", "卸货地址")
	f.SetCellValue("Sheet1", "Q1", "卸货时间")
	f.SetCellValue("Sheet1", "R1", "卸货重量")
	f.SetCellValue("Sheet1", "S1", "运输司机姓名")
	f.SetCellValue("Sheet1", "T1", "运输司机电话")
	f.SetCellValue("Sheet1", "U1", "运输车牌号")
	f.SetCellValue("Sheet1", "V1", "公司名称")
	f.SetCellValue("Sheet1", "W1", "货单创建人")
	f.SetCellValue("Sheet1", "X1", "货单创建人电话")
	f.SetCellValue("Sheet1", "Y1", "创建日期")
	f.SetCellValue("Sheet1", "Z1", "是否开启路耗")
	f.SetCellValue("Sheet1", "AA1", "路耗模式")
	f.SetCellValue("Sheet1", "AB1", "货物价值")
	f.SetCellValue("Sheet1", "AC1", "路耗结算方式")
	f.SetCellValue("Sheet1", "AD1", "路耗状态")
	f.SetCellValue("Sheet1", "AE1", "亏损重量")
	f.SetCellValue("Sheet1", "AF1", "亏损费用")
	f.SetCellValue("Sheet1", "AG1", "运费核算状态")
	f.SetCellValue("Sheet1", "AH1", "备注")
	f.SetCellValue("Sheet1", "AI1", "已支付服务费")
	f.SetCellValue("Sheet1", "AJ1", "支付时间")
	f.SetCellValue("Sheet1", "AK1", "加油宝费用")
	f.SetCellValue("Sheet1", "AL1", "实际里程")
	f.SetCellValue("Sheet1", "AM1", "已支付运费")
	f.SetCellValue("Sheet1", "AN1", "电子油卡金额")
	f.SetCellValue("Sheet1", "AO1", "预付金额")
	f.SetCellValue("Sheet1", "AP1", "保险费用")
	f.SetCellValue("Sheet1", "AQ1", "是否开票")
	f.SetCellValue("Sheet1", "AR1", "开票时间")
	f.SetCellValue("Sheet1", "AS1", "保险公司")
	f.SetCellValue("Sheet1", "AT1", "投保时间")
	f.SetCellValue("Sheet1", "AU1", "投保金额")
	f.SetCellValue("Sheet1", "AV1", "手续费")
	f.SetCellValue("Sheet1", "AW1", "ETC金额")

	// Set active sheet of the workbook.
	f.SetActiveSheet(index)

	start := 2
	for rows.Next() {
		var id string
		var goodsNumber sql.NullString
		var transportationNumber sql.NullString
		var waybillStatus sql.NullInt64
		var radioValue sql.NullInt64
		var univalentShould sql.NullFloat64
		var ownerUnitPrice sql.NullFloat64
		var freightGross sql.NullFloat64
		var freightAmount sql.NullFloat64
		var serviceCharge sql.NullFloat64
		var goodsType sql.NullString
		var goodsName sql.NullString
		var lineName sql.NullString
		var loadingName sql.NullString
		var loadingAddress sql.NullString
		var loadingTime sql.NullTime
		var loadingNumber sql.NullString
		var unloadName sql.NullString
		var unloadAddress sql.NullString
		var unloadTime sql.NullTime
		var unloadNumber sql.NullString
		var transportationDriver sql.NullString
		var transportationPhone sql.NullString
		var transportationPlate sql.NullString
		var companyName sql.NullString
		var createrName sql.NullString
		var createrPhone sql.NullString
		var createTime sql.NullTime
		var isRoadLoss sql.NullInt64
		var roadLossMode sql.NullInt64
		var goodsValue sql.NullFloat64
		var roadLossSettlementMethod sql.NullInt64
		var roadLossState sql.NullInt64
		var lossWeightOfGoods sql.NullFloat64
		var deductionOfLossExpenses sql.NullFloat64
		var freightAccountingStatus sql.NullInt64
		var remarks sql.NullString
		var serviceFeePaid sql.NullFloat64
		var payTime sql.NullTime
		var oilCardOnline sql.NullFloat64
		var actualmileage sql.NullFloat64
		var freightPaid sql.NullFloat64
		var oilCardAmount sql.NullFloat64
		var etcFee sql.NullFloat64

		if err := rows.Scan(
			&id,
			&goodsNumber,
			&transportationNumber,
			&waybillStatus,
			&radioValue,
			&univalentShould,
			&ownerUnitPrice,
			&freightGross,
			&freightAmount,
			&serviceCharge,
			&goodsType,
			&goodsName,
			&lineName,
			&loadingName,
			&loadingAddress,
			&loadingTime,
			&loadingNumber,
			&unloadName,
			&unloadAddress,
			&unloadTime,
			&unloadNumber,
			&transportationDriver,
			&transportationPhone,
			&transportationPlate,
			&companyName,
			&createrName,
			&createrPhone,
			&createTime,
			&isRoadLoss,
			&roadLossMode,
			&goodsValue,
			&roadLossSettlementMethod,
			&roadLossState,
			&lossWeightOfGoods,
			&deductionOfLossExpenses,
			&freightAccountingStatus,
			&remarks,
			&serviceFeePaid,
			&payTime,
			&oilCardOnline,
			&actualmileage,
			&freightPaid,
			&oilCardAmount,
			&etcFee,
		); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}

		startS := strconv.Itoa(start)

		transportationStatus := ""
		switch waybillStatus.Int64 {
		case 0:
			transportationStatus = "待接单"
		case 1:
			transportationStatus = "待确认"
		case 2:
			transportationStatus = "运输中"
		case 3:
			transportationStatus = "待支付"
		case 4:
			transportationStatus = "待评价"
		case 5:
			transportationStatus = "已评价"
		case 6:
			transportationStatus = "已取消"
		case 7:
			transportationStatus = "已关闭 "
		case 8:
			transportationStatus = "已完成"
		case 9:
			transportationStatus = "待验收"
		}

		radioValueS := "按车次结算"
		if radioValue.Int64 == 2 {
			radioValueS = "按重量结算"
		}

		f.SetCellValue("Sheet1", "A"+startS, goodsNumber.String)
		f.SetCellValue("Sheet1", "B"+startS, transportationNumber.String)
		f.SetCellValue("Sheet1", "C"+startS, transportationStatus)
		f.SetCellValue("Sheet1", "D"+startS, radioValueS)
		f.SetCellValue("Sheet1", "E"+startS, univalentShould.Float64)
		f.SetCellValue("Sheet1", "F"+startS, ownerUnitPrice.Float64)
		f.SetCellValue("Sheet1", "G"+startS, freightGross.Float64)
		f.SetCellValue("Sheet1", "H"+startS, freightAmount.Float64)
		f.SetCellValue("Sheet1", "I"+startS, serviceCharge.Float64)
		f.SetCellValue("Sheet1", "J"+startS, goodsType.String)
		f.SetCellValue("Sheet1", "K"+startS, goodsName.String)
		f.SetCellValue("Sheet1", "L"+startS, lineName.String)

		if strings.Contains(loadingAddress.String, loadingName.String) {
			loadingAddress.String = strings.ReplaceAll(loadingAddress.String, loadingName.String, "")
		}
		f.SetCellValue("Sheet1", "M"+startS, loadingName.String+loadingAddress.String)
		f.SetCellValue("Sheet1", "N"+startS, loadingTime.Time.Format("2006-01-02 15:04:05"))
		f.SetCellValue("Sheet1", "O"+startS, loadingNumber.String)

		if strings.Contains(unloadAddress.String, unloadName.String) {
			unloadAddress.String = strings.ReplaceAll(unloadAddress.String, unloadName.String, "")
		}
		f.SetCellValue("Sheet1", "P"+startS, unloadName.String+unloadAddress.String)
		f.SetCellValue("Sheet1", "Q"+startS, unloadTime.Time.Format("2006-01-02 15:04:05"))
		f.SetCellValue("Sheet1", "R"+startS, unloadNumber.String)
		f.SetCellValue("Sheet1", "S"+startS, transportationDriver.String)
		f.SetCellValue("Sheet1", "T"+startS, transportationPhone.String)
		f.SetCellValue("Sheet1", "U"+startS, transportationPlate.String)
		f.SetCellValue("Sheet1", "V"+startS, companyName.String)
		f.SetCellValue("Sheet1", "W"+startS, createrName.String)
		f.SetCellValue("Sheet1", "X"+startS, createrPhone.String)
		f.SetCellValue("Sheet1", "Y"+startS, createTime.Time.Format("2006-01-02 15:04:05"))

		if isRoadLoss.Int64 == 0 {
			f.SetCellValue("Sheet1", "Z"+startS, "否")
		} else {
			f.SetCellValue("Sheet1", "Z"+startS, "是")
		}

		switch roadLossMode.Int64 {
		case 0:
			f.SetCellValue("Sheet1", "AA"+startS, "无")
		case 1:
			f.SetCellValue("Sheet1", "AA"+startS, "按比例")
		case 2:
			f.SetCellValue("Sheet1", "AA"+startS, "按数量")
		}

		f.SetCellValue("Sheet1", "W"+startS, goodsValue.Float64)

		switch roadLossSettlementMethod.Int64 {
		case 1:
			f.SetCellValue("Sheet1", "AC"+startS, "取装货重量")
		case 2:
			f.SetCellValue("Sheet1", "AC"+startS, "取卸货重量")
		case 3:
			f.SetCellValue("Sheet1", "AC"+startS, "装卸货取大")
		case 4:
			f.SetCellValue("Sheet1", "AC"+startS, "装卸货取小")
		}

		switch roadLossState.Int64 {
		case 0:
			f.SetCellValue("Sheet1", "AD"+startS, "不亏吨(运输路耗=允许路耗)")
		case 1:
			f.SetCellValue("Sheet1", "AD"+startS, "亏吨但不超过允许亏吨数(运输路耗<允许路耗)")
		case 2:
			f.SetCellValue("Sheet1", "AD"+startS, "亏吨并且超过允许亏吨数（运输路耗>允许路耗）")
		case 3:
			f.SetCellValue("Sheet1", "AD"+startS, "涨吨(运输路耗<0)）")
		}

		f.SetCellValue("Sheet1", "AE"+startS, lossWeightOfGoods.Float64)
		f.SetCellValue("Sheet1", "AF"+startS, deductionOfLossExpenses.Float64)

		switch freightAccountingStatus.Int64 {
		case 0:
			f.SetCellValue("Sheet1", "AG"+startS, "无需核算")
		case 1:
			f.SetCellValue("Sheet1", "AG"+startS, "待核算")
		case 2:
			f.SetCellValue("Sheet1", "AG"+startS, "已核算")
		}

		f.SetCellValue("Sheet1", "AH"+startS, remarks.String)
		f.SetCellValue("Sheet1", "AI"+startS, serviceFeePaid.Float64)
		f.SetCellValue("Sheet1", "AJ"+startS, payTime.Time.Format("2006-01-02 15:04:05"))
		f.SetCellValue("Sheet1", "AK"+startS, oilCardOnline.Float64)
		f.SetCellValue("Sheet1", "AL"+startS, actualmileage.Float64)
		f.SetCellValue("Sheet1", "AM"+startS, freightPaid.Float64)
		f.SetCellValue("Sheet1", "AN"+startS, oilCardAmount.Float64)

		if plans[transportationNumber.String].PayPlanType == 2 {
			f.SetCellValue("Sheet1", "AO"+startS, plans[transportationNumber.String].PayPlanType)
		} else {
			f.SetCellValue("Sheet1", "AO"+startS, 0)
		}

		if _, ok := plans[transportationNumber.String]; ok {
			f.SetCellValue("Sheet1", "AP"+startS, plans[transportationNumber.String].InsuranceAmount)
		} else {
			f.SetCellValue("Sheet1", "AP"+startS, 0)
		}

		if _, ok := invoiceOpen[id]; ok {
			f.SetCellValue("Sheet1", "AQ"+startS, "已开票")
			f.SetCellValue("Sheet1", "AR"+startS, invoiceOpen[id].Format("2006-01-02 15:04:05"))
		} else {
			f.SetCellValue("Sheet1", "AQ"+startS, "未开票")
			f.SetCellValue("Sheet1", "AR"+startS, "")
		}

		if _, ok := insuranceData[transportationNumber.String]; ok {
			f.SetCellValue("Sheet1", "AS"+startS, insuranceData[transportationNumber.String]["insurance_company"])
			f.SetCellValue("Sheet1", "AT"+startS, insuranceData[transportationNumber.String]["insurance_time"])
			f.SetCellValue("Sheet1", "AU"+startS, insuranceData[transportationNumber.String]["thirdparty_insurance_amount"])
		} else {
			f.SetCellValue("Sheet1", "AS"+startS, "")
			f.SetCellValue("Sheet1", "AT"+startS, "")
			f.SetCellValue("Sheet1", "AU"+startS, 0)
		}

		if _, ok := plans[transportationNumber.String]; ok {
			f.SetCellValue("Sheet1", "AV"+startS, plans[transportationNumber.String].CommissionAmount)
		} else {
			f.SetCellValue("Sheet1", "AV"+startS, 0)
		}

		if etcFee.Valid && etcFee.Float64 > 0 {
			f.SetCellValue("Sheet1", "AW"+startS, etcFee.Float64/100)
		} else {
			f.SetCellValue("Sheet1", "AW"+startS, 0)
		}

		start++
	}

	if err := f.SaveAs("财务运单数据.xlsx"); err != nil {
		logger.Stdout.Error(err.Error())
	}

	e := &email.Email{
		To:      []string{"<EMAIL>"},
		Subject: time.Now().Format("********") + "财务运单数据",
		Attach:  "财务运单数据.xlsx",
	}

	if err := e.SendEmail(); err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	os.Remove("财务运单数据.xlsx")
	logger.Stdout.Info("财务运单数据邮件发送成功")
}

func FinanceInvoice() {
	companies := map[string]string{
		"赤峰现代智慧物流有限公司":      "a82dc78ba61880beda018fa5eab6c944",
		"赤峰市元宝山区兴元智慧物流有限公司": "a82dc78ba61880beda018fa5eab6c955",
	}

	emails := map[string][]string{
		"赤峰现代智慧物流有限公司":      {"<EMAIL>", "<EMAIL>"},
		"赤峰市元宝山区兴元智慧物流有限公司": {"<EMAIL>"},
	}

	for companyName, companyID := range companies {
		if _, ok := emails[companyName]; !ok {
			continue
		}
		if len(emails[companyName]) == 0 {
			continue
		}

		rows, err := model.DB.Table("tms_invoice_list").
			Select([]string{"invoice_title", "invoice_value", "invoice_apply_time", "quality_type",
				"transportation_ids"}).
			Where("invoice_state = ?", 0).
			Where("select_company_id = ?", companyID).
			Rows()
		if err != nil {
			logger.Stdout.Error(err.Error())
			return
		}

		total := 0
		for rows.Next() {
			var invoiceTitle string
			var invoiceValue float64
			var invoiceApplyTime time.Time
			var qualityType sql.NullInt64
			var transportationIds string
			if err := rows.Scan(&invoiceTitle,
				&invoiceValue,
				&invoiceApplyTime,
				&qualityType,
				&transportationIds); err != nil {
				logger.Stdout.Error(err.Error())
				continue
			}

			if invoiceTitle == "克什克腾旗新城热力有限责任公司林业棚户区供热分公司" {
				invoiceTitle = "新城热力林业棚户区供热分公司"
			}
			if invoiceTitle == "内蒙古磐石建材商贸有限公司乌兰察布市察哈尔右翼后旗建材分公司" {
				invoiceTitle = "磐石建材商贸有限公司建材分公司"
			}

			total += 1

			f, err := excelize.OpenFile("template/company-invoice.xlsx")
			if err != nil {
				defer func() {
					f.Close()
				}()
			}
			index := f.GetActiveSheetIndex()
			f.SetActiveSheet(index)

			f1, err := excelize.OpenFile("template/company-invoice-details.xlsx")
			if err != nil {
				defer func() {
					f1.Close()
				}()
			}
			index1 := f1.GetActiveSheetIndex()
			f1.SetActiveSheet(index1)

			transportRows, err := model.DB.Table("tms_transport_note AS a").
				Joins("JOIN tms_order_history AS b ON a.order_id = b.id AND a.order_version_no = b.version_no").
				Select([]string{"b.loading_name", "b.unload_name", "a.transportation_plate", "b.goods_type",
					"b.goods_name", "a.loading_number", "a.unload_number", "a.freight_paid", "a.service_fee_paid",
					"a.road_loss_settlement_method", "b.loading_address", "b.unload_address"}).
				Where("a.id IN (?)", strings.Split(transportationIds, ",")).
				Rows()
			if err != nil {
				logger.Stdout.Error(err.Error())
				continue
			}

			start := 4
			for transportRows.Next() {
				var loadingName string
				var unloadName string
				var transportationPlate string
				var goodsType string
				var goodsName string
				var loadingNumber float64
				var unloadNumber float64
				var freightPaid float64
				var serviceFeePaid float64
				var roadLossSettlementMethod sql.NullInt64
				var loadingAddress string
				var unloadAddress string
				if err := transportRows.Scan(&loadingName,
					&unloadName,
					&transportationPlate,
					&goodsType,
					&goodsName,
					&loadingNumber,
					&unloadNumber,
					&freightPaid,
					&serviceFeePaid,
					&roadLossSettlementMethod,
					&loadingAddress,
					&unloadAddress); err != nil {
					logger.Stdout.Error(err.Error())
					continue
				}

				if goodsName == "" {
					goodsName = goodsType
				}

				// 使用具体的装卸货地址
				if strings.Contains(invoiceTitle, "蒙东国际物流有限公司") ||
					strings.Contains(invoiceTitle, "准格尔旗万亨商贸有限责任公司") ||
					strings.Contains(invoiceTitle, "内蒙古融德能源有限责任公司") ||
					strings.Contains(invoiceTitle, "内蒙古呼鑫能源有限公司") {
					loadingName = loadingName + strings.ReplaceAll(loadingAddress, loadingName, "")
					unloadName = unloadName + strings.ReplaceAll(unloadAddress, unloadName, "")
				}

				f.SetCellValue("货物运输服务特定信息", "A"+strconv.Itoa(start), loadingName)
				f.SetCellValue("货物运输服务特定信息", "B"+strconv.Itoa(start), unloadName)
				f.SetCellValue("货物运输服务特定信息", "C"+strconv.Itoa(start), "公路运输")
				f.SetCellValue("货物运输服务特定信息", "D"+strconv.Itoa(start), transportationPlate)
				f.SetCellValue("货物运输服务特定信息", "E"+strconv.Itoa(start), goodsName)

				// 判断使用哪个重量作为开票重量
				unit := "吨"
				weight := 0.0
				if qualityType.Valid && qualityType.Int64 == 0 {
					weight = unloadNumber
				} else if qualityType.Valid && qualityType.Int64 == 1 {
					weight = loadingNumber
				} else if qualityType.Valid && qualityType.Int64 == 2 {
					if roadLossSettlementMethod.Valid && roadLossSettlementMethod.Int64 == 1 {
						weight = loadingNumber
					} else if roadLossSettlementMethod.Valid && roadLossSettlementMethod.Int64 == 2 {
						weight = unloadNumber
					} else if roadLossSettlementMethod.Valid && roadLossSettlementMethod.Int64 == 3 {
						weight = max(loadingNumber, unloadNumber)
					} else if roadLossSettlementMethod.Valid && roadLossSettlementMethod.Int64 == 4 {
						weight = min(loadingNumber, unloadNumber)
					} else {
						weight = min(loadingNumber, unloadNumber)
					}
				} else if qualityType.Valid && qualityType.Int64 == 3 {
					weight = 1
					unit = "次"
				}

				f1.SetCellValue("1-明细模板", "A"+strconv.Itoa(start), "运输服务费")
				f1.SetCellValue("1-明细模板", "D"+strconv.Itoa(start), unit)
				f1.SetCellValue("1-明细模板", "E"+strconv.Itoa(start), weight)
				f1.SetCellValue("1-明细模板", "G"+strconv.Itoa(start), freightPaid+serviceFeePaid)
				f1.SetCellValue("1-明细模板", "H"+strconv.Itoa(start), "0.09")

				start++
			}

			dir := fmt.Sprintf("运输发票数据/%s_%s", invoiceTitle, invoiceApplyTime.Format("********150405"))
			if _, err := os.Stat(dir); os.IsNotExist(err) {
				if err := os.MkdirAll(dir, 0755); err != nil {
					logger.Stdout.Error(err.Error())
				}
			}

			filename := fmt.Sprintf("%s/%s_%0.2f_%s.xlsx", dir, invoiceTitle, invoiceValue, invoiceApplyTime.Format("********150405"))
			if err := f.SaveAs(filename); err != nil {
				logger.Stdout.Error(err.Error())
				return
			}

			filename1 := fmt.Sprintf("%s/%s_%0.2f_%s-明细.xlsx", dir, invoiceTitle, invoiceValue, invoiceApplyTime.Format("********150405"))
			if err := f1.SaveAs(filename1); err != nil {
				logger.Stdout.Error(err.Error())
				return
			}
		}

		if total == 0 {
			logger.Stdout.Info(companyName + "没有需要发送的数据")
			continue
		}

		zipName := companyName + "-运输发票数据.zip"
		source := "运输发票数据"
		if err := toolbox.Zip(zipName, source); err != nil {
			logger.Stdout.Error(err.Error())
			return
		}

		e := &email.Email{
			To:      emails[companyName],
			Subject: time.Now().Format("********") + "运输发票数据",
			Attach:  zipName,
		}

		if err := e.SendEmail(); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}
		// 删除临时文件
		os.RemoveAll(source)
		os.RemoveAll(zipName)

		logger.Stdout.Info("财务运输发票数据邮件发送成功")
	}
}

// InvoiceBasicInformationT 1-发票基本信息
type InvoiceBasicInformationT struct {
	SerialNumber int
	InvoiceType  string
	BusinessType string
	IsIncludeTax string
	TaxCompany   string
	TaxNumber    string
	TaxReamrk    string
}

// InvoiceDetailsInformationT 2-发票明细信息
type InvoiceDetailsInformationT struct {
	SerialNumber   int
	BusinessName   string
	BusinessNumber string
	Unit           string
	Weight         float64
	Amount         float64
	TaxRate        string
}

// InvoiceBusinessInformationT 3-特定业务信息
type InvoiceBusinessInformationT struct {
	SerialNumber         int
	LoadingName          string
	UnloadName           string
	TransportType        string
	VehicleLicenseNumber string
	GoodsName            string
}

// InvoiceBatchT 批量发票信息
type InvoiceBatchT struct {
	BasicInformation    InvoiceBasicInformationT
	DetailsInformation  []InvoiceDetailsInformationT
	BusinessInformation []InvoiceBusinessInformationT
}

func FinanceInvoiceBatch() {
	companies := map[string]string{
		"赤峰现代智慧物流有限公司":      "a82dc78ba61880beda018fa5eab6c944",
		"赤峰市元宝山区兴元智慧物流有限公司": "a82dc78ba61880beda018fa5eab6c955",
	}

	emails := map[string][]string{
		"赤峰现代智慧物流有限公司":      {"<EMAIL>", "<EMAIL>"},
		"赤峰市元宝山区兴元智慧物流有限公司": {"<EMAIL>"},
	}

	// 需要详细装卸货地址的公司
	detailAddressCompanies := []string{"蒙东国际物流有限公司", "准格尔旗万亨商贸有限责任公司", "内蒙古融德能源有限责任公司", "内蒙古呼鑫能源有限公司", "内蒙古博越煤炭洗选有限责任公司", "内蒙古嘉多兴能源有限公司", "内蒙古宏欣能源有限公司", "内蒙古国翔能源有限公司"}

	// 需要发票明细的公司
	detailsInformationComapnies := []string{"赤峰中骐物流有限公司", "赤峰玖汇物流有限公司", "唐山万溢物流有限公司", "内蒙古天壹成信环保科技有限公司", "赤峰市乐广佰商贸有限公司", "唐山鸿北商贸有限公司", "达拉特旗李五兴供应链管理有限公司", "内蒙古光宏能源有限公司", "唐山市鼎石物流股份有限公司", "包头市金丰物流有限责任公司", "内蒙古政锐阳物流有限公司", "包头市鸿赫煤炭经销有限公司", "唐山市轩鸣物流有限公司", "赤峰同驰运输有限公司", "赤峰驰达运输有限公司", "内蒙古国开矿业有限公司", "内蒙古博越煤炭洗选有限责任公司", "内蒙古嘉多兴能源有限公司", "内蒙古宏欣能源有限公司", "内蒙古呼鑫能源有限公司", "内蒙古嘉多兴能源有限公司", "内蒙古博越煤炭洗选有限责任公司"}

	// 查询需要开票公司的公司信息
	regAccounts := make(map[string]string)
	taxRows, _ := model.DB.Table("tms_invoice_info").
		Select([]string{"invoice_title", "tax_reg_account"}).
		Where("is_delete = ?", 0).
		Rows()
	for taxRows.Next() {
		var invoiceTitle string
		var taxRegAccount string
		if err := taxRows.Scan(&invoiceTitle, &taxRegAccount); err != nil {
			logger.Stdout.Error(err.Error())
			return
		}
		regAccounts[invoiceTitle] = taxRegAccount
	}

	// 按照平台公司分别获取开票数据
	for companyName, companyID := range companies {
		if _, ok := emails[companyName]; !ok {
			continue
		}
		if len(emails[companyName]) == 0 {
			continue
		}

		var invoiceBatchData []InvoiceBatchT

		// 查询需要开票的数据
		rows, err := model.DB.Table("tms_invoice_list").
			Select([]string{"invoice_title", "invoice_value", "invoice_apply_time", "quality_type",
				"transportation_ids", "remark"}).
			Where("invoice_state = ?", 0).
			Where("select_company_id = ?", companyID).
			Rows()
		if err != nil {
			logger.Stdout.Error(err.Error())
			return
		}

		serialNumber := 1
		for rows.Next() {
			var invoiceTitle string
			var invoiceValue float64
			var invoiceApplyTime time.Time
			var qualityType sql.NullInt64
			var transportationIds string
			var remark sql.NullString
			if err := rows.Scan(&invoiceTitle,
				&invoiceValue,
				&invoiceApplyTime,
				&qualityType,
				&transportationIds,
				&remark); err != nil {
				logger.Stdout.Error(err.Error())
				continue
			}

			// 发票基本信息
			basicInformation := InvoiceBasicInformationT{
				SerialNumber: serialNumber,
				InvoiceType:  "增值税专用发票",
				BusinessType: "货物运输服务",
				IsIncludeTax: "是",
				TaxCompany:   invoiceTitle,
				TaxNumber:    regAccounts[invoiceTitle],
				TaxReamrk:    remark.String,
			}

			// 查询运单数据
			transportRows, err := model.DB.Table("tms_transport_note AS a").
				Joins("JOIN tms_order_history AS b ON a.order_id = b.id AND a.order_version_no = b.version_no").
				Select([]string{"b.loading_name", "b.unload_name", "a.transportation_plate", "b.goods_type",
					"b.goods_name", "a.loading_number", "a.unload_number", "a.freight_paid", "a.service_fee_paid",
					"b.radio_value", "a.road_loss_settlement_method", "b.loading_address", "b.unload_address", "b.goods_untis"}).
				Where("a.id IN (?)", strings.Split(transportationIds, ",")).
				Rows()
			if err != nil {
				logger.Stdout.Error(err.Error())
				continue
			}

			// 发票明细信息
			var detailsInformation []InvoiceDetailsInformationT

			// 发票业务信息
			var businessInformation []InvoiceBusinessInformationT
			for transportRows.Next() {
				var loadingName string
				var unloadName string
				var transportationPlate string
				var goodsType string
				var goodsName string
				var loadingNumber float64
				var unloadNumber float64
				var freightPaid float64
				var serviceFeePaid float64
				var radioValue int
				var roadLossSettlementMethod sql.NullInt64
				var loadingAddress string
				var unloadAddress string
				var goodsUnits string
				if err := transportRows.Scan(&loadingName,
					&unloadName,
					&transportationPlate,
					&goodsType,
					&goodsName,
					&loadingNumber,
					&unloadNumber,
					&freightPaid,
					&serviceFeePaid,
					&radioValue,
					&roadLossSettlementMethod,
					&loadingAddress,
					&unloadAddress,
					&goodsUnits); err != nil {
					logger.Stdout.Error(err.Error())
					continue
				}

				if goodsName == "" {
					goodsName = goodsType
				}

				// 判断使用哪个重量作为开票重量
				unit := goodsUnits
				weight := 0.0
				if qualityType.Valid && qualityType.Int64 == 0 {
					weight = unloadNumber
				} else if qualityType.Valid && qualityType.Int64 == 1 {
					weight = loadingNumber
				} else if qualityType.Valid && qualityType.Int64 == 2 {
					if roadLossSettlementMethod.Valid && roadLossSettlementMethod.Int64 == 1 {
						weight = loadingNumber
					} else if roadLossSettlementMethod.Valid && roadLossSettlementMethod.Int64 == 2 {
						weight = unloadNumber
					} else if roadLossSettlementMethod.Valid && roadLossSettlementMethod.Int64 == 3 {
						weight = max(loadingNumber, unloadNumber)
					} else if roadLossSettlementMethod.Valid && roadLossSettlementMethod.Int64 == 4 {
						weight = min(loadingNumber, unloadNumber)
					} else {
						weight = min(loadingNumber, unloadNumber)
					}
				} else if qualityType.Valid && qualityType.Int64 == 3 {
					weight = 1
					unit = "次"
				}

				// 使用具体装卸货地址

				if lo.Contains(detailAddressCompanies, invoiceTitle) {
					loadingName = loadingName + strings.ReplaceAll(loadingAddress, loadingName, "")
					unloadName = unloadName + strings.ReplaceAll(unloadAddress, unloadName, "")
				}

				businessInformation = append(businessInformation, InvoiceBusinessInformationT{
					SerialNumber:         serialNumber,
					LoadingName:          loadingName,
					UnloadName:           unloadName,
					TransportType:        "公路运输",
					VehicleLicenseNumber: transportationPlate,
					GoodsName:            goodsName,
				})

				if lo.Contains(detailsInformationComapnies, invoiceTitle) {
					detailsInformation = append(detailsInformation, InvoiceDetailsInformationT{
						SerialNumber:   serialNumber,
						BusinessName:   "运输服务费",
						BusinessNumber: "3010102020100000000",
						Unit:           unit,
						Weight:         weight,
						Amount:         freightPaid + serviceFeePaid,
						TaxRate:        "0.09",
					})
				} else {
					if len(detailsInformation) == 0 {
						detailsInformation = append(detailsInformation, InvoiceDetailsInformationT{
							SerialNumber:   serialNumber,
							BusinessName:   "运输服务费",
							BusinessNumber: "3010102020100000000",
							Unit:           unit,
							Weight:         weight,
							Amount:         freightPaid + serviceFeePaid,
							TaxRate:        "0.09",
						})
					} else {
						t := detailsInformation[len(detailsInformation)-1]
						t.Weight += weight
						t.Amount += freightPaid + serviceFeePaid
						detailsInformation[len(detailsInformation)-1] = t
					}
				}

			}

			// 发票批次信息
			invoiceBatchData = append(invoiceBatchData, InvoiceBatchT{
				BasicInformation:    basicInformation,
				DetailsInformation:  detailsInformation,
				BusinessInformation: businessInformation,
			})

			serialNumber++
		}
		if len(invoiceBatchData) == 0 {
			logger.Stdout.Info(companyName + "没有需要发送的数据")
			continue
		}

		// 打开模板
		f, err := excelize.OpenFile("template/batch-invoice.xlsx")
		if err != nil {
			defer func() {
				f.Close()
			}()
		}
		index := f.GetActiveSheetIndex()
		f.SetActiveSheet(index)

		// 填充数据
		start1 := 4
		start2 := 4
		start3 := 4

		reg := regexp.MustCompile(`方数[：:]\s*(\d+\.\d*)`)

		for _, v := range invoiceBatchData {
			f.SetCellValue("1-发票基本信息", fmt.Sprintf("A%d", start1), v.BasicInformation.SerialNumber)
			f.SetCellValue("1-发票基本信息", fmt.Sprintf("B%d", start1), v.BasicInformation.InvoiceType)
			f.SetCellValue("1-发票基本信息", fmt.Sprintf("C%d", start1), v.BasicInformation.BusinessType)
			f.SetCellValue("1-发票基本信息", fmt.Sprintf("D%d", start1), v.BasicInformation.IsIncludeTax)
			f.SetCellValue("1-发票基本信息", fmt.Sprintf("F%d", start1), v.BasicInformation.TaxCompany)
			f.SetCellValue("1-发票基本信息", fmt.Sprintf("G%d", start1), v.BasicInformation.TaxNumber)
			f.SetCellValue("1-发票基本信息", fmt.Sprintf("R%d", start1), v.BasicInformation.TaxReamrk)
			start1++

			for _, vv := range v.DetailsInformation {

				if v.BasicInformation.TaxCompany == "赤峰鑫海节能建材有限公司" {
					matches := reg.FindStringSubmatch(v.BasicInformation.TaxReamrk)
					if len(matches) > 1 {
						weight := matches[1]
						vv.Weight, _ = strconv.ParseFloat(weight, 64)
						vv.Unit = "方"
					}
				}

				f.SetCellValue("2-发票明细信息", fmt.Sprintf("A%d", start2), vv.SerialNumber)
				f.SetCellValue("2-发票明细信息", fmt.Sprintf("B%d", start2), vv.BusinessName)
				f.SetCellValue("2-发票明细信息", fmt.Sprintf("C%d", start2), vv.BusinessNumber)
				f.SetCellValue("2-发票明细信息", fmt.Sprintf("E%d", start2), vv.Unit)
				f.SetCellValue("2-发票明细信息", fmt.Sprintf("F%d", start2), vv.Weight)
				f.SetCellValue("2-发票明细信息", fmt.Sprintf("H%d", start2), vv.Amount)
				f.SetCellValue("2-发票明细信息", fmt.Sprintf("I%d", start2), vv.TaxRate)
				start2++
			}

			for _, vv := range v.BusinessInformation {
				f.SetCellValue("3-特定业务信息", fmt.Sprintf("A%d", start3), vv.SerialNumber)
				f.SetCellValue("3-特定业务信息", fmt.Sprintf("H%d", start3), vv.LoadingName)
				f.SetCellValue("3-特定业务信息", fmt.Sprintf("I%d", start3), vv.UnloadName)
				f.SetCellValue("3-特定业务信息", fmt.Sprintf("J%d", start3), vv.TransportType)
				f.SetCellValue("3-特定业务信息", fmt.Sprintf("K%d", start3), vv.VehicleLicenseNumber)
				f.SetCellValue("3-特定业务信息", fmt.Sprintf("L%d", start3), vv.GoodsName)
				start3++
			}
		}

		// 保存excel
		dir := "批量运输发票数据"
		if _, err := os.Stat(dir); os.IsNotExist(err) {
			if err := os.MkdirAll(dir, 0755); err != nil {
				logger.Stdout.Error(err.Error())
			}
		}
		filename := fmt.Sprintf("%s/%s.xlsx", dir, companyName+"-批量开票")
		if err := f.SaveAs(filename); err != nil {
			logger.Stdout.Error(err.Error())
			return
		}

		zipName := companyName + "-批量运输发票数据.zip"
		source := "批量运输发票数据"
		if err := toolbox.Zip(zipName, source); err != nil {
			logger.Stdout.Error(err.Error())
			return
		}

		e := &email.Email{
			To:      emails[companyName],
			Subject: time.Now().Format("********") + "批量运输发票数据",
			Attach:  zipName,
		}

		if err := e.SendEmail(); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}
		// 删除临时文件
		os.RemoveAll(source)
		os.RemoveAll(zipName)

		logger.Stdout.Info("财务批量运输发票数据邮件发送成功")
	}
}

func BankStatementReconcile() {
	//平台收支提取
	// 收 = 收到银行回执       查询平台流水记录表中的充值记录
	//支=所有货主付款总额       查询支付计划中的付款总额
	//银行提取 收=划拨资金池  查询银行流水中的划拨资金池
	// 支= 摘要付款6666       查询银行流水中的付款6666
	//已0点-次0点 为准
	startTime := time.Now()

	sysOrgCompanyID := "a82dc78ba61880beda018fa5eab6c944"
	sysOrgCode := "A03"

	baseTime := time.Now().Add(-24 * time.Hour)

	fmt.Println(baseTime.Format("2006-01-02"))

	// 查询货主子单元编号
	shipperDmanbrs := []string{}
	shipperIDs := []string{}
	subRows, err := model.DB.Table("sys_zhaoshang_account").
		Select([]string{"dmanbr", "user_id", "dmanam"}).
		Where("company_id = ?", sysOrgCompanyID).
		Where("sys_org_code = ?", sysOrgCode).
		Where("user_type = ?", 0).
		Where("is_delete = ?", 0).
		Rows()
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	for subRows.Next() {
		var dmanbr string
		var userID string
		var dmanam string
		if err := subRows.Scan(&dmanbr, &userID, &dmanam); err != nil {
			logger.Stdout.Error(err.Error())
			return
		}
		shipperDmanbrs = append(shipperDmanbrs, dmanbr)
		shipperIDs = append(shipperIDs, userID)
	}

	c := cmb.GetHszy()
	ctnkey := ""
	for {
		rr, err := c.SubAccountQueryRecord("", ctnkey, "********", "********")
		if err != nil {
			logger.Stdout.Error(err.Error())
			return
		}

		for _, v := range rr.Ntdmthlsz {

			if strings.Contains(v.Trxtxt, "CYBM2506201123157653") {
				fmt.Printf("%+v\n", v)
			}
		}

		if len(rr.Ntdmthlsy) == 0 {
			break
		}

		// fmt.Println(rr.Ntdmthlsy)
		ctnkey = rr.Ntdmthlsy[0].Ctnkey
		if ctnkey == "" {
			break
		}
	}
	os.Exit(1)

	/* for _, k := range shipperDmanbrs {
		rr, err := c.SubAccountQueryBalance(k)
		if err != nil {
			logger.Stdout.Error(err.Error())
			return
		}
		for _, vv := range rr.Ntdmalstz {
			actbal, _ := strconv.ParseFloat(vv.Actbal, 64)
			if actbal > 0 {
				fmt.Printf("%s %s %.2f\n", vv.Dmanbr, vv.Dmanam, actbal)
			}
		}
	} */
	// fmt.Println(c.SubAccountQueryBalance("**********"))

	/* bankIncomeTotal := 0.0
	bankPayTotal := 0.0

	f := excelize.NewFile()
	defer f.Close()
	cellIndex := 2

	// 循环查询银行流水
	ctnkey := ""
	for {
		rr, err := c.SubAccountQueryRecord("", ctnkey, baseTime.Format("********"), baseTime.Format("********"))
		if err != nil {
			logger.Stdout.Error(err.Error())
			return
		}

		for _, v := range rr.Ntdmthlsz {

			f.SetCellValue("Sheet1", fmt.Sprintf("A%d", cellIndex), v.Trxtxt)
			f.SetCellValue("Sheet1", fmt.Sprintf("B%d", cellIndex), v.Trxdir)
			f.SetCellValue("Sheet1", fmt.Sprintf("C%d", cellIndex), v.Trxamt)
			f.SetCellValue("Sheet1", fmt.Sprintf("D%d", cellIndex), v.Trxdat)
			f.SetCellValue("Sheet1", fmt.Sprintf("E%d", cellIndex), v.Trxtim)
			f.SetCellValue("Sheet1", fmt.Sprintf("F%d", cellIndex), v.Dmanbr)
			f.SetCellValue("Sheet1", fmt.Sprintf("G%d", cellIndex), v.Dmanam)
			f.SetCellValue("Sheet1", fmt.Sprintf("H%d", cellIndex), v.Rpyacc)
			f.SetCellValue("Sheet1", fmt.Sprintf("I%d", cellIndex), v.Rpynam)

			cellIndex++

			// 只计算资金池往中转户划账的流水
			// 限制条件 付款6666 D 货主往中转户划账
			if strings.Contains(v.Trxtxt, "付款6666") && strings.ToUpper(v.Trxdir) == "D" &&
				lo.Contains(shipperDmanbrs, v.Dmanbr) {
				amount, _ := strconv.ParseFloat(strings.TrimLeft(v.Trxamt, "-"), 64)
				bankPayTotal += amount
			}

			if strings.Contains(v.Trxtxt, "划拨资金池") && strings.ToUpper(v.Trxdir) == "D" &&
				lo.Contains(shipperDmanbrs, v.Dmanbr) {
				amount, _ := strconv.ParseFloat(strings.TrimLeft(v.Trxamt, "-"), 64)
				bankIncomeTotal += amount
			}
		}

		if len(rr.Ntdmthlsy) == 0 {
			break
		}

		fmt.Println(rr.Ntdmthlsy)
		ctnkey = rr.Ntdmthlsy[0].Ctnkey
		if ctnkey == "" {
			break
		}
	}

	f.SaveAs("test.xlsx")

	fmt.Printf("银行收入总额:%.2f\n", bankIncomeTotal)
	fmt.Printf("银行支出总额:%.2f\n", bankPayTotal) */

	// 查询平台收支
	var platformIncomeRecords []model.TmsExpenseRecord
	if err := model.DB.Select([]string{"transaction_amount"}).
		Where("account_id IN (?)", shipperIDs).
		Where("account_type = ?", 1).       // 账户类型：货主
		Where("reasons_for_change = ?", 0). // 充值
		Where("sys_org_code = ?", sysOrgCode).
		Where("is_delete = ?", 0).
		Where("consumption_time BETWEEN ? AND ?", baseTime.Format("2006-01-02")+" 00:00:00", baseTime.Format("2006-01-02")+" 23:59:59").
		Find(&platformIncomeRecords).Error; err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	platformIncomeTotal := 0.0
	for _, record := range platformIncomeRecords {
		platformIncomeTotal += record.TransactionAmount
	}

	var platformPayRecords []model.TmsExpenseRecord
	if err := model.DB.Select([]string{"transaction_amount"}).
		Where("account_id IN (?)", shipperIDs).
		Where("account_type = ?", 1).        // 账户类型：货主
		Where("reasons_for_change = ?", 99). // 支出
		Where("sys_org_code = ?", sysOrgCode).
		Where("is_delete = ?", 0).
		Where("consumption_time BETWEEN ? AND ?", baseTime.Format("2006-01-02")+" 00:00:00", baseTime.Format("2006-01-02")+" 23:59:59").
		Find(&platformPayRecords).Error; err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	platformPayTotal := 0.0
	for _, plan := range platformPayRecords {
		platformPayTotal += plan.TransactionAmount
	}

	fmt.Printf("平台收入总额:%.2f\n", platformIncomeTotal)
	fmt.Printf("平台支出总额:%.2f\n", platformPayTotal)

	fmt.Printf("执行完毕,耗时:%v\n", time.Since(startTime))
}
