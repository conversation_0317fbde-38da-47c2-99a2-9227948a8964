package tasks

import (
	"database/sql"
	"fmt"

	"os"
	"strconv"
	"strings"
	"time"
	"wlhy/model"
	"wlhy/toolbox/email"
	"wlhy/toolbox/logger"

	"github.com/xuri/excelize/v2"
)

func RongTong() {
	yestoday := time.Now().Add(-24 * time.Hour).Format("2006-01-02")
	rows, err := model.DB.Table("tms_transport_note AS a").
		Joins("JOIN tms_order_history AS b ON a.order_id = b.id AND a.order_version_no = b.version_no").
		Joins("JOIN tms_shipper AS c ON b.create_company_id = c.shipper_id").
		Select([]string{
			"b.goods_number",
			"a.transportation_number",
			"a.waybill_status",
			"b.radio_value",
			"a.univalent_should",
			"b.line_name",
			"b.loading_name",
			"b.loading_address",
			"a.loading_time",
			"a.loading_number",
			"b.unload_name",
			"b.unload_address",
			"a.unload_time",
			"a.unload_number",
			"a.transportation_driver",
			"a.transportation_phone",
			"a.transportation_plate",
			"c.company_name",
			"b.goods_value",
			"b.owner_unit_price",
		}).
		Where("a.is_delete = ?", 0).
		Where("c.shipper_type = ? AND c.is_delete = ?", 1, 0).
		Where("b.create_company_id IN ?", []string{"1744532557608534017", "1723656779815837698", "1729396777802285057",
			"1745240177369677826", "1744680320723963906", "1747510707028312065", "1747189125848285185",
			"1753017361937219586", "1742387049121198082", "1744883938181775361", "1744876191457476610",
			"1865942971241463810", "1874628008172998657", "1828711719948931073", "1875747505744683010",
			"1875750556356980737", "1875752795708469250", "1875754097553321986", "1874628008172998657",
			"1878702164355276802"}).
		Where("a.loading_time BETWEEN ? AND ?", yestoday+" 00:00:00", yestoday+" 23:59:59").
		Rows()
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	f := excelize.NewFile()
	defer func() {
		if err := f.Close(); err != nil {
			logger.Stdout.Error(err.Error())
		}
	}()

	// Create a new sheet.
	index, err := f.NewSheet("Sheet1")
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	// Set value of a cell.
	f.SetCellValue("Sheet1", "A1", "货单号")
	f.SetCellValue("Sheet1", "B1", "线路名称")
	f.SetCellValue("Sheet1", "C1", "运单号")
	f.SetCellValue("Sheet1", "D1", "装货重量")
	f.SetCellValue("Sheet1", "E1", "卸货重量")
	f.SetCellValue("Sheet1", "F1", "运输司机姓名")
	f.SetCellValue("Sheet1", "G1", "运输司机电话")
	f.SetCellValue("Sheet1", "H1", "运输车牌号")
	f.SetCellValue("Sheet1", "I1", "装货时间")
	f.SetCellValue("Sheet1", "J1", "装货地址")
	f.SetCellValue("Sheet1", "K1", "卸货时间")
	f.SetCellValue("Sheet1", "L1", "卸货地址")
	f.SetCellValue("Sheet1", "M1", "司机单价")
	f.SetCellValue("Sheet1", "N1", "货主单价")
	f.SetCellValue("Sheet1", "O1", "货物价值")

	f.SetActiveSheet(index)

	start := 2

	for rows.Next() {
		var goodsNumber sql.NullString
		var transportationNumber sql.NullString
		var waybillStatus sql.NullInt64
		var radioValue sql.NullInt64
		var univalentShould sql.NullFloat64
		var lineName sql.NullString
		var loadingName sql.NullString
		var loadingAddress sql.NullString
		var loadingTime sql.NullTime
		var loadingNumber sql.NullFloat64
		var unloadName sql.NullString
		var unloadAddress sql.NullString
		var unloadTime sql.NullTime
		var unloadNumber sql.NullFloat64
		var transportationDriver sql.NullString
		var transportationPhone sql.NullString
		var transportationPlate sql.NullString
		var companyName sql.NullString
		var goodsValue sql.NullFloat64
		var ownerUnitPrice sql.NullFloat64

		if err := rows.Scan(
			&goodsNumber,
			&transportationNumber,
			&waybillStatus,
			&radioValue,
			&univalentShould,
			&lineName,
			&loadingName,
			&loadingAddress,
			&loadingTime,
			&loadingNumber,
			&unloadName,
			&unloadAddress,
			&unloadTime,
			&unloadNumber,
			&transportationDriver,
			&transportationPhone,
			&transportationPlate,
			&companyName,
			&goodsValue,
			&ownerUnitPrice,
		); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}

		// fmt.Println(freightGross, serviceCharge, loadingNumber, companyName, loadingTime, unloadTime)
		startS := strconv.Itoa(start)

		f.SetCellValue("Sheet1", "A"+startS, goodsNumber.String)                             // 货单号
		f.SetCellValue("Sheet1", "B"+startS, lineName.String)                                // 线路名称
		f.SetCellValue("Sheet1", "C"+startS, transportationNumber.String)                    // 运单号
		f.SetCellValue("Sheet1", "D"+startS, loadingNumber.Float64)                          // 装货重量
		f.SetCellValue("Sheet1", "E"+startS, unloadNumber.Float64)                           // 卸货重量
		f.SetCellValue("Sheet1", "F"+startS, transportationDriver.String)                    // 司机
		f.SetCellValue("Sheet1", "G"+startS, transportationPhone.String)                     // 司机电话
		f.SetCellValue("Sheet1", "H"+startS, transportationPlate.String)                     // 车牌号
		f.SetCellValue("Sheet1", "I"+startS, loadingTime.Time.Format("2006-01-02 15:04:05")) // 装货时间
		if strings.Contains(loadingAddress.String, loadingName.String) {
			loadingAddress.String = strings.ReplaceAll(loadingAddress.String, loadingName.String, "")
		}
		f.SetCellValue("Sheet1", "J"+startS, loadingName.String+loadingAddress.String) // 装货地

		f.SetCellValue("Sheet1", "K"+startS, unloadTime.Time.Format("2006-01-02 15:04:05")) // 卸货时间
		if strings.Contains(unloadAddress.String, unloadName.String) {
			unloadAddress.String = strings.ReplaceAll(unloadAddress.String, unloadName.String, "")
		}
		f.SetCellValue("Sheet1", "L"+startS, unloadName.String+unloadAddress.String) // 卸货地

		f.SetCellValue("Sheet1", "M"+startS, univalentShould.Float64)                      // 司机单价
		f.SetCellValue("Sheet1", "N"+startS, fmt.Sprintf("%0.2f", ownerUnitPrice.Float64)) // 货主单价
		f.SetCellValue("Sheet1", "O"+startS, goodsValue.Float64)                           // 货物价值
		f.SetCellValue("Sheet1", "P"+startS, companyName.String)                           // 公司名称

		start++
	}

	if err := f.SaveAs("运单数据.xlsx"); err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	e := &email.Email{
		To:      []string{"<EMAIL>", "<EMAIL>", "<EMAIL>"},
		Subject: time.Now().Format("20060102") + "运单数据",
		Attach:  "运单数据.xlsx",
	}

	if err := e.SendEmail(); err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	os.Remove("运单数据.xlsx")

	logger.Stdout.Info("融通邮件发送成功")
}

func RongTongException() {
	type NoteT struct {
		CompanyName          string
		GoodsNumber          string
		TransportationNumber string
		ExceptionType        int
		ExceptionStatus      int
		TransportationPhone  string
		TransportationDriver string
		TransportationPlate  string
		ExceptionTime        time.Time
	}

	var notes []NoteT

	start := "2025-01-01 00:00:00"
	end := "2025-02-01 00:00:00"

	model.DB.Table("tms_transport_risk_exception AS a").
		Joins("JOIN tms_transport_note AS b ON a.exception_id = b.id").
		Joins("JOIN tms_order_history AS c ON b.order_id = c.id AND b.order_version_no = c.version_no").
		Joins("JOIN tms_shipper AS d ON b.create_company_id = d.company_id").
		Select([]string{"d.company_name", "c.goods_number", "b.transportation_number", "a.exception_type",
			"a.status AS exception_status", "b.transportation_phone", "b.transportation_driver", "b.transportation_plate", "a.create_time AS exception_time"}).
		Where("a.exception_type IN (?)", []int{1, 2}).
		Where("a.create_time BETWEEN ? AND ?", start, end).
		Where("d.shipper_type = ? AND d.is_delete = ?", 1, 0).
		Where("b.waybill_status NOT IN ? ", []int{6, 7}).
		Where("d.company_id IN (?)", []string{"1723656779815837698", "1742387049121198082", "1744532557608534017", "1744876191457476610", "1745240177369677826", "1747189125848285185", "1747510707028312065", "1828711719948931073", "1865942971241463810", "1874628008172998657", "1875747505744683010", "1875750556356980737", "1875752795708469250", "1875754097553321986", "1878710749042794498", "1878702164355276802"}).
		Group("b.transportation_number").
		Find(&notes)

	f := excelize.NewFile()
	defer f.Close()

	f.SetCellValue("Sheet1", "A1", "公司名称")
	f.SetCellValue("Sheet1", "B1", "货单号")
	f.SetCellValue("Sheet1", "C1", "运单号")
	f.SetCellValue("Sheet1", "D1", "异常类型")
	f.SetCellValue("Sheet1", "E1", "异常状态")
	f.SetCellValue("Sheet1", "F1", "进入异常时间")
	f.SetCellValue("Sheet1", "G1", "司机电话")
	f.SetCellValue("Sheet1", "H1", "司机姓名")
	f.SetCellValue("Sheet1", "I1", "车牌号")

	exceptionTepes := map[int]string{
		1: "轨迹异常",
		2: "装卸货地址异常",
	}

	exceptionStatuses := map[int]string{
		0: "待处理",
		1: "已处理",
	}

	for k, note := range notes {
		f.SetCellValue("Sheet1", fmt.Sprintf("A%d", k+2), note.CompanyName)
		f.SetCellValue("Sheet1", fmt.Sprintf("B%d", k+2), note.GoodsNumber)
		f.SetCellValue("Sheet1", fmt.Sprintf("C%d", k+2), note.TransportationNumber)
		f.SetCellValue("Sheet1", fmt.Sprintf("D%d", k+2), exceptionTepes[note.ExceptionType])
		f.SetCellValue("Sheet1", fmt.Sprintf("E%d", k+2), exceptionStatuses[note.ExceptionStatus])
		f.SetCellValue("Sheet1", fmt.Sprintf("F%d", k+2), note.ExceptionTime.Format("2006-01-02 15:04:05"))
		f.SetCellValue("Sheet1", fmt.Sprintf("G%d", k+2), note.TransportationPhone)
		f.SetCellValue("Sheet1", fmt.Sprintf("H%d", k+2), note.TransportationDriver)
		f.SetCellValue("Sheet1", fmt.Sprintf("I%d", k+2), note.TransportationPlate)
	}

	if err := f.SaveAs("异常运单列表.xlsx"); err != nil {
		fmt.Println(err)
	}
}
