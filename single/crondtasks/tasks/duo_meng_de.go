package tasks

import (
	"fmt"
	"os"
	"strconv"
	"time"
	"wlhy/model"
	"wlhy/toolbox"
	"wlhy/toolbox/logger"

	"github.com/google/uuid"
	"github.com/samber/lo"
	"github.com/xuri/excelize/v2"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

func DuoMengDeOil() {
	// 校验货主余额
	var preShipperBalance model.TmsShipperBalance
	if err := model.DB.
		Where("shipper_id = ?", "1938875864915189761").
		First(&preShipperBalance).Error; err != nil && err != gorm.ErrRecordNotFound {
		logger.Stdout.Error(err.Error())
		return
	}
	if preShipperBalance.ID == "" {
		logger.Stdout.Error("货主余额记录不存在")
		return
	}
	// 余额不足1000元预警通知，并停止派油
	if preShipperBalance.AccountBalanceLocal < 5000 {
		lark.SendTextMessage(lark.ReceivesID.WangBingXin, "内蒙古多蒙德冶金化工集团有限公司余额不足，无法继续派油，请提醒客户充值")
		logger.Stdout.Error("货主余额不足")
		return
	}

	// 创建记录文件
	f, err := os.OpenFile("logs/duomengdeoil.log", os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0655)
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	defer f.Close()

	// 油卡金额与货单的关联关系
	oilAmount := map[string]float64{
		"TYBM2506291022446001": 1000,
		"TYBM2506291019475752": 1000,
		"TYBM2506291018579380": 1000,
		"TYBM2506291013378270": 1000,
		"TYBM2506291013012643": 1000,
		"TYBM2506291009171511": 1000,
		"TYBM2506291006250221": 1000,
		"TYBM2506291006080263": 500,
	}
	goodsNumber := []string{}
	for k := range oilAmount {
		goodsNumber = append(goodsNumber, k)
	}

	type NoteT struct {
		GoodsNumber          string `gorm:"column:goods_number"`
		ShipperID            string `gorm:"column:shipper_id"`
		ShipperName          string `gorm:"column:shipper_name"`
		ShipperPhone         string `gorm:"column:shipper_phone"`
		ShipperCompanyName   string `gorm:"column:shipper_company_name"`
		ShipperDmanbr        string `gorm:"column:shipper_dmanbr"`
		DriverID             string `gorm:"column:driver_id"`
		DriverName           string `gorm:"column:driver_name"`
		DriverPhone          string `gorm:"column:driver_phone"`
		DriverDmanbr         string `gorm:"column:driver_dmanbr"`
		TransportationID     string `gorm:"column:transportation_id"`
		TransportationNumber string `gorm:"column:transportation_number"`
		TransportationPlate  string `gorm:"column:transportation_plate"`
		PlatformCompanyID    string `gorm:"column:appoint_company_id"`
		PlatformCompanyName  string `gorm:"column:appoint_company_name"`
	}

	var notes []NoteT
	if err := model.DB.Table("tms_transport_note AS a").
		Joins("JOIN tms_order_history AS b ON a.order_id = b.id AND a.order_version_no = b.version_no").
		Joins("JOIN tms_shipper AS c ON a.create_company_id = c.company_id").
		Joins("JOIN tms_driver AS d ON a.payee_id = d.driver_id").
		Joins("JOIN tms_shipper_balance AS e on c.shipper_id = e.shipper_id").
		Select([]string{"b.goods_number", "c.shipper_id", "c.shipper_name", "c.shipper_phone",
			"c.company_name AS shipper_company_name", "d.driver_id", "d.driver_name", "d.phone AS driver_phone",
			"a.id AS transportation_id", "a.transportation_number", "a.transportation_plate", "b.appoint_company_id", "b.appoint_company_name"}).
		Where("b.goods_number IN ?", goodsNumber).
		Where("a.waybill_status = ? AND a.is_delete = ?", 9, 0).
		Where("c.shipper_type = ? AND c.is_delete = ?", 1, 0).
		Where("d.is_delete = ?", 0).
		Scan(&notes).Error; err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	if len(notes) == 0 {
		return
	}

	// 查询子单元
	usersID := []string{}
	for _, note := range notes {
		usersID = append(usersID, note.DriverID)
		usersID = append(usersID, note.ShipperID)
	}
	usersID = lo.Uniq(usersID)
	dmanbrs := make(map[string]string)
	dmanbrRows, err := model.DB.Table("sys_zhaoshang_account").
		Select([]string{"dmanbr", "company_id", "user_id"}).
		Where("is_delete = ?", 0).
		Where("user_id IN (?)", usersID).
		Rows()
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	defer dmanbrRows.Close()

	for dmanbrRows.Next() {
		var dmanbr string
		var companyID string
		var userID string
		if err := dmanbrRows.Scan(&dmanbr, &companyID, &userID); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}
		dmanbrs[companyID+userID] = dmanbr
	}

	for _, note := range notes {
		var plan model.TmsTransportPayPlan
		if err := model.DB.
			Where("waybill_No = ?", note.TransportationNumber).
			Where("is_delete = ?", 0).First(&plan).Error; err != nil && err != gorm.ErrRecordNotFound {
			logger.Stdout.Error(err.Error())
			return
		}
		if plan.ID != "" {
			continue
		}

		isReturn := false
		err := model.DB.Transaction(func(tx *gorm.DB) error {
			// 查询货主余额
			var shipperBalance model.TmsShipperBalance
			if err := tx.Clauses(clause.Locking{Strength: "UPDATE"}).
				Where("shipper_id = ?", note.ShipperID).
				First(&shipperBalance).Error; err != nil {
				return err
			}

			// 余额不足1000元预警通知，并停止派油
			if shipperBalance.AccountBalanceLocal < 5000 {
				lark.SendTextMessage(lark.ReceivesID.WangBingXin, "内蒙古多蒙德冶金化工集团有限公司余额不足，无法继续派油，请提醒客户充值")
				logger.Stdout.Error("货主余额不足")
				isReturn = true
				return nil
			}

			fmt.Fprintf(f, "%s %s %s 查询货主余额 当前余额 %f\n", time.Now().Format("2006-01-02 15:04:05"), note.TransportationNumber, note.DriverName, shipperBalance.AccountBalanceLocal)

			// 检查是否存在油卡，不存在则新建油卡 wlhy.tms_driver_company_oil
			var driverCompanyOil model.TmsDriverCompanyOil
			if err := tx.
				Where("driver_id = ? AND company_id = ?", note.DriverID, note.PlatformCompanyID).
				First(&driverCompanyOil).Error; err != nil {
				if err == gorm.ErrRecordNotFound {
					// 新建油卡
					if err := tx.Create(&model.TmsDriverCompanyOil{
						ID:         toolbox.HszyPrimaryID(),
						CreateTime: time.Now(),
						CreateBy:   note.DriverID,
						UpdateBy:   note.DriverID,
						UpdateTime: time.Now(),
						IsDelete:   0,
						DriverID:   note.DriverID,
						CompanyID:  note.PlatformCompanyID,
						OilBalance: 0,
					}).Error; err != nil {
						return err
					}

					// 再次查询
					if err := tx.
						Where("driver_id = ? AND company_id = ?", note.DriverID, note.PlatformCompanyID).
						First(&driverCompanyOil).Error; err != nil {
						return err
					}

					fmt.Fprintf(f, "%s %s %s 新建油卡\n", time.Now().Format("2006-01-02 15:04:05"), note.TransportationNumber, note.DriverName)
				} else {
					return err
				}
			}

			fmt.Fprintf(f, "%s %s %s 当前油卡余额%f\n", time.Now().Format("2006-01-02 15:04:05"), note.TransportationNumber, note.DriverName, driverCompanyOil.OilBalance)

			// 写入支付计划数据 类型为预付 状态为已完成 wlhy.tms_transport_pay_plan
			if err := tx.Create(&model.TmsTransportPayPlan{
				ID:                         toolbox.HszyPrimaryID(),
				Type:                       2,
				PayType:                    0,
				State:                      2,
				Reason:                     "",
				TotalAmount:                oilAmount[note.GoodsNumber],
				ShipperState:               2,
				CashPoolState:              2,
				OpState:                    2,
				TransportAmount:            oilAmount[note.GoodsNumber],
				TransportState:             2,
				ImmediatelyAmount:          0,
				ImmediatelyState:           2,
				ImmediatelyCostAmount:      0,
				ImmediatelyCostState:       2,
				ImmediatelyExceptionAmount: 0,
				ImmediatelyExceptionState:  2,
				ServiceRate:                99,
				ServiceAmount:              0,
				ServiceState:               2,
				InsuranceState:             2,
				CommissionAmount:           0,
				CommissionState:            2,
				IsUseOil:                   0,
				OilAmount:                  0,
				PlatformProfit:             0,
				PlatformProfitState:        2,
				ShipperProfit:              0,
				ShipperProfitState:         2,
				ThirdpartyProfit:           0,
				ThirdpartyProfitState:      2,
				CostProfit:                 0,
				CostProfitState:            2,
				TransportationID:           note.TransportationID,
				WaybillNo:                  note.TransportationNumber,
				ShipperID:                  note.ShipperID,
				CompanyNo:                  note.PlatformCompanyID,
				IsOneself:                  1,
				ShipperPayMem:              dmanbrs[note.PlatformCompanyID+note.ShipperID],
				ReceiveMem:                 dmanbrs[note.PlatformCompanyID+note.DriverID],
				WithdrawalStatus:           2,
				CreateBy:                   note.ShipperID,
				CreateTime:                 time.Now(),
				IsDelete:                   0,
				ShipperPayTime:             time.Now(),
				ShipperAccBalance:          shipperBalance.AccountBalanceLocal - oilAmount[note.GoodsNumber],
				UpdateTime:                 time.Now(),
			}).Error; err != nil {
				return err
			}

			fmt.Fprintf(f, "%s %s %s 写入支付计划\n", time.Now().Format("2006-01-02 15:04:05"), note.TransportationNumber, note.DriverName)

			// 更新运单状态 wlhy.tms_transport_note
			if err := tx.Table("tms_transport_note").Where("id = ?", note.TransportationID).Updates(map[string]any{
				"freight_paid":     oilAmount[note.GoodsNumber],
				"service_fee_paid": 0,
			}).Error; err != nil {
				return err
			}

			payBatchNumber := uuid.New().String()

			// 写入货主资金流水记录 资金池流出 wlhy.tms_expense_record
			if err := tx.Create(&model.TmsExpenseRecord{
				SerialNumber:               toolbox.HszyPrimaryID(),
				PayBatchNumber:             payBatchNumber,
				WaybillID:                  note.TransportationID,
				WaybillNumber:              note.TransportationNumber,
				SerialNumberType:           0,
				ReasonsForChange:           99,
				TransactionAmount:          oilAmount[note.GoodsNumber],
				AccountBalance:             0,
				RevenueAndExpenditureTypes: 1,
				AccountType:                1,
				AccountName:                note.PlatformCompanyName,
				AccountID:                  note.PlatformCompanyID,
				DriverInformation:          note.DriverName + note.TransportationPlate,
				CreateTime:                 time.Now(),
				ConsumptionTime:            time.Now(),
				TransactionTime:            time.Now(),
				TradeStatus:                1,
				IsDelete:                   0,
				SysOrgCode:                 "A03",
				IsCapitalReporting:         1,
				CapitalReportingTime:       time.Now(),
				UpdateTime:                 time.Now(),
				VerifiedTime:               time.Now(),
			}).Error; err != nil {
				return err
			}

			fmt.Fprintf(f, "%s %s %s 写入预付油卡资金池流出资金流水记录\n", time.Now().Format("2006-01-02 15:04:05"), note.TransportationNumber, note.DriverName)

			// 货主支出流水 wlhy.tms_expense_record
			if err := tx.Create(&model.TmsExpenseRecord{
				SerialNumber:               toolbox.HszyPrimaryID(),
				PayBatchNumber:             payBatchNumber,
				WaybillID:                  note.TransportationID,
				WaybillNumber:              note.TransportationNumber,
				SerialNumberType:           0,
				ReasonsForChange:           1,
				TransactionAmount:          oilAmount[note.GoodsNumber],
				AccountBalance:             shipperBalance.AccountBalanceLocal - oilAmount[note.GoodsNumber],
				RevenueAndExpenditureTypes: 1,
				AccountType:                1,
				AccountName:                note.ShipperName,
				AccountNumber:              note.ShipperPhone,
				AccountID:                  note.ShipperID,
				DriverInformation:          note.DriverName + note.TransportationPlate,
				Remark:                     "预付电子油卡",
				CreateTime:                 time.Now(),
				ConsumptionTime:            time.Now(),
				TransactionTime:            time.Now(),
				TradeStatus:                1,
				IsDelete:                   0,
				SysOrgCode:                 "A03",
				IsCapitalReporting:         1,
				CapitalReportingTime:       time.Now(),
				UpdateTime:                 time.Now(),
				VerifiedTime:               time.Now(),
			}).Error; err != nil {
				return err
			}

			fmt.Fprintf(f, "%s %s %s 写入预付油卡货主流出资金流水记录\n", time.Now().Format("2006-01-02 15:04:05"), note.TransportationNumber, note.DriverName)

			// 写入司机资金流水记录 收入 类型为油卡 wlhy.tms_expense_record
			if err := tx.Create(&model.TmsExpenseRecord{
				SerialNumber:               toolbox.HszyPrimaryID(),
				PayBatchNumber:             payBatchNumber,
				WaybillID:                  note.TransportationID,
				WaybillNumber:              note.TransportationNumber,
				SerialNumberType:           0,
				ReasonsForChange:           31,
				TransactionAmount:          oilAmount[note.GoodsNumber],
				AccountBalance:             driverCompanyOil.OilBalance + oilAmount[note.GoodsNumber],
				RevenueAndExpenditureTypes: 0,
				AccountType:                2,
				AccountName:                note.DriverName,
				AccountNumber:              note.DriverPhone,
				AccountID:                  note.DriverID,
				DriverInformation:          note.DriverName + note.TransportationPlate,
				Remark:                     "预付电子油卡",
				CreateTime:                 time.Now(),
				ConsumptionTime:            time.Now(),
				TransactionTime:            time.Now(),
				TradeStatus:                1,
				IsDelete:                   0,
				SysOrgCode:                 "A03",
				IsCapitalReporting:         1,
				CapitalReportingTime:       time.Now(),
				UpdateTime:                 time.Now(),
				VerifiedTime:               time.Now(),
			}).Error; err != nil {
				return err
			}

			fmt.Fprintf(f, "%s %s %s 写入预付油卡司机收入资金流水记录\n", time.Now().Format("2006-01-02 15:04:05"), note.TransportationNumber, note.DriverName)

			// 写入油卡流水记录，增加余额 wlhy.tms_driver_oil_write_off_record wlhy.tms_driver_company_oil
			if err := tx.Create(&model.TmsDriverOilWriteOffRecord{
				ID:                   toolbox.HszyPrimaryID(),
				CreateBy:             note.DriverID,
				CreateTime:           time.Now(),
				IsDelete:             0,
				DriverID:             note.DriverID,
				DriverName:           note.DriverName,
				VehicleLicenseNumber: note.TransportationPlate,
				CompanyID:            note.PlatformCompanyID,
				TotalPrice:           oilAmount[note.GoodsNumber],
				WriteOffTime:         time.Now(),
				Desc:                 "预付电子油卡",
				CurrentBalance:       driverCompanyOil.OilBalance + oilAmount[note.GoodsNumber],
				TransportationNumber: note.TransportationNumber,
				RecordType:           1,
				DriverType:           1,
				UpdateTime:           time.Now(),
			}).Error; err != nil {
				return err
			}

			fmt.Fprintf(f, "%s %s %s 写入油卡流水记录，增加余额\n", time.Now().Format("2006-01-02 15:04:05"), note.TransportationNumber, note.DriverName)

			// 增加油卡余额
			if err := tx.Model(&model.TmsDriverCompanyOil{}).
				Where("id = ?", driverCompanyOil.ID).
				Updates(map[string]any{
					"update_by":   note.DriverID,
					"update_time": time.Now(),
					"oil_balance": driverCompanyOil.OilBalance + oilAmount[note.GoodsNumber],
				}).Error; err != nil {
				return err
			}

			fmt.Fprintf(f, "%s %s %s 增加油卡余额 当前余额 %f\n", time.Now().Format("2006-01-02 15:04:05"), note.TransportationNumber, note.DriverName, driverCompanyOil.OilBalance+oilAmount[note.GoodsNumber])

			// 扣减货主余额 wlhy.tms_shipper_balance
			if err := tx.Table("tms_shipper_balance").
				Where("shipper_id = ?", note.ShipperID).
				Updates(map[string]any{
					"version":               shipperBalance.Version + 1,
					"update_time":           time.Now(),
					"account_balance_local": shipperBalance.AccountBalanceLocal - oilAmount[note.GoodsNumber],
				}).Error; err != nil {
				return err
			}

			fmt.Fprintf(f, "%s %s %s 扣减货主余额 当前余额 %f\n", time.Now().Format("2006-01-02 15:04:05"), note.TransportationNumber, note.DriverName, shipperBalance.AccountBalanceLocal-oilAmount[note.GoodsNumber])

			return nil
		})
		if err != nil {
			fmt.Fprintf(f, "%s %s %s 事务执行失败:%+v\n", time.Now().Format("2006-01-02 15:04:05"), note.TransportationNumber, note.DriverName, err)
		}
		if isReturn {
			return
		}
	}

	// 发送统计数据
	if time.Now().Hour() == 9 && time.Now().Minute() == 30 {
		startTime := time.Now().Add(-24 * time.Hour)
		endTime := time.Now()

		rows, err := model.DB.Table("tms_transport_note AS a").
			Joins("JOIN tms_order_history AS b ON a.order_id = b.id AND a.order_version_no = b.version_no").
			Joins("JOIN tms_transport_pay_plan AS c ON a.transportation_number = c.waybill_No").
			Select([]string{"a.transportation_number", "c.total_Amount", "c.create_time", "a.transportation_plate"}).
			Where("b.goods_number IN (?)", goodsNumber).
			Where("c.create_time BETWEEN ? AND ?", startTime.Format("2006-01-02"), endTime.Format("2006-01-02")).
			Where("c.type = ?", 2).
			Where("c.is_delete = ?", 0).
			Rows()
		if err != nil {
			logger.Stdout.Error(fmt.Sprintf("统计数据失败:%+v", err))
			return
		}
		defer rows.Close()

		excel := excelize.NewFile()
		defer excel.Close()

		excel.SetCellValue("Sheet1", "A1", "运单号")
		excel.SetCellValue("Sheet1", "B1", "预付电子油卡")
		excel.SetCellValue("Sheet1", "C1", "预付时间")
		excel.SetCellValue("Sheet1", "D1", "车牌号")

		cellIndex := 2
		for rows.Next() {
			var transportationNumber string
			var totalAmount float64
			var createTime time.Time
			var transportationPlate string
			if err := rows.Scan(&transportationNumber, &totalAmount, &createTime, &transportationPlate); err != nil {
				logger.Stdout.Error(fmt.Sprintf("统计数据失败:%+v", err))
				continue
			}

			excel.SetCellValue("Sheet1", "A"+strconv.Itoa(cellIndex), transportationNumber)
			excel.SetCellValue("Sheet1", "B"+strconv.Itoa(cellIndex), totalAmount)
			excel.SetCellValue("Sheet1", "C"+strconv.Itoa(cellIndex), createTime.Format("2006-01-02 15:04:05"))
			excel.SetCellValue("Sheet1", "D"+strconv.Itoa(cellIndex), transportationPlate)
			cellIndex++
		}

		filename := fmt.Sprintf("%s-%s多蒙德预付电子油卡统计数据.xlsx", startTime.Format("20060102"), endTime.Format("20060102"))
		if err := excel.SaveAs(filename); err != nil {
			logger.Stdout.Error(fmt.Sprintf("保存多蒙德预付电子油卡统计数据失败:%+v", err))
			return
		}

		fileKey, err := lark.UploadXlsFile(filename)
		if err != nil {
			logger.Stdout.Error(fmt.Sprintf("上传多蒙德预付电子油卡统计数据失败:%+v", err))
			return
		}
		lark.SendFileMessage(lark.ReceivesID.XiaoYiYing, fileKey)
		lark.SendFileMessage(lark.ReceivesID.WangBingXin, fileKey)
		os.Remove(filename)
		logger.Stdout.Info(fmt.Sprintf("%s-%s多蒙德预付电子油卡统计数据已发送", startTime, endTime))
	}
}
