package tasks

import (
	"database/sql"
	"fmt"
	"math"
	"os"
	"sort"
	"strconv"
	"strings"
	"time"
	"wlhy/model"
	"wlhy/toolbox/logger"

	"github.com/xuri/excelize/v2"
)

// BusinessReports 运营日级数据报表
func BusinessReports() {
	startTime := time.Now()

	f, err := excelize.OpenFile("template/business_template.xlsx")
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	defer f.Close()

	// 根据客户来源进行分类
	var keHuZhuanJieShao, shiChangZhiTuo, xiangMuKeHu, ningChengZiGongSi, yuanBaoShanZiGongSi, juJianRen, xiaoShouShiChangZhiTuo, xiaoShouKeHuZhuanJieShao, duoMengDe []string
	shippers, err := model.DB.Table("tms_shipper").
		Select([]string{"company_name", "shipper_from"}).
		Where("shipper_type = ? AND is_delete = ?", 1, 0).
		Rows()
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	for shippers.Next() {
		var companyName string
		var shipperFrom string
		if err := shippers.Scan(&companyName, &shipperFrom); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}

		switch shipperFrom {
		case "商务客户转介绍":
			keHuZhuanJieShao = append(keHuZhuanJieShao, companyName)
		case "商务市场直拓":
			shiChangZhiTuo = append(shiChangZhiTuo, companyName)
		case "项目客户":
			xiangMuKeHu = append(xiangMuKeHu, companyName)
		case "宁城子公司":
			ningChengZiGongSi = append(ningChengZiGongSi, companyName)
		case "元宝山子公司":
			yuanBaoShanZiGongSi = append(yuanBaoShanZiGongSi, companyName)
		case "居间人":
			juJianRen = append(juJianRen, companyName)
		case "销售直营":
			xiaoShouShiChangZhiTuo = append(xiaoShouShiChangZhiTuo, companyName)
		case "销售+介绍人":
			xiaoShouKeHuZhuanJieShao = append(xiaoShouKeHuZhuanJieShao, companyName)
		case "多蒙德项目客户":
			duoMengDe = append(duoMengDe, companyName)
		}
	}

	companies := map[string]map[string][]string{
		"商务": {
			"商务客户转介绍": keHuZhuanJieShao,
			"商务市场直拓":  shiChangZhiTuo,
			"项目客户":    xiangMuKeHu,
			"宁城子公司":   ningChengZiGongSi,
			"元宝山子公司":  yuanBaoShanZiGongSi,
			"多蒙德项目客户": duoMengDe,
		},
		"销售": {
			"居间人":    juJianRen,
			"销售直营":   xiaoShouShiChangZhiTuo,
			"销售+介绍人": xiaoShouKeHuZhuanJieShao,
		},
	}
	sortBelong := []string{"商务", "销售"}
	sortCategory := []string{"商务客户转介绍", "商务市场直拓", "项目客户", "宁城子公司", "元宝山子公司", "多蒙德项目客户", "居间人",
		"销售直营", "销售+介绍人"}

	currentYear, _ := time.Parse("2006", time.Now().Format("2006"))
	t1 := time.Now().Add(-24 * time.Hour).Format("2006-01-02")
	t2 := time.Now().Add(-48 * time.Hour).Format("2006-01-02")
	t3 := time.Now().Add(-24 * time.Hour).Format("2006-01")

	start := 8
	ups := []int{}
	downs := []int{}

	totalFreight := 0.0
	totalTodayFreight := 0.0
	totalYestodayFreight := 0.0
	totalTodayOrder := 0
	totalMonthFreight := 0.0

	coalTodayAmount := 0.0
	coalYestodayAmount := 0.0
	otherTodayAmount := 0.0
	otherYestodayAmount := 0.0

	for _, belong := range sortBelong {
		f.SetCellValue("总数据日级报表", fmt.Sprintf("A%d", start), belong)

		num := 0
		for _, category := range sortCategory {

			companySlice := companies[belong][category]
			if len(companySlice) == 0 {
				continue
			}

			categoryTodayFreight := 0.0
			currentCompanies := []map[string]any{}

			for _, companyName := range companySlice {
				rows, err := model.DB.Table("tms_transport_note AS a").
					Joins("JOIN tms_shipper AS b ON a.create_company_id = b.company_id").
					Joins("JOIN tms_order_history AS c ON a.order_id = c.id AND a.order_version_no = c.version_no").
					Select([]string{"a.id", "b.company_id", "a.loading_time", "b.company_name", "a.freight_amount", "a.service_charge", "a.freight_paid", "a.service_fee_paid", "c.goods_type"}).
					Where("b.shipper_type = ?", 1).
					Where("b.is_delete = ?", 0).
					Where("b.company_name = ?", companyName).
					Where("b.company_name NOT IN (?)", []string{"连城县函成化百货商行", "赤峰跨境淘电子商务有限公司"}).
					Where("a.waybill_status NOT IN (?) AND a.is_delete = ?", []int{6, 7}, 0).
					Where("a.create_time >= ?", currentYear.Format("2006-01-02 15:04:05")).
					Where("a.create_time <= ?", time.Now().Format("2006-01-02"+" 00:00:00")).
					Rows()
				if err != nil {
					logger.Stdout.Error(err.Error())
					continue
				}

				companyCode := ""
				monthFreight := 0.0    // 月度交易额
				todayFreight := 0.0    // 今日交易额
				yestodayFreight := 0.0 // 昨日交易额
				todayOrder := 0        // 今日运单数量
				yestodayOrder := 0     // 昨日运单数量

				for rows.Next() {
					var id string
					var companyId string
					var loadingTime sql.NullTime
					var companyName string
					var freightAmount sql.NullFloat64
					var serviceCharge sql.NullFloat64
					var freightPaid sql.NullFloat64
					var serviceFeePaid sql.NullFloat64
					var goodsType string

					if err := rows.Scan(&id, &companyId, &loadingTime, &companyName, &freightAmount, &serviceCharge, &freightPaid, &serviceFeePaid, &goodsType); err != nil {
						logger.Stdout.Error(err.Error())
						continue
					}

					freight := freightAmount.Float64 + serviceCharge.Float64
					if freightPaid.Valid && freightPaid.Float64 > 0 {
						freight = freightPaid.Float64 + serviceFeePaid.Float64
					}
					if loadingTime.Time.Unix() >= currentYear.Unix() {
						totalFreight += freight
					}

					tt := loadingTime.Time.Format("2006-01")
					if tt == t3 {
						monthFreight += freight
						totalMonthFreight += freight
					}

					t := loadingTime.Time.Format("2006-01-02")
					if t == t1 {
						todayFreight += freight
						todayOrder++
						totalTodayFreight += freight
						totalTodayOrder++
						if goodsType == "煤炭及制品" {
							coalTodayAmount += freight
						} else {
							otherTodayAmount += freight
						}
					}
					if t == t2 {
						yestodayFreight += freight
						yestodayOrder++
						totalYestodayFreight += freight
						if goodsType == "煤炭及制品" {
							coalYestodayAmount += freight
						} else {
							otherYestodayAmount += freight
						}
					}

					companyCode = companyId
				}

				currentCompanies = append(currentCompanies, map[string]any{
					"companyName":     companyName,
					"companyCode":     companyCode,
					"monthFreight":    monthFreight,
					"todayFreight":    todayFreight,
					"yestodayFreight": yestodayFreight,
					"todayOrder":      todayOrder,
					"yestodayOrder":   yestodayOrder,
				})
			}

			// 按照今日交易额排序
			sort.Slice(currentCompanies, func(i, j int) bool {
				return currentCompanies[i]["todayFreight"].(float64) > currentCompanies[j]["todayFreight"].(float64)
			})

			for _, company := range currentCompanies {
				f.SetCellValue("总数据日级报表", fmt.Sprintf("E%d", start), company["companyName"])
				f.SetCellValue("总数据日级报表", fmt.Sprintf("F%d", start), company["companyCode"])
				f.SetCellValue("总数据日级报表", fmt.Sprintf("G%d", start), company["todayOrder"])
				f.SetCellValue("总数据日级报表", fmt.Sprintf("H%d", start), fmt.Sprintf("%0.2f", company["todayFreight"].(float64)/10000))
				f.SetCellValue("总数据日级报表", fmt.Sprintf("I%d", start), fmt.Sprintf("%0.2f", company["yestodayFreight"].(float64)/10000))
				f.SetCellValue("总数据日级报表", fmt.Sprintf("J%d", start), fmt.Sprintf("%0.2f", company["monthFreight"].(float64)/10000))

				diffFreight := company["yestodayFreight"].(float64) - company["todayFreight"].(float64)
				if math.Abs(diffFreight) > 100000 && diffFreight > 0 {
					f.SetCellValue("总数据日级报表", fmt.Sprintf("K%d", start), "↑")
					ups = append(ups, start)
				} else if math.Abs(diffFreight) > 100000 && diffFreight < 0 {
					f.SetCellValue("总数据日级报表", fmt.Sprintf("K%d", start), "↓")
					downs = append(downs, start)
				} else {
					f.SetCellValue("总数据日级报表", fmt.Sprintf("K%d", start), "")
				}

				categoryTodayFreight += company["todayFreight"].(float64)
				start++
			}

			f.SetCellValue("总数据日级报表", fmt.Sprintf("B%d", start-len(companySlice)), category)
			f.SetCellValue("总数据日级报表", fmt.Sprintf("C%d", start-len(companySlice)), len(companySlice))
			f.SetCellValue("总数据日级报表", fmt.Sprintf("D%d", start-len(companySlice)), fmt.Sprintf("%0.2f", categoryTodayFreight/10000))

			f.MergeCell("总数据日级报表", fmt.Sprintf("B%d", start-len(companySlice)), fmt.Sprintf("B%d", start-1))
			f.MergeCell("总数据日级报表", fmt.Sprintf("C%d", start-len(companySlice)), fmt.Sprintf("C%d", start-1))
			f.MergeCell("总数据日级报表", fmt.Sprintf("D%d", start-len(companySlice)), fmt.Sprintf("D%d", start-1))

			num += len(companySlice)

			f.MergeCell("总数据日级报表", fmt.Sprintf("A%d", start-num), fmt.Sprintf("A%d", start-1))
		}
	}
	s2, _ := f.NewStyle(&excelize.Style{
		Border: []excelize.Border{
			{Type: "left", Color: "#000000", Style: 1},
			{Type: "right", Color: "#000000", Style: 1},
			{Type: "top", Color: "#000000", Style: 1},
			{Type: "bottom", Color: "#000000", Style: 1},
		},
		Font: &excelize.Font{
			Size: 10,
		},
		Alignment: &excelize.Alignment{
			Horizontal: "center",
			Vertical:   "center",
			WrapText:   true,
		},
	})
	f.SetCellStyle("总数据日级报表", "A8", "K"+strconv.Itoa(start), s2)

	for _, v := range ups {

		upS, _ := f.NewStyle(&excelize.Style{
			Border: []excelize.Border{
				{Type: "left", Color: "#000000", Style: 1},
				{Type: "right", Color: "#000000", Style: 1},
				{Type: "top", Color: "#000000", Style: 1},
				{Type: "bottom", Color: "#000000", Style: 1},
			},
			Font: &excelize.Font{
				Size:  10,
				Color: "#3CB371",
			},
			Alignment: &excelize.Alignment{
				Horizontal: "center",
				Vertical:   "center",
				WrapText:   true,
			},
		})
		f.SetCellStyle("总数据日级报表", fmt.Sprintf("K%d", v), fmt.Sprintf("K%d", v), upS)
	}

	for _, v := range downs {
		downS, _ := f.NewStyle(&excelize.Style{
			Border: []excelize.Border{
				{Type: "left", Color: "#000000", Style: 1},
				{Type: "right", Color: "#000000", Style: 1},
				{Type: "top", Color: "#000000", Style: 1},
				{Type: "bottom", Color: "#000000", Style: 1},
			},
			Font: &excelize.Font{
				Size:  10,
				Color: "#FF0000",
			},
			Alignment: &excelize.Alignment{
				Horizontal: "center",
				Vertical:   "center",
				WrapText:   true,
			},
		})
		f.SetCellStyle("总数据日级报表", fmt.Sprintf("K%d", v), fmt.Sprintf("K%d", v), downS)
	}

	remarks := "备注：\n1.根据近一年数据，设定日级阈值（平均值 + 2倍标准差）为50%，当运费的日变化率超过或低于50%时，可以认为是“突然上升（绿色↑）”或“突然下降”（红色↓）。\n3.日级营业额低于10万客户已隐藏"

	f.SetCellValue("总数据日级报表", "A"+strconv.Itoa(start+1), remarks)
	f.MergeCell("总数据日级报表", "A"+strconv.Itoa(start+1), "K"+strconv.Itoa(start+1))
	remarkStyle, _ := f.NewStyle(&excelize.Style{
		Alignment: &excelize.Alignment{
			Horizontal: "left",
			Vertical:   "center",
			WrapText:   true,
		},
	})
	f.SetCellStyle("总数据日级报表", "A"+strconv.Itoa(start+1), "K"+strconv.Itoa(start+1), remarkStyle)
	f.SetRowHeight("总数据日级报表", start+1, 90)

	f.SetCellValue("总数据日级报表", "A2", time.Now().Add(-24*time.Hour).Format("2006-01-02"))
	f.SetCellValue("总数据日级报表", "A5", fmt.Sprintf("%0.2f", totalTodayFreight/10000))
	f.SetCellValue("总数据日级报表", "B5", totalTodayOrder)
	f.SetCellValue("总数据日级报表", "C5", fmt.Sprintf("%0.2f", totalMonthFreight/10000))
	f.SetCellValue("总数据日级报表", "D5", fmt.Sprintf("%0.2f", totalFreight/*********))
	f.SetCellValue("总数据日级报表", "E5", fmt.Sprintf("%0.2f%%", (totalTodayFreight-totalYestodayFreight)/totalYestodayFreight*100))

	f.SetCellValue("总数据日级报表", "F5", fmt.Sprintf("%0.2f", coalTodayAmount/10000))
	f.SetCellValue("总数据日级报表", "G5", fmt.Sprintf("%0.2f%%", (coalTodayAmount-coalYestodayAmount)/coalYestodayAmount*100))
	f.SetCellValue("总数据日级报表", "H5", fmt.Sprintf("%0.2f", otherTodayAmount/10000))
	f.SetCellValue("总数据日级报表", "I5", fmt.Sprintf("%0.2f%%", (otherTodayAmount-otherYestodayAmount)/otherYestodayAmount*100))

	index1, _ := f.GetSheetIndex("截止当日数据")
	f.SetActiveSheet(index1)

	overview1, overview2 := overview()
	f.SetCellValue("截止当日数据", "B1", overview1)
	f.SetCellValue("截止当日数据", "B2", overview2)

	active, _ := f.GetSheetIndex("总数据日级报表")
	f.SetActiveSheet(active)

	tt := time.Now().Add(-24 * time.Hour)
	xlsxName := fmt.Sprintf("%s日级运单报表.xlsx", tt.Format("20060102"))
	if err := f.SaveAs(xlsxName); err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	// 发送文件到飞书指定用户
	fileKey, err := lark.UploadXlsFile(xlsxName)
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	if err := lark.SendFileMessage(lark.ReceivesID.ZhaoChenYing, fileKey); err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	os.Remove(xlsxName)

	fmt.Println(time.Since(startTime))

	logger.Stdout.Info(fmt.Sprintf("%s日级运单报表已发送", tt.Format("20060102")))
}

// overview 概览数据
func overview() (string, string) {
	date := time.Now().Add(-24 * time.Hour).Format("2006-01-02")
	rows1, err := model.DB.Table("tms_transport_note").
		Select([]string{"loading_time", "freight_amount", "service_charge", "freight_paid", "service_fee_paid", "loading_number"}).
		Where("waybill_status NOT IN (?)", []int{6, 7}).
		Rows()
	if err != nil {
		logger.Stdout.Error(err.Error())
		return "", ""
	}

	var totalFreightGrossShipper float64
	var dateFreightGrossShipper float64
	var totalLoadingNumber float64
	var dateLoadingNumber float64
	totalTranportNotes := 0
	dateTransportNotes := 0

	for rows1.Next() {
		var loadingTime sql.NullTime
		var freightAmount sql.NullFloat64
		var serviceCharge sql.NullFloat64
		var freightPaid sql.NullFloat64
		var serviceFeePaid sql.NullFloat64
		var loadingNumber float64
		if err := rows1.Scan(&loadingTime, &freightAmount, &serviceCharge, &freightPaid, &serviceFeePaid, &loadingNumber); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}
		t := loadingTime.Time.Format("2006-01-02")

		if freightPaid.Valid && freightPaid.Float64 > 0 {
			totalFreightGrossShipper += freightPaid.Float64
		} else {
			totalFreightGrossShipper += freightAmount.Float64
		}

		if serviceFeePaid.Valid && serviceFeePaid.Float64 > 0 {
			totalFreightGrossShipper += serviceFeePaid.Float64
		} else {
			totalFreightGrossShipper += serviceCharge.Float64
		}

		totalTranportNotes++
		totalLoadingNumber += float64(loadingNumber)

		if t == date {
			if freightPaid.Valid && freightPaid.Float64 > 0 {
				dateFreightGrossShipper += freightPaid.Float64
			} else {
				dateFreightGrossShipper += freightAmount.Float64
			}

			if serviceFeePaid.Valid && serviceFeePaid.Float64 > 0 {
				dateFreightGrossShipper += serviceFeePaid.Float64
			} else {
				dateFreightGrossShipper += serviceCharge.Float64
			}
			dateTransportNotes++
			dateLoadingNumber += loadingNumber
		}
	}

	rows2, err := model.DB.Table("tms_shipper").
		Select([]string{"id", "company_name", "create_time"}).
		Where("is_delete = ?", 0).
		Where("shipper_type = ?", 1).
		Where("enterprise_status = ?", 2).
		Group("company_name").
		Rows()
	if err != nil {
		logger.Stdout.Error(err.Error())
		return "", ""
	}

	totalShippers := 0
	dateShippers := 0
	for rows2.Next() {
		var id int
		var companyName string
		var createTime sql.NullTime
		if err := rows2.Scan(&id, &companyName, &createTime); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}
		t := createTime.Time.Format("2006-01-02")
		if t == date {
			dateShippers += 1
		}
		totalShippers += 1
	}

	rows3, err := model.DB.Table("tms_vehicle").
		Select([]string{"id", "create_time", "vehicle_license_number"}).
		Where("is_delete = ?", 0).
		Rows()
	if err != nil {
		logger.Stdout.Error(err.Error())
		return "", ""
	}

	totalVehicles := 0
	dateVehicles := 0
	for rows3.Next() {
		var id int
		var createTime sql.NullTime
		var vehicleLicenseNumber string
		if err := rows3.Scan(&id, &createTime, &vehicleLicenseNumber); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}
		if strings.Contains(vehicleLicenseNumber, "挂") || strings.Contains(vehicleLicenseNumber, "超") {
			continue
		}

		t := createTime.Time.Format("2006-01-02")
		if t == date {
			dateVehicles += 1
		}
		totalVehicles += 1
	}

	totalTax := totalFreightGrossShipper/1.09*0.09 + totalFreightGrossShipper/1.09*0.09*0.12
	dateTax := dateFreightGrossShipper/1.09*0.09 + dateFreightGrossShipper/1.09*0.09*0.12

	overview1 := fmt.Sprintf(`赤峰网络货运产业园已在2023年11月11日开园运营。截至%s日，入驻平台企业累计%d家（其中：今日新增%d家）；累计注册货运车辆%d辆（其中：今日新增%d辆）；累计成交业务%d单（其中：今日成交%d单）；累计实现营业额%0.2f亿元（其中：今日实现%0.2f万元）；累计实现地方贡献%0.2f万元（其中：今日实现%0.2f万元）；累计货运吞吐量%0.2f万吨（其中：今日货运吞吐量%0.2f万吨）。`, time.Now().Format("2006年01月02"), totalShippers, dateShippers, totalVehicles, dateVehicles, totalTranportNotes, dateTransportNotes, totalFreightGrossShipper/*********, dateFreightGrossShipper/10000, totalTax/10000, dateTax/10000, totalLoadingNumber/10000, dateLoadingNumber/10000)

	overview2 := fmt.Sprintf(`截至%s日，该网络货运平台累计入驻企业%d家，注册货运车辆%d辆，成交业务%d单，货运吞吐量%0.2f万吨，实现营业额%0.2f亿元，实现税收%0.2f万元。`, time.Now().Format("2006年01月02"), totalShippers, totalVehicles, totalTranportNotes, totalLoadingNumber/10000, totalFreightGrossShipper/*********, totalTax/10000)

	return overview1, overview2
}

// OverloadReportMonth 月度超载报表
func OverloadReportMonth() {
	startDay := time.Now().Add(-60*24*time.Hour).Format("2006-01") + "-01"
	endDay := time.Now().Format("2006-01") + "-01"
	endTime, _ := time.Parse("2006-01-02", endDay)
	endTime = time.Date(endTime.Year(), endTime.Month(), 1, 0, 0, 0, 0, endTime.Location())

	rows, err := model.DB.Table("tms_transport_note AS a").
		Joins("JOIN tms_shipper AS b ON a.create_company_id = b.company_id").
		Joins("JOIN tms_vehicle AS c on a.transportation_plate = c.vehicle_license_number").
		Joins("LEFT JOIN tms_vehicle as d ON c.trailer_info_id = d.id").
		Select([]string{"b.company_name", "a.loading_number", "c.nuclear_load_weight",
			"d.nuclear_load_weight AS trailer_nuclear_load_weight", "a.freight_paid", "a.service_fee_paid",
			"a.freight_amount", "a.service_charge", "a.loading_time"}).
		Where("a.waybill_status NOT IN (?)", []int{6, 7}).
		Where("a.loading_time BETWEEN ? AND ?", startDay, endDay).
		Where("b.shipper_type = ? AND b.is_delete = ?", 1, 0).
		Rows()
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	type Item struct {
		TotalNotesNum          int
		OverloadNotesNum       int
		SereveOverloadNotesNum int
		TotalFreight           float64
		OverLoadFreight        float64
		SereveOverLoadFreight  float64
	}

	data := make(map[string]Item)
	prevOverloadNotesNum := 0
	currentOverloadCompaniesNum := make(map[string]bool)
	currentSevereOverloadCompaniesNum := make(map[string]bool)
	currentTotalNotesNum := 0
	currentOverloadNotesNum := 0
	currentSevereOverloadNotesNum := 0
	for rows.Next() {
		var companyName string
		var loadingNumber float64
		var nuclearLoadWeight float64
		var trailerNuclearLoadWeight sql.NullFloat64
		var freightPaid sql.NullFloat64
		var serviceFeePaid sql.NullFloat64
		var freightAmount float64
		var serviceCharge float64
		var loadingTime time.Time
		if err := rows.Scan(&companyName, &loadingNumber, &nuclearLoadWeight, &trailerNuclearLoadWeight, &freightPaid,
			&serviceFeePaid, &freightAmount, &serviceCharge, &loadingTime); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}

		// 核定载重量
		currentNuclearLoadWeight := nuclearLoadWeight
		if trailerNuclearLoadWeight.Float64 > 0 {
			currentNuclearLoadWeight += trailerNuclearLoadWeight.Float64
		}

		// 统计
		if loadingTime.Format("2006-01") == endTime.Add(-24*time.Hour).Format("2006-01") {
			if _, ok := data[companyName]; !ok {
				data[companyName] = Item{}
			}

			t := data[companyName]

			t.TotalNotesNum++
			currentTotalNotesNum++
			freight := freightAmount + serviceCharge
			if freightPaid.Valid && freightPaid.Float64 > 0 {
				freight = freightPaid.Float64 + serviceFeePaid.Float64
			}
			t.TotalFreight += freight

			if loadingNumber > currentNuclearLoadWeight {
				t.OverloadNotesNum++
				currentOverloadCompaniesNum[companyName] = true
				currentOverloadNotesNum++
				if loadingNumber > 100 {
					t.SereveOverloadNotesNum++
					currentSevereOverloadCompaniesNum[companyName] = true
					currentSevereOverloadNotesNum++
				}
				t.OverLoadFreight += freight
				if loadingNumber > 100 {
					t.SereveOverLoadFreight += freight
				}
			}

			data[companyName] = t
		} else {
			if loadingNumber > currentNuclearLoadWeight {
				prevOverloadNotesNum++
			}
		}

	}

	f, err := excelize.OpenFile("template/overload_month_template.xlsx")
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	defer f.Close()

	cellIndex := 5
	for k, v := range data {
		f.SetCellValue("Sheet1", fmt.Sprintf("A%d", cellIndex), k)
		f.SetCellValue("Sheet1", fmt.Sprintf("B%d", cellIndex), v.TotalNotesNum)
		f.SetCellValue("Sheet1", fmt.Sprintf("C%d", cellIndex), v.OverloadNotesNum)
		f.SetCellValue("Sheet1", fmt.Sprintf("D%d", cellIndex), fmt.Sprintf("%0.0f%%", float64(v.OverloadNotesNum)/float64(v.TotalNotesNum)*100))
		f.SetCellValue("Sheet1", fmt.Sprintf("E%d", cellIndex), fmt.Sprintf("%0.2f", v.TotalFreight/10000))
		f.SetCellValue("Sheet1", fmt.Sprintf("F%d", cellIndex), fmt.Sprintf("%0.2f", v.OverLoadFreight/10000))
		f.SetCellValue("Sheet1", fmt.Sprintf("G%d", cellIndex), fmt.Sprintf("%0.0f%%", float64(v.OverLoadFreight)/float64(v.TotalFreight)*100))
		f.SetCellValue("Sheet1", fmt.Sprintf("H%d", cellIndex), v.SereveOverloadNotesNum)
		f.SetCellValue("Sheet1", fmt.Sprintf("I%d", cellIndex), fmt.Sprintf("%0.0f%%", float64(v.SereveOverloadNotesNum)/float64(v.TotalNotesNum)*100))
		cellIndex++
	}
	f.SetCellValue("Sheet1", "A1", fmt.Sprintf("%s超吨数据报表", endTime.Add(-24*time.Hour).Format("2006年01月")))
	f.SetCellValue("Sheet1", "A2", time.Now().Format("2006年01月"))

	remark := fmt.Sprintf("备注：%s共%d家公司业务拉运，%d家超吨发运（其中包含%d家百吨以上严重超载），超载订单占总订单比率为%0.0f%%（严重超载占比%0.0f%%），较上月对比下降%0.0f%%", endTime.Format("2006年01月"), len(data), len(currentOverloadCompaniesNum), len(currentSevereOverloadCompaniesNum), float64(currentOverloadNotesNum)/float64(currentTotalNotesNum)*100, float64(currentSevereOverloadNotesNum)/float64(currentTotalNotesNum)*100, float64(prevOverloadNotesNum-currentOverloadNotesNum)/float64(prevOverloadNotesNum)*100)
	f.SetCellValue("Sheet1", fmt.Sprintf("A%d", cellIndex), remark)

	redFont, _ := f.NewStyle(&excelize.Style{
		Alignment: &excelize.Alignment{
			Horizontal: "left",
			Vertical:   "center",
			WrapText:   true,
		},
		Font: &excelize.Font{
			Color: "#FE0202",
		},
	})

	f.MergeCell("Sheet1", fmt.Sprintf("A%d", cellIndex), fmt.Sprintf("I%d", cellIndex))
	f.SetCellStyle("Sheet1", fmt.Sprintf("A%d", cellIndex), fmt.Sprintf("I%d", cellIndex), redFont)
	f.SetRowHeight("Sheet1", cellIndex, 50)

	xlsxName := fmt.Sprintf("%s月度超载业务占比.xlsx", endTime.Add(-24*time.Hour).Format("200601"))
	if err := f.SaveAs(xlsxName); err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	// 发送文件到飞书指定用户
	fileKey, err := lark.UploadXlsFile(xlsxName)
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	if err := lark.SendFileMessage(lark.ReceivesID.ZhaoChenYing, fileKey); err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	os.Remove(xlsxName)

	logger.Stdout.Info(fmt.Sprintf("%s月度超载业务占比报表已发送", endTime.Add(-24*time.Hour).Format("200601")))
}

// BusinessReportsFor100k 日级10万以上客户报表
func BusinessReportsFor100k() {
	freight100k("lu")
	freight100k("chen")
}

func freight100k(recipient string) {
	if recipient == "" {
		return
	}
	templateName := ""
	xlsxName := ""
	switch recipient {
	case "lu":
		templateName = "business_100k_template.xlsx"
		xlsxName = fmt.Sprintf("%s日级10万以上客户数据报表.xlsx", time.Now().Add(-24*time.Hour).Format("20060102"))
	case "chen":
		templateName = "business_100k_template-noncompany.xlsx"
		xlsxName = fmt.Sprintf("%s日级10万以上客户数据报表-客户编码.xlsx", time.Now().Add(-24*time.Hour).Format("20060102"))
	}

	f, err := excelize.OpenFile("template/" + templateName)
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	defer f.Close()

	// 根据客户来源进行分类
	var keHuZhuanJieShao, shiChangZhiTuo, xiangMuKeHu, ningChengZiGongSi, yuanBaoShanZiGongSi, juJianRen, xiaoShouShiChangZhiTuo, xiaoShouKeHuZhuanJieShao, duoMengDe []string
	shippers, err := model.DB.Table("tms_shipper").
		Select([]string{"company_name", "shipper_from"}).
		Where("shipper_type = ? AND is_delete = ?", 1, 0).
		Rows()
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	for shippers.Next() {
		var companyName string
		var shipperFrom string
		if err := shippers.Scan(&companyName, &shipperFrom); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}

		switch shipperFrom {
		case "商务客户转介绍":
			keHuZhuanJieShao = append(keHuZhuanJieShao, companyName)
		case "商务市场直拓":
			shiChangZhiTuo = append(shiChangZhiTuo, companyName)
		case "项目客户":
			xiangMuKeHu = append(xiangMuKeHu, companyName)
		case "宁城子公司":
			ningChengZiGongSi = append(ningChengZiGongSi, companyName)
		case "元宝山子公司":
			yuanBaoShanZiGongSi = append(yuanBaoShanZiGongSi, companyName)
		case "居间人":
			juJianRen = append(juJianRen, companyName)
		case "销售直营":
			xiaoShouShiChangZhiTuo = append(xiaoShouShiChangZhiTuo, companyName)
		case "销售+介绍人":
			xiaoShouKeHuZhuanJieShao = append(xiaoShouKeHuZhuanJieShao, companyName)
		case "多蒙德项目客户":
			duoMengDe = append(duoMengDe, companyName)
		}
	}

	companies := map[string]map[string][]string{
		"商务": {
			"商务客户转介绍": keHuZhuanJieShao,
			"商务市场直拓":  shiChangZhiTuo,
			"项目客户":    xiangMuKeHu,
			"宁城子公司":   ningChengZiGongSi,
			"元宝山子公司":  yuanBaoShanZiGongSi,
			"多蒙德项目客户": duoMengDe,
		},
		"销售": {
			"居间人":    juJianRen,
			"销售直营":   xiaoShouShiChangZhiTuo,
			"销售+介绍人": xiaoShouKeHuZhuanJieShao,
		},
	}
	sortBelong := []string{"商务", "销售"}
	sortCategory := []string{"商务客户转介绍", "商务市场直拓", "项目客户", "宁城子公司", "元宝山子公司", "多蒙德项目客户", "居间人",
		"销售直营", "销售+介绍人"}

	currentYear, _ := time.Parse("2006", time.Now().Format("2006"))
	t1 := time.Now().Add(-24 * time.Hour).Format("2006-01-02")
	t2 := time.Now().Add(-48 * time.Hour).Format("2006-01-02")
	t3 := time.Now().Add(-24 * time.Hour).Format("2006-01")

	start := 6
	ups := []int{}
	downs := []int{}

	totalFreight := 0.0
	totalTodayFreight := 0.0
	totalYestodayFreight := 0.0
	totalTodayOrder := 0
	totalMonthFreight := 0.0

	coalTodayAmount := 0.0
	coalYestodayAmount := 0.0
	otherTodayAmount := 0.0
	otherYestodayAmount := 0.0

	for _, belong := range sortBelong {
		f.SetCellValue("总数据日级报表", fmt.Sprintf("A%d", start), belong)

		num := 0
		for _, category := range sortCategory {
			companySlice := companies[belong][category]
			if len(companySlice) == 0 {
				continue
			}

			categoryTodayFreight := 0.0
			currentCompanies := []map[string]any{}

			for _, companyName := range companySlice {
				rows, err := model.DB.Table("tms_transport_note AS a").
					Joins("JOIN tms_shipper AS b ON a.create_company_id = b.company_id").
					Joins("JOIN tms_order_history AS c ON a.order_id = c.id AND a.order_version_no = c.version_no").
					Select([]string{"a.id", "b.company_id", "a.loading_time", "b.company_name", "a.freight_amount", "a.service_charge", "a.freight_paid", "a.service_fee_paid", "c.goods_type"}).
					Where("b.shipper_type = ?", 1).
					Where("b.is_delete = ?", 0).
					Where("b.company_name = ?", companyName).
					Where("b.company_name NOT IN (?)", []string{"连城县函成化百货商行", "赤峰跨境淘电子商务有限公司"}).
					Where("a.waybill_status NOT IN (?) AND a.is_delete = ?", []int{6, 7}, 0).
					Where("a.create_time >= ?", currentYear.Format("2006-01-02 15:04:05")).
					Where("a.create_time <= ?", time.Now().Format("2006-01-02"+" 00:00:00")).
					Rows()
				if err != nil {
					logger.Stdout.Error(err.Error())
					continue
				}

				companyCode := ""
				monthFreight := 0.0    // 月度交易额
				todayFreight := 0.0    // 今日交易额
				yestodayFreight := 0.0 // 昨日交易额
				todayOrder := 0        // 今日运单数量
				yestodayOrder := 0     // 昨日运单数量

				for rows.Next() {
					var id string
					var companyId string
					var loadingTime sql.NullTime
					var companyName string
					var freightAmount sql.NullFloat64
					var serviceCharge sql.NullFloat64
					var freightPaid sql.NullFloat64
					var serviceFeePaid sql.NullFloat64
					var goodsType string

					if err := rows.Scan(&id, &companyId, &loadingTime, &companyName, &freightAmount, &serviceCharge, &freightPaid, &serviceFeePaid, &goodsType); err != nil {
						logger.Stdout.Error(err.Error())
						continue
					}

					freight := freightAmount.Float64 + serviceCharge.Float64
					if freightPaid.Valid && freightPaid.Float64 > 0 {
						freight = freightPaid.Float64 + serviceFeePaid.Float64
					}
					if loadingTime.Time.Unix() >= currentYear.Unix() {
						totalFreight += freight
					}

					tt := loadingTime.Time.Format("2006-01")
					if tt == t3 {
						monthFreight += freight
						totalMonthFreight += freight
					}

					t := loadingTime.Time.Format("2006-01-02")
					if t == t1 {
						todayFreight += freight
						todayOrder++
						totalTodayFreight += freight
						totalTodayOrder++
						if goodsType == "煤炭及制品" {
							coalTodayAmount += freight
						} else {
							otherTodayAmount += freight
						}
					}
					if t == t2 {
						yestodayFreight += freight
						yestodayOrder++
						totalYestodayFreight += freight
						if goodsType == "煤炭及制品" {
							coalYestodayAmount += freight
						} else {
							otherYestodayAmount += freight
						}
					}

					companyCode = companyId
				}

				categoryTodayFreight += todayFreight
				if todayFreight < 100000 {
					continue
				}

				currentCompanies = append(currentCompanies, map[string]any{
					"companyName":     companyName,
					"companyCode":     companyCode,
					"monthFreight":    monthFreight,
					"todayFreight":    todayFreight,
					"yestodayFreight": yestodayFreight,
					"todayOrder":      todayOrder,
					"yestodayOrder":   yestodayOrder,
				})
			}

			// 按照今日交易额排序
			sort.Slice(currentCompanies, func(i, j int) bool {
				return currentCompanies[i]["todayFreight"].(float64) > currentCompanies[j]["todayFreight"].(float64)
			})

			for _, company := range currentCompanies {
				switch recipient {
				case "lu":
					f.SetCellValue("总数据日级报表", fmt.Sprintf("E%d", start), company["companyName"])
				case "chen":
					f.SetCellValue("总数据日级报表", fmt.Sprintf("E%d", start), company["companyCode"])
				}

				f.SetCellValue("总数据日级报表", fmt.Sprintf("F%d", start), company["todayOrder"])
				f.SetCellValue("总数据日级报表", fmt.Sprintf("G%d", start), fmt.Sprintf("%0.2f", company["todayFreight"].(float64)/10000))
				f.SetCellValue("总数据日级报表", fmt.Sprintf("H%d", start), fmt.Sprintf("%0.2f", company["monthFreight"].(float64)/10000))

				diffFreight := company["yestodayFreight"].(float64) - company["todayFreight"].(float64)
				if math.Abs(diffFreight) > 100000 && diffFreight > 0 {
					f.SetCellValue("总数据日级报表", fmt.Sprintf("I%d", start), "↑")
					ups = append(ups, start)
				} else if math.Abs(diffFreight) > 100000 && diffFreight < 0 {
					f.SetCellValue("总数据日级报表", fmt.Sprintf("I%d", start), "↓")
					downs = append(downs, start)
				} else {
					f.SetCellValue("总数据日级报表", fmt.Sprintf("I%d", start), "")
				}

				start++
			}

			f.SetCellValue("总数据日级报表", fmt.Sprintf("B%d", start-len(currentCompanies)), category)
			f.SetCellValue("总数据日级报表", fmt.Sprintf("C%d", start-len(currentCompanies)), len(companySlice))
			f.SetCellValue("总数据日级报表", fmt.Sprintf("D%d", start-len(currentCompanies)), fmt.Sprintf("%0.2f", categoryTodayFreight/10000))

			if len(currentCompanies) == 0 {
				f.SetCellValue("总数据日级报表", fmt.Sprintf("E%d", start), "日级无10万以上客户")
				f.MergeCell("总数据日级报表", fmt.Sprintf("E%d", start), fmt.Sprintf("I%d", start))
				start++
			}
			if len(currentCompanies) > 0 {
				f.MergeCell("总数据日级报表", fmt.Sprintf("B%d", start-len(currentCompanies)), fmt.Sprintf("B%d", start-1))
				f.MergeCell("总数据日级报表", fmt.Sprintf("C%d", start-len(currentCompanies)), fmt.Sprintf("C%d", start-1))
				f.MergeCell("总数据日级报表", fmt.Sprintf("D%d", start-len(currentCompanies)), fmt.Sprintf("D%d", start-1))
			}

			num += len(currentCompanies)
			f.MergeCell("总数据日级报表", fmt.Sprintf("A%d", start-num), fmt.Sprintf("A%d", start-1))
		}
	}
	start--
	s2, _ := f.NewStyle(&excelize.Style{
		Border: []excelize.Border{
			{Type: "left", Color: "#000000", Style: 1},
			{Type: "right", Color: "#000000", Style: 1},
			{Type: "top", Color: "#000000", Style: 1},
			{Type: "bottom", Color: "#000000", Style: 1},
		},
		Font: &excelize.Font{
			Size: 10,
		},
		Alignment: &excelize.Alignment{
			Horizontal: "center",
			Vertical:   "center",
			WrapText:   true,
		},
	})
	f.SetCellStyle("总数据日级报表", "A6", "I"+strconv.Itoa(start), s2)

	for _, v := range ups {
		upS, _ := f.NewStyle(&excelize.Style{
			Border: []excelize.Border{
				{Type: "left", Color: "#000000", Style: 1},
				{Type: "right", Color: "#000000", Style: 1},
				{Type: "top", Color: "#000000", Style: 1},
				{Type: "bottom", Color: "#000000", Style: 1},
			},
			Font: &excelize.Font{
				Size:  10,
				Color: "#3CB371",
			},
			Alignment: &excelize.Alignment{
				Horizontal: "center",
				Vertical:   "center",
				WrapText:   true,
			},
		})
		f.SetCellStyle("总数据日级报表", fmt.Sprintf("I%d", v), fmt.Sprintf("I%d", v), upS)
	}

	for _, v := range downs {
		downS, _ := f.NewStyle(&excelize.Style{
			Border: []excelize.Border{
				{Type: "left", Color: "#000000", Style: 1},
				{Type: "right", Color: "#000000", Style: 1},
				{Type: "top", Color: "#000000", Style: 1},
				{Type: "bottom", Color: "#000000", Style: 1},
			},
			Font: &excelize.Font{
				Size:  10,
				Color: "#FF0000",
			},
			Alignment: &excelize.Alignment{
				Horizontal: "center",
				Vertical:   "center",
				WrapText:   true,
			},
		})
		f.SetCellStyle("总数据日级报表", fmt.Sprintf("I%d", v), fmt.Sprintf("I%d", v), downS)
	}

	remarks := "备注：\n1.根据近一年数据，设定日级阈值（平均值 + 2倍标准差）为50%，当运费的日变化率超过或低于50%时，可以认为是“突然上升（绿色↑）”或“突然下降”（红色↓）。\n2.日级营业额低于10万客户已隐藏"

	f.SetCellValue("总数据日级报表", "A"+strconv.Itoa(start+1), remarks)
	f.MergeCell("总数据日级报表", "A"+strconv.Itoa(start+1), "I"+strconv.Itoa(start+1))
	remarkStyle, _ := f.NewStyle(&excelize.Style{
		Alignment: &excelize.Alignment{
			Horizontal: "left",
			Vertical:   "center",
			WrapText:   true,
		},
	})
	f.SetCellStyle("总数据日级报表", "A"+strconv.Itoa(start+1), "I"+strconv.Itoa(start+1), remarkStyle)
	f.SetRowHeight("总数据日级报表", start+1, 90)

	f.SetCellValue("总数据日级报表", "A2", time.Now().Add(-24*time.Hour).Format("2006-01-02"))
	f.SetCellValue("总数据日级报表", "A4", fmt.Sprintf("%0.2f", totalTodayFreight/10000))
	f.SetCellValue("总数据日级报表", "B4", totalTodayOrder)
	f.SetCellValue("总数据日级报表", "C4", fmt.Sprintf("%0.2f", totalMonthFreight/10000))
	f.SetCellValue("总数据日级报表", "D4", fmt.Sprintf("%0.2f", totalFreight/*********))
	f.SetCellValue("总数据日级报表", "E4", fmt.Sprintf("%0.2f%%", (totalTodayFreight-totalYestodayFreight)/totalYestodayFreight*100))

	f.SetCellValue("总数据日级报表", "F4", fmt.Sprintf("%0.2f", coalTodayAmount/10000))
	f.SetCellValue("总数据日级报表", "G4", fmt.Sprintf("%0.2f%%", (coalTodayAmount-coalYestodayAmount)/coalYestodayAmount*100))
	f.SetCellValue("总数据日级报表", "H4", fmt.Sprintf("%0.2f", otherTodayAmount/10000))
	f.SetCellValue("总数据日级报表", "I4", fmt.Sprintf("%0.2f%%", (otherTodayAmount-otherYestodayAmount)/otherYestodayAmount*100))

	if err := f.SaveAs(xlsxName); err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	// 发送文件到飞书指定用户
	fileKey, err := lark.UploadXlsFile(xlsxName)
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	if err := lark.SendFileMessage(lark.ReceivesID.ZhaoChenYing, fileKey); err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	os.Remove(xlsxName)

	logger.Stdout.Info(fmt.Sprintf("%s日级10万以上客户数据报表已发送-%s", time.Now().Add(-24*time.Hour).Format("20060102"), recipient))
}

// BusinessReportsForChildCompany 发送子公司的日级运单报表
func BusinessReportsForChildCompany() {
	childCompanyBusinessReport("宁城")
	childCompanyBusinessReport("元宝山")
}

// childCompanyBusinessReport 子公司日级运单报表数据
func childCompanyBusinessReport(childCompany string) {
	startTime := time.Now()

	receiveID := ""
	xlsxName := ""
	if childCompany == "宁城" {
		xlsxName = fmt.Sprintf("%s宁城日级运单报表.xlsx", time.Now().Add(-24*time.Hour).Format("20060102"))
		receiveID = lark.ReceivesID.WangPeng
	}
	if childCompany == "元宝山" {
		xlsxName = fmt.Sprintf("%s元宝山日级运单报表.xlsx", time.Now().Add(-24*time.Hour).Format("20060102"))
		receiveID = lark.ReceivesID.YuLei
	}

	// 根据客户来源进行分类
	companies := []string{}
	shippers, err := model.DB.Table("tms_shipper").
		Select([]string{"company_name", "shipper_from"}).
		Where("shipper_type = ? AND is_delete = ?", 1, 0).
		Rows()
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	for shippers.Next() {
		var companyName string
		var shipperFrom string
		if err := shippers.Scan(&companyName, &shipperFrom); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}

		if strings.Contains(shipperFrom, childCompany) {
			companies = append(companies, companyName)
		}
	}

	f, err := excelize.OpenFile("template/child_company_template.xlsx")
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	defer f.Close()

	t1 := time.Now().Add(-24 * time.Hour).Format("2006-01-02")
	t2 := time.Now().Add(-48 * time.Hour).Format("2006-01-02")

	cellIndex := 6

	totalFreight := 0.0
	totalTodayFreight := 0.0
	totalYestodayFreight := 0.0
	totalTodayOrder := 0

	currentCompanies := []map[string]any{}

	for _, companyName := range companies {
		rows, err := model.DB.Table("tms_transport_note AS a").
			Joins("JOIN tms_shipper AS b ON a.create_company_id = b.company_id").
			Joins("JOIN tms_order_history AS c ON a.order_id = c.id AND a.order_version_no = c.version_no").
			Select([]string{"a.id", "b.company_id", "a.loading_time", "b.company_name", "a.freight_amount", "a.service_charge", "a.freight_paid", "a.service_fee_paid", "c.goods_type"}).
			Where("b.shipper_type = ?", 1).
			Where("b.is_delete = ?", 0).
			Where("b.company_name = ?", companyName).
			Where("b.company_name NOT IN (?)", []string{"连城县函成化百货商行", "赤峰跨境淘电子商务有限公司"}).
			Where("a.waybill_status NOT IN (?) AND a.is_delete = ?", []int{6, 7}, 0).
			Where("a.create_time <= ?", time.Now().Format("2006-01-02"+" 00:00:00")).
			Rows()
		if err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}

		todayFreight := 0.0    // 今日交易额
		yestodayFreight := 0.0 // 昨日交易额
		todayOrder := 0        // 今日运单数量
		yestodayOrder := 0     // 昨日运单数量

		for rows.Next() {
			var id string
			var companyId string
			var loadingTime sql.NullTime
			var companyName string
			var freightAmount sql.NullFloat64
			var serviceCharge sql.NullFloat64
			var freightPaid sql.NullFloat64
			var serviceFeePaid sql.NullFloat64
			var goodsType string

			if err := rows.Scan(&id, &companyId, &loadingTime, &companyName, &freightAmount, &serviceCharge, &freightPaid, &serviceFeePaid, &goodsType); err != nil {
				logger.Stdout.Error(err.Error())
				continue
			}

			freight := freightAmount.Float64 + serviceCharge.Float64
			if freightPaid.Valid && freightPaid.Float64 > 0 {
				freight = freightPaid.Float64 + serviceFeePaid.Float64
			}

			totalFreight += freight

			t := loadingTime.Time.Format("2006-01-02")
			if t == t1 {
				todayFreight += freight
				todayOrder++
				totalTodayFreight += freight
				totalTodayOrder++
			}
			if t == t2 {
				yestodayFreight += freight
				yestodayOrder++
				totalYestodayFreight += freight
			}
		}

		currentCompanies = append(currentCompanies, map[string]any{
			"companyName":     companyName,
			"todayFreight":    todayFreight,
			"yestodayFreight": yestodayFreight,
			"todayOrder":      todayOrder,
			"yestodayOrder":   yestodayOrder,
		})
	}

	// 按照今日交易额排序
	sort.Slice(currentCompanies, func(i, j int) bool {
		return currentCompanies[i]["todayFreight"].(float64) > currentCompanies[j]["todayFreight"].(float64)
	})

	for _, company := range currentCompanies {
		f.SetCellValue("Sheet1", fmt.Sprintf("A%d", cellIndex), company["companyName"])
		f.SetCellValue("Sheet1", fmt.Sprintf("B%d", cellIndex), company["todayOrder"])
		f.SetCellValue("Sheet1", fmt.Sprintf("C%d", cellIndex), fmt.Sprintf("%0.2f", company["todayFreight"].(float64)/10000))
		f.SetCellValue("Sheet1", fmt.Sprintf("D%d", cellIndex), "")

		cellIndex++
	}

	if childCompany == "宁城" {
		f.SetCellValue("Sheet1", "A1", "宁城日级数据报表")
	}
	if childCompany == "元宝山" {
		f.SetCellValue("Sheet1", "A1", "元宝山日级数据报表")
	}

	f.SetCellValue("Sheet1", "A2", time.Now().Add(-24*time.Hour).Format("2006-01-02"))

	f.SetCellValue("Sheet1", "A4", totalTodayOrder)
	f.SetCellValue("Sheet1", "B4", fmt.Sprintf("%0.2f", totalTodayFreight/10000))
	f.SetCellValue("Sheet1", "C4", fmt.Sprintf("%0.2f", totalFreight/10000))
	f.SetCellValue("Sheet1", "D4", fmt.Sprintf("%0.2f%%", (totalTodayFreight-totalYestodayFreight)/totalYestodayFreight*100))

	if err := f.SaveAs(xlsxName); err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	// 发送文件到飞书
	fileKey, err := lark.UploadXlsFile(xlsxName)
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	if err := lark.SendFileMessage(receiveID, fileKey); err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	os.Remove(xlsxName)

	fmt.Println(time.Since(startTime))

	logger.Stdout.Info(fmt.Sprintf("%s日级运单报表已发送到飞书-%s", childCompany, time.Now().Add(-24*time.Hour).Format("20060102")))
}
