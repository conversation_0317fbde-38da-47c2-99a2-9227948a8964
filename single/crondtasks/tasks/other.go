package tasks

import (
	"database/sql"
	"fmt"
	"os"
	"time"

	"wlhy/model"
	"wlhy/thirdparty/zwl"
	"wlhy/toolbox/email"
	"wlhy/toolbox/logger"

	"github.com/xuri/excelize/v2"
	"golang.org/x/exp/rand"
)

// UpdateShipperPayStatus 更改运单状态为无需二次审核
func UpdateShipperPayStatus() {
	rows, _ := model.DB.Table("tms_transport_note").
		Select([]string{"id"}).
		Where("shipper_pay_status = ?", 0).
		Rows()

	ids := []string{}
	for rows.Next() {
		var id string
		if err := rows.Scan(&id); err != nil {
			continue
		}
		ids = append(ids, id)
	}
	if len(ids) == 0 {
		return
	}

	if err := model.DB.Table("tms_transport_note").
		Where("id IN ?", ids).
		Update("shipper_pay_status", 1).
		Error; err != nil {
		logger.Stdout.Error(err.<PERSON>rror())
	}
}

// UpdateShipperPayStatus 更改回单已签收但运单状态为待验收的运单状态为待支付
func UpdateTransportExceptionReceipt() {
	rows, _ := model.DB.Table("tms_transport_note AS a").
		Joins("LEFT JOIN shipper_config AS b ON a.create_company_id = b.user_id").
		Select([]string{"a.id", "b.upload_receipt_enabled", "a.unload_time", "a.transportation_number"}).
		Where("a.is_up_receipt = ?", 1).
		Where("a.is_confirm_receipt = ?", 1).
		Where("a.waybill_status = ?", 9).
		Where("a.unload_number != ?", 0).
		Rows()

	ids := []string{}
	for rows.Next() {
		var id string
		var uploadReceiptEnabled sql.NullInt16
		var unloadTime sql.NullTime
		var transportationNumber string
		if err := rows.Scan(&id, &uploadReceiptEnabled, &unloadTime, &transportationNumber); err != nil {
			continue
		}
		ids = append(ids, id)
	}

	if len(ids) == 0 {
		return
	}

	if err := model.DB.Table("tms_transport_note").
		Where("id IN ?", ids).
		Updates(map[string]any{
			"waybill_status": 3,
		}).
		Error; err != nil {
		logger.Stdout.Error(err.Error())
	}
}

// UpdateShipperConfig 更新shipper_config
func UpdateShipperConfig() {
	rows, err := model.DB.Table("tms_shipper AS a").
		Joins("LEFT JOIN shipper_config AS b ON a.company_id = b.user_id").
		Select([]string{"a.company_id", "b.id"}).
		Where("a.shipper_type = ?", 1).
		Where("a.is_delete = ?", 0).
		Where("a.audit_status = ?", 2).
		Where("a.certification_status = ?", 2).
		Rows()
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	for rows.Next() {
		var companyID string
		var configID sql.NullString
		if err := rows.Scan(&companyID, &configID); err != nil {
			continue
		}

		if configID.Valid || companyID == "" {
			continue
		}

		fmt.Println(companyID, "-", configID)
		id := fmt.Sprintf("%d%06d", time.Now().UnixMilli(), rand.Intn(999999))

		if err := model.DB.Table("shipper_config").Create(map[string]any{
			"id":                           id,
			"create_by":                    companyID,
			"create_time":                  time.Now().Format("2006-01-02 15:04:05"),
			"update_by":                    companyID,
			"update_time":                  time.Now().Format("2006-01-02 15:04:05"),
			"is_delete":                    0,
			"user_id":                      companyID,
			"vehicle_photo_enabled":        0,
			"person_vehicle_photo_enabled": 0,
			"loading_photo_enabled":        0,
			"delivery_note_enabled":        2, // 启用装货磅单
			"unloading_note_enabled":       2, // 启用卸货磅单
			"unloading_photo_enabled":      0,
			"large_transport_enabled":      0,
			"upload_receipt_enabled":       1, // 启用回单上传
			"auto_withdrawal":              1, // 启用到付运费自动提现
			"reservation_auto_withdrawal":  0,
			"is_approve":                   0, // 启用银行无审批模式
			"day_limit":                    0,
		}).Error; err != nil {
			logger.Stdout.Error(err.Error())
		}
	}
}

// CrawlerData 抓取迪卡侬货号数据
func CrawlerData() {
	db, err := model.NewTestDB()
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	rows, err := db.Raw("SELECT * FROM crawler_decathlon_result cdr WHERE cdr.check_date = ?",
		time.Now().Format("2006-01-02")).Rows()
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	f := excelize.NewFile()
	defer f.Close()

	f.SetCellValue("Sheet1", "A1", "dsmCode")
	f.SetCellValue("Sheet1", "B1", "modelCode")
	f.SetCellValue("Sheet1", "C1", "商品名称")
	f.SetCellValue("Sheet1", "D1", "颜色标签")
	f.SetCellValue("Sheet1", "E1", "价格")
	f.SetCellValue("Sheet1", "F1", "尺码")
	f.SetCellValue("Sheet1", "G1", "库存")
	f.SetCellValue("Sheet1", "H1", "爬取时间")
	f.SetCellValue("Sheet1", "I1", "加后缀的货号")

	start := 2
	for rows.Next() {
		var id int
		var dsmCode sql.NullString
		var modelCode sql.NullString
		var name sql.NullString
		var colorLabel sql.NullString
		var price sql.NullString
		var size sql.NullString
		var quantity sql.NullString
		var checkDate sql.NullString
		var modelCodeConversion sql.NullString
		if err := rows.Scan(&id, &dsmCode, &modelCode, &name, &colorLabel, &price, &size, &quantity, &checkDate, &modelCodeConversion); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}

		f.SetCellValue("Sheet1", fmt.Sprintf("A%d", start), dsmCode.String)
		f.SetCellValue("Sheet1", fmt.Sprintf("B%d", start), modelCode.String)
		f.SetCellValue("Sheet1", fmt.Sprintf("C%d", start), name.String)
		f.SetCellValue("Sheet1", fmt.Sprintf("D%d", start), colorLabel.String)
		f.SetCellValue("Sheet1", fmt.Sprintf("E%d", start), price.String)
		f.SetCellValue("Sheet1", fmt.Sprintf("F%d", start), size.String)
		f.SetCellValue("Sheet1", fmt.Sprintf("G%d", start), quantity.String)
		f.SetCellValue("Sheet1", fmt.Sprintf("H%d", start), checkDate.String)
		f.SetCellValue("Sheet1", fmt.Sprintf("I%d", start), modelCodeConversion.String)
		start++
	}

	filename := fmt.Sprintf("迪卡侬货号数据-%s.xlsx", time.Now().Format("20060102"))
	if err := f.SaveAs(filename); err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	e := &email.Email{
		To:      []string{"<EMAIL>", "<EMAIL>"},
		Subject: time.Now().Format("20060102") + "迪卡侬货号数据",
		Attach:  filename,
	}

	if err := e.SendEmail(); err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	os.Remove(filename)
	logger.Stdout.Info("迪卡侬货号数据邮件发送成功")
}

// CheckZWLBalance 检查中物联余额
func CheckZWLBalance() {
	z := zwl.NewZWL()
	balance, err := z.ComBalance()
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	if balance <= 1000 {
		lark.SendTextMessage(lark.ReceivesID.ZhaoShengWei, fmt.Sprintf("中物联余额不足, 请及时充值, 当前余额: %.2f", balance))
	}
}
