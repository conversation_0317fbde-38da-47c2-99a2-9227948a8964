package tasks

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"
	"wlhy/model"
	"wlhy/thirdparty/oss"
	"wlhy/toolbox"
	"wlhy/toolbox/email"
	"wlhy/toolbox/logger"
)

func JiaTuo() {
	// t := time.Now()
	// y := t.Add(-24 * time.Hour)

	rows, err := model.DB.Table("tms_transport_note AS a").
		Joins("JOIN tms_shipper AS b ON a.create_company_id = b.company_id").
		Select([]string{"a.transportation_number", "a.transportation_plate", "a.loading_image", "a.unload_image"}).
		Where("b.company_id = ?", "1810605222710239233").
		Where("a.waybill_status IN (?)", []int{3, 4, 5, 8, 9}).
		/* Where("a.unload_time BETWEEN ? AND ?", fmt.Sprintf("%s 00:00:00", y.Format("2006-01-02")), fmt.Sprintf("%s 00:00:00", t.Format("2006-01-02"))). */
		Where("a.transportation_number IN (?)", []string{
			// TODO
		}).
		Rows()
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	dir := "jiatuo"
	if _, err := os.Stat(dir); os.IsNotExist(err) {
		os.Mkdir(dir, 0777)
	}

	for rows.Next() {
		var transportationNumber string
		var transportationPlate string
		var loadingImage string
		var unloadImage string
		if err := rows.Scan(&transportationNumber, &transportationPlate, &loadingImage, &unloadImage); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}

		loadingImages := strings.Split(loadingImage, ",")
		unloadImages := strings.Split(unloadImage, ",")

		currentDir := filepath.Join(dir, transportationNumber+"_"+transportationPlate)
		if _, err := os.Stat(currentDir); os.IsNotExist(err) {
			os.Mkdir(currentDir, 0777)
		}

		if len(loadingImages) >= 4 && loadingImages[3] != "" && loadingImages[3] != "null" {
			filename := filepath.Join(currentDir, "装货"+filepath.Ext(loadingImages[3]))
			toolbox.DownloadFile(filename, loadingImages[3])
		}

		if len(unloadImages) >= 4 && unloadImages[3] != "" && unloadImages[3] != "null" {
			filename := filepath.Join(currentDir, "卸货"+filepath.Ext(unloadImages[3]))
			toolbox.DownloadFile(filename, unloadImages[3])
		}
	}

	zipName := fmt.Sprintf("%s-jiatuo.zip", time.Now().Format("20060102150405"))
	if err := toolbox.Zip(zipName, dir); err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	bucket, err := oss.NewOSSByBucket("cfhszy")
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	object := fmt.Sprintf("poundbill/%s", zipName)
	if err := bucket.PutObjectFromFile(object, zipName); err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	downloadUrl, err := bucket.SignObjectUrl(object)
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	e := &email.Email{
		To:      []string{"<EMAIL>"},
		Subject: "【红山智运】 - " + time.Now().Format("20060102") + "磅单数据",
		Body:    "磅单下载地址：" + downloadUrl,
	}

	if err := e.SendEmail(); err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	// 删除临时文件
	os.RemoveAll(dir)
	os.RemoveAll(zipName)

	logger.Stdout.Info("张家口嘉拓商贸有限公司邮件发送成功")
}

func JiuHui() {
	t := time.Now()
	y := t.Add(-24 * time.Hour)

	rows, err := model.DB.Table("tms_transport_note AS a").
		Joins("JOIN tms_shipper AS b ON a.create_company_id = b.company_id").
		Select([]string{"a.transportation_number", "a.transportation_plate", "a.loading_image", "a.unload_image"}).
		Where("b.company_id = ?", "1780419927125815297").
		Where("a.waybill_status IN (?)", []int{3, 4, 5, 8, 9}).
		Where("a.unload_time BETWEEN ? AND ?", fmt.Sprintf("%s 00:00:00", y.Format("2006-01-02")), fmt.Sprintf("%s 00:00:00", t.Format("2006-01-02"))).
		Rows()
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	dir := "jiuhui"
	if _, err := os.Stat(dir); os.IsNotExist(err) {
		os.Mkdir(dir, 0777)
	}

	for rows.Next() {
		var transportationNumber string
		var transportationPlate string
		var loadingImage string
		var unloadImage string
		if err := rows.Scan(&transportationNumber, &transportationPlate, &loadingImage, &unloadImage); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}

		loadingImages := strings.Split(loadingImage, ",")
		unloadImages := strings.Split(unloadImage, ",")

		currentDir := filepath.Join(dir, transportationNumber+"_"+transportationPlate)
		if _, err := os.Stat(currentDir); os.IsNotExist(err) {
			os.Mkdir(currentDir, 0777)
		}

		if len(loadingImages) > 0 {
			loadingNo := 1
			for _, v := range loadingImages {
				if v == "" || v == "null" {
					continue
				}
				filename := filepath.Join(currentDir, fmt.Sprintf("装货%d%s", loadingNo, filepath.Ext(v)))
				toolbox.DownloadFile(filename, v)
				loadingNo++
			}
		}

		if len(unloadImages) > 0 {
			unloadNo := 1
			for _, v := range unloadImages {
				if v == "" || v == "null" {
					continue
				}
				filename := filepath.Join(currentDir, fmt.Sprintf("卸货%d%s", unloadNo, filepath.Ext(v)))
				toolbox.DownloadFile(filename, v)
				unloadNo++
			}
		}
	}

	zipName := fmt.Sprintf("%s-jiuhui.zip", time.Now().Format("20060102150405"))
	if err := toolbox.Zip(zipName, dir); err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	bucket, err := oss.NewOSSByBucket("cfhszy")
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	object := fmt.Sprintf("poundbill/%s", zipName)
	if err := bucket.PutObjectFromFile(object, zipName); err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	downloadUrl, err := bucket.SignObjectUrl(object)
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	e := &email.Email{
		To:      []string{"<EMAIL>"},
		Subject: "【红山智运】 - " + time.Now().Format("20060102") + "磅单数据",
		Body:    "磅单下载地址：" + downloadUrl,
	}

	if err := e.SendEmail(); err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	// 删除临时文件
	os.RemoveAll(dir)
	os.RemoveAll(zipName)

	logger.Stdout.Info("赤峰玖汇物流有限公司邮件发送成功")
}
