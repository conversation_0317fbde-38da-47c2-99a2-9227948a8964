package tasks

import (
	"database/sql"
	"fmt"

	"os"
	"strconv"
	"strings"
	"time"
	"wlhy/model"
	"wlhy/toolbox/logger"

	"github.com/samber/lo"
	"github.com/xuri/excelize/v2"
)

func SumOfBusiness() {
	start := time.Now().Add(-7*24*time.Hour).Format("2006-01-02 ") + "00:00:00"
	end := time.Now().Format("2006-01-02 ") + "00:00:00"

	// Create a new file.
	f := excelize.NewFile()
	defer func() {
		if err := f.Close(); err != nil {
			logger.Stdout.Error(err.Error())
		}
	}()

	// Create a new sheet.
	index1, err := f.NewSheet("Sheet1")
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	index2, err := f.NewSheet("Sheet2")
	if err != nil {
		logger.Stdout.Error(err.<PERSON>rror())
		return
	}

	rows1, _ := model.DB.Table("tms_transport_note AS a").
		Joins("JOIN tms_shipper AS b ON a.create_company_id = b.company_id").
		Select([]string{"a.id", "a.freight_amount", "a.service_charge", "b.company_name", "a.freight_paid",
			"a.service_fee_paid"}).
		Where("a.waybill_status NOT IN (?)", []int{6, 7}).
		Where("b.shipper_type = ? AND b.is_delete = ?", 1, 0).
		Where("a.create_time BETWEEN ? AND ?", start, end).
		Rows()

	total1 := make(map[string]float64)
	for rows1.Next() {
		var id int
		var freightAmount float64
		var serviceCharge float64
		var companyName string
		var freightPaid float64
		var serviceFeePaid float64
		if err := rows1.Scan(&id, &freightAmount, &serviceCharge, &companyName, &freightPaid, &serviceFeePaid); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}

		if freightPaid > 0 {
			total1[companyName] += (freightPaid + serviceFeePaid)
		} else {
			total1[companyName] += (freightAmount + serviceCharge)
		}
	}

	f.SetActiveSheet(index1)
	f.SetCellValue("Sheet1", "A1", "公司名称")
	f.SetCellValue("Sheet1", "B1", "运费/元")
	startKey := 2
	for k, v := range total1 {
		startS := strconv.Itoa(startKey)
		f.SetCellValue("Sheet1", "A"+startS, k)
		f.SetCellValue("Sheet1", "B"+startS, v)
		startKey++
	}

	rows2, _ := model.DB.Table("tms_transport_note AS a").
		Joins("JOIN tms_shipper AS b ON a.create_company_id = b.company_id").
		Select([]string{"a.id", "a.freight_amount", "a.service_charge", "b.company_name", "a.freight_paid",
			"a.service_fee_paid"}).
		Where("a.waybill_status NOT IN (?)", []int{6, 7}).
		Where("b.shipper_type = ? AND b.is_delete = ?", 1, 0).
		Where("a.create_time BETWEEN ? AND ?", start, end).
		Where("b.create_time BETWEEN ? AND ?", start, end).
		Rows()

	total2 := make(map[string]float64)
	for rows2.Next() {
		var id int
		var freightAmount float64
		var serviceCharge float64
		var companyName string
		var freightPaid float64
		var serviceFeePaid float64
		if err := rows2.Scan(&id, &freightAmount, &serviceCharge, &companyName, &freightPaid, &serviceFeePaid); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}

		if freightPaid > 0 {
			total2[companyName] += (freightPaid + serviceFeePaid)
		} else {
			total2[companyName] += (freightAmount + serviceCharge)
		}
	}

	f.SetActiveSheet(index2)
	f.SetCellValue("Sheet2", "A1", "公司名称")
	f.SetCellValue("Sheet2", "B1", "运费/元")
	startKey = 2
	for k, v := range total2 {
		startS := strconv.Itoa(startKey)
		f.SetCellValue("Sheet2", "A"+startS, k)
		f.SetCellValue("Sheet2", "B"+startS, v)
		startKey++
	}

	f.SetActiveSheet(index1)

	st, _ := time.Parse("2006-01-02", start)
	et, _ := time.Parse("2006-01-02", end)
	xlsxName := fmt.Sprintf("%s-%s营业额数据.xlsx", st.Format("20060102"), et.Format("20060102"))
	if err := f.SaveAs(xlsxName); err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	// 发送文件到飞书指定用户
	fileKey, err := lark.UploadXlsFile(xlsxName)
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	if err := lark.SendFileMessage(lark.ReceivesID.ShanJingBo, fileKey); err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	os.Remove(xlsxName)

	logger.Stdout.Info(fmt.Sprintf("%s-%s营业额数据已发送", st.Format("20060102"), et.Format("20060102")))
}

func SumOfBusinessMonth() {
	prevYear := strings.Split(time.Now().Add(-15*24*time.Hour).Format("2006-01-02"), "-")[0]
	prevMonth := strings.Split(time.Now().Add(-15*24*time.Hour).Format("2006-01-02"), "-")[1]
	start := fmt.Sprintf("%s-%s-01 00:00:00", prevYear, prevMonth)
	end := time.Now().Format("2006-01") + "-01 00:00:00"

	// Create a new file.
	f := excelize.NewFile()
	defer func() {
		if err := f.Close(); err != nil {
			logger.Stdout.Error(err.Error())
		}
	}()

	// Create a new sheet.
	index1, err := f.NewSheet("Sheet1")
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	index2, err := f.NewSheet("Sheet2")
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	rows1, _ := model.DB.Table("tms_transport_note AS a").
		Joins("JOIN tms_shipper AS b ON a.create_company_id = b.company_id").
		Select([]string{"a.id", "a.freight_amount", "a.service_charge", "b.company_name", "a.freight_paid",
			"a.service_fee_paid"}).
		Where("a.waybill_status NOT IN (?)", []int{6, 7}).
		Where("b.shipper_type = ? AND b.is_delete = ?", 1, 0).
		Where("a.create_time BETWEEN ? AND ?", start, end).
		Rows()

	total1 := make(map[string]float64)
	for rows1.Next() {
		var id int
		var freightAmount float64
		var serviceCharge float64
		var companyName string
		var freightPaid float64
		var serviceFeePaid float64
		if err := rows1.Scan(&id, &freightAmount, &serviceCharge, &companyName, &freightPaid, &serviceFeePaid); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}

		if freightPaid > 0 {
			total1[companyName] += (freightPaid + serviceFeePaid)
		} else {
			total1[companyName] += (freightAmount + serviceCharge)
		}
	}

	f.SetActiveSheet(index1)
	f.SetCellValue("Sheet1", "A1", "公司名称")
	f.SetCellValue("Sheet1", "B1", "运费/元")
	startKey := 2
	for k, v := range total1 {
		startS := strconv.Itoa(startKey)
		f.SetCellValue("Sheet1", "A"+startS, k)
		f.SetCellValue("Sheet1", "B"+startS, v)
		startKey++
	}

	rows2, _ := model.DB.Table("tms_transport_note AS a").
		Joins("JOIN tms_shipper AS b ON a.create_company_id = b.company_id").
		Select([]string{"a.id", "a.freight_amount", "a.service_charge", "b.company_name", "a.freight_paid",
			"a.service_fee_paid"}).
		Where("a.waybill_status NOT IN (?)", []int{6, 7}).
		Where("b.shipper_type = ? AND b.is_delete = ?", 1, 0).
		Where("a.create_time BETWEEN ? AND ?", start, end).
		Where("b.create_time BETWEEN ? AND ?", start, end).
		Rows()

	total2 := make(map[string]float64)
	for rows2.Next() {
		var id int
		var freightAmount float64
		var serviceCharge float64
		var companyName string
		var freightPaid float64
		var serviceFeePaid float64
		if err := rows2.Scan(&id, &freightAmount, &serviceCharge, &companyName, &freightPaid, &serviceFeePaid); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}

		if freightPaid > 0 {
			total2[companyName] += (freightPaid + serviceFeePaid)
		} else {
			total2[companyName] += (freightAmount + serviceCharge)
		}
	}

	f.SetActiveSheet(index2)
	f.SetCellValue("Sheet2", "A1", "公司名称")
	f.SetCellValue("Sheet2", "B1", "运费/元")
	startKey = 2
	for k, v := range total2 {
		startS := strconv.Itoa(startKey)
		f.SetCellValue("Sheet2", "A"+startS, k)
		f.SetCellValue("Sheet2", "B"+startS, v)
		startKey++
	}

	f.SetActiveSheet(index1)
	st, _ := time.Parse("2006-01-02", start)
	et, _ := time.Parse("2006-01-02", end)
	xlsxName := fmt.Sprintf("%s-%s月度营业额数据.xlsx", st.Format("200601"), et.Format("200601"))
	if err := f.SaveAs(xlsxName); err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	// 发送文件到飞书指定用户
	fileKey, err := lark.UploadXlsFile(xlsxName)
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	if err := lark.SendFileMessage(lark.ReceivesID.ShanJingBo, fileKey); err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	os.Remove(xlsxName)

	logger.Stdout.Info(fmt.Sprintf("%s-%s月度营业额数据已发送", st.Format("200601"), et.Format("200601")))
}

func AgentFreightAmount() {
	t1 := time.Now().Add(-24 * time.Hour).Format("2006-01-02")
	t2 := time.Now().Format("2006-01-02")

	var agentFreightCompanies []string
	if err := model.DB.Table("tms_shipper").
		Select([]string{"company_name"}).
		Where("shipper_type = ? AND is_delete = ?", 1, 0).
		Where("shipper_from IN (?)", []string{"居间人", "销售直营", "销售+介绍人"}).
		Scan(&agentFreightCompanies).Error; err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	currentYear, _ := time.Parse("2006", time.Now().Format("2006"))
	rows, _ := model.DB.Table("tms_transport_note AS a").
		Joins("JOIN tms_shipper AS b ON a.create_company_id = b.company_id").
		Select([]string{"b.company_name", "a.freight_amount", "a.service_charge", "a.freight_paid",
			"a.service_fee_paid", "a.loading_time"}).
		Where("a.waybill_status NOT IN (?)", []int{6, 7}).
		Where("a.is_region_out = ?", 0).
		Where("b.shipper_type  = ? AND b.is_delete = ?", 1, 0).
		Where("b.company_name IN (?)", agentFreightCompanies).
		Where("a.create_time >= ?", currentYear.Format("2006-01-02 15:04:05")).
		Where("a.create_time <= ?", t2+" 00:00:00").
		Rows()

	com := make(map[string]float64)
	for rows.Next() {
		var companyName string
		var freightAmount float64
		var serviceCharge float64
		var freightPaid sql.NullFloat64
		var serviceFeePaid sql.NullFloat64
		var loadingTime sql.NullTime

		if err := rows.Scan(&companyName, &freightAmount, &serviceCharge, &freightPaid, &serviceFeePaid, &loadingTime); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}

		if loadingTime.Valid && loadingTime.Time.Format("2006-01-02") == t1 {
			if _, ok := com[companyName]; !ok {
				com[companyName] = 0
			}

			amount := freightAmount + serviceCharge
			if freightPaid.Valid && freightPaid.Float64 > 0 {
				amount = freightPaid.Float64 + serviceFeePaid.Float64
			}

			com[companyName] += amount
		}
	}

	f := excelize.NewFile()
	defer f.Close()

	f.SetCellValue("Sheet1", "A1", "公司名称")
	f.SetCellValue("Sheet1", "B1", "运费金额")

	start := 2
	for k, v := range com {
		f.SetCellValue("Sheet1", fmt.Sprintf("A%d", start), k)
		f.SetCellValue("Sheet1", fmt.Sprintf("B%d", start), fmt.Sprintf("%0.2f", v))
		start++
	}

	tt, _ := time.Parse("2006-01-02", t2)
	tt = tt.Add(-24 * time.Hour)
	xlsxName := fmt.Sprintf("%s居间人公司日运费统计.xlsx", tt.Format("20060102"))
	if err := f.SaveAs(xlsxName); err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	// 发送文件到飞书指定用户
	fileKey, err := lark.UploadXlsFile(xlsxName)
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	if err := lark.SendFileMessage(lark.ReceivesID.ShanJingBo, fileKey); err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	os.Remove(xlsxName)

	logger.Stdout.Info(fmt.Sprintf("%s居间人公司日运费统计数据已发送", tt.Format("20060102")))
}

func AgentFreightAmountMonth() {
	prevYear := strings.Split(time.Now().Add(-15*24*time.Hour).Format("2006-01-02"), "-")[0]
	prevMonth := strings.Split(time.Now().Add(-15*24*time.Hour).Format("2006-01-02"), "-")[1]
	startTime := fmt.Sprintf("%s-%s-01 00:00:00", prevYear, prevMonth)
	endTime := time.Now().Format("2006-01") + "-01 00:00:00"

	var agentFreightCompanies []string
	if err := model.DB.Table("tms_shipper").
		Select([]string{"company_name"}).
		Where("shipper_type = ? AND is_delete = ?", 1, 0).
		Where("shipper_from IN (?)", []string{"居间人", "销售直营", "销售+介绍人"}).
		Scan(&agentFreightCompanies).Error; err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	rows, _ := model.DB.Table("tms_invoice_list").
		Select([]string{"invoice_title", "invoice_value"}).
		Where("invoice_title IN (?)", agentFreightCompanies).
		Where("invoice_state = ?", 2).
		Where("is_delete = ?", 0).
		Where("invoice_make_time BETWEEN ? AND ?", startTime, endTime).
		Rows()

	com := make(map[string]float64)
	for rows.Next() {
		var invoiceTitle string
		var invoiceValue float64

		if err := rows.Scan(&invoiceTitle, &invoiceValue); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}

		if _, ok := com[invoiceTitle]; !ok {
			com[invoiceTitle] = 0
		}

		com[invoiceTitle] += invoiceValue
	}

	f := excelize.NewFile()
	defer f.Close()

	f.SetCellValue("Sheet1", "A1", "公司名称")
	f.SetCellValue("Sheet1", "B1", "运费金额")

	start := 2
	for k, v := range com {
		f.SetCellValue("Sheet1", fmt.Sprintf("A%d", start), k)
		f.SetCellValue("Sheet1", fmt.Sprintf("B%d", start), fmt.Sprintf("%0.2f", v))
		start++
	}

	st, _ := time.Parse("2006-01-02", endTime)
	et, _ := time.Parse("2006-01-02", endTime)
	xlsxName := fmt.Sprintf("%s-%s居间人公司月运费统计.xlsx", st.Format("200601"), et.Format("200601"))
	if err := f.SaveAs(xlsxName); err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	// 发送文件到飞书指定用户
	fileKey, err := lark.UploadXlsFile(xlsxName)
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	if err := lark.SendFileMessage(lark.ReceivesID.ShanJingBo, fileKey); err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	os.Remove(xlsxName)

	logger.Stdout.Info(fmt.Sprintf("%s-%s居间人公司月运费统计数据已发送", st.Format("200601"), et.Format("200601")))
}

func MonthFreightAmount() {
	baseTime := time.Now().Format("2006-01")
	if time.Now().Day() == 1 {
		baseTime = time.Now().Add(-24 * time.Hour).Format("2006-01")
	}

	rows, _ := model.DB.Table("tms_transport_note AS a").
		Joins("JOIN tms_shipper AS b ON a.create_company_id = b.company_id").
		Select([]string{"b.company_name", "b.shipper_from", "a.freight_amount", "a.service_charge", "a.freight_paid", "a.service_fee_paid", "a.create_time", "a.finished_time"}).
		Where("a.waybill_status NOT IN (?)", []int{6, 7}).
		Where("a.create_time >= ?", "2024-01-01 00:00:00").
		Where("a.create_time < ?", time.Now().Format("2006-01-02")+" 00:00:00").
		Where("b.shipper_type = ? AND b.is_delete = ?", 1, 0).
		Rows()
	data := make(map[string]map[string]float64)
	from := make(map[string][]string)
	for rows.Next() {
		var companyName string
		var shipperFrom string
		var freightAmount sql.NullFloat64
		var serviceCharge sql.NullFloat64
		var freightPaid sql.NullFloat64
		var serviceFeePaid sql.NullFloat64
		var createTime sql.NullTime
		var finishedTime sql.NullTime
		if err := rows.Scan(&companyName, &shipperFrom, &freightAmount, &serviceCharge, &freightPaid, &serviceFeePaid, &createTime, &finishedTime); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}

		from[shipperFrom] = append(from[shipperFrom], companyName)

		key := companyName
		if _, ok := data[key]; !ok {
			data[key] = map[string]float64{"amount": 0, "paid": 0}
		}

		t1 := createTime.Time.Format("2006-01")
		t2 := finishedTime.Time.Format("2006-01")
		if t1 == baseTime {
			data[key]["amount"] += (freightAmount.Float64 + serviceCharge.Float64)
		}
		if t2 == baseTime {
			data[key]["paid"] += (freightPaid.Float64 + serviceFeePaid.Float64)
		}
	}

	f := excelize.NewFile()
	defer f.Close()

	for k, v := range from {
		from[k] = lo.Uniq(v)
	}

	for shipperFrom, companies := range from {
		f.NewSheet(shipperFrom)
		f.SetCellValue(shipperFrom, "A1", "公司名称")
		f.SetCellValue(shipperFrom, "B1", "公司归属")
		f.SetCellValue(shipperFrom, "C1", "应付运费")
		f.SetCellValue(shipperFrom, "D1", "已支付运费")

		i := 2
		for _, companyName := range companies {
			if _, ok := data[companyName]; ok {
				f.SetCellValue(shipperFrom, "A"+strconv.Itoa(i), companyName)
				f.SetCellValue(shipperFrom, "B"+strconv.Itoa(i), shipperFrom)
				f.SetCellValue(shipperFrom, "C"+strconv.Itoa(i), data[companyName]["amount"])
				f.SetCellValue(shipperFrom, "D"+strconv.Itoa(i), data[companyName]["paid"])
				i++
			}
		}
	}

	bt, _ := time.Parse("2006-01-02", baseTime)
	xlsxName := fmt.Sprintf("%s月度运费统计数据.xlsx", bt.Format("200601"))
	if err := f.SaveAs(xlsxName); err != nil {
		logger.Stdout.Error(err.Error())
	}

	// 发送文件到飞书指定用户
	fileKey, err := lark.UploadXlsFile(xlsxName)
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	receives := []string{lark.ReceivesID.ShanJingBo, lark.ReceivesID.WangBingXin}
	for _, receive := range receives {
		if err := lark.SendFileMessage(receive, fileKey); err != nil {
			logger.Stdout.Error(err.Error())
			return
		}
	}
	os.Remove(xlsxName)

	logger.Stdout.Info(fmt.Sprintf("%s月度运费统计数据已发送", bt.Format("200601")))
}

func FinancePaidAmount() {
	totalAmount := make(map[string]map[string]float64)
	paidAmount := make(map[string]map[string]float64)
	regionOutTotalAmount := make(map[string]map[string]float64)
	regionOutPaidAmount := make(map[string]map[string]float64)

	rows, err := model.DB.Table("tms_transport_note AS a").
		Joins("JOIN tms_shipper AS b ON a.create_company_id = b.company_id").
		Select([]string{
			"a.waybill_status",
			"a.is_region_out",
			"a.freight_paid",
			"a.service_fee_paid",
			"a.freight_amount",
			"a.service_charge",
			"b.company_name",
			"a.pay_time",
			"a.create_time",
		}).
		Where("a.is_delete = ?", 0).
		Where("a.waybill_status IN (?)", []int{1, 2, 3, 4, 5, 8, 9}).
		Where("a.create_time >= ?", "2024-01-01 00:00:00").
		Where("a.create_time < ?", time.Now().Format("2006-01-02")+" 00:00:00").
		Where("b.shipper_type = ?", 1).
		Order("a.create_time DESC").
		Rows()
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	for rows.Next() {
		var waybillStatus int
		var isRegionOut int
		var freightPaid sql.NullFloat64
		var serviceFeePaid sql.NullFloat64
		var freightAmount float64
		var serviceCharge float64
		var companyName string
		var payTime sql.NullTime
		var createTime sql.NullTime

		if err := rows.Scan(&waybillStatus, &isRegionOut, &freightPaid, &serviceFeePaid, &freightAmount,
			&serviceCharge, &companyName, &payTime, &createTime); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}

		if lo.Contains([]int{4, 5, 8}, waybillStatus) && isRegionOut == 0 {
			if payTime.Valid && !payTime.Time.IsZero() {
				t := payTime.Time.Format("2006-01")
				if _, ok := paidAmount[t]; !ok {
					paidAmount[t] = make(map[string]float64)
				}

				paidAmount[t]["freightPaid"] += freightPaid.Float64
				paidAmount[t]["serviceFeePaid"] += serviceFeePaid.Float64
			}
		}

		if lo.Contains([]int{4, 5, 8}, waybillStatus) && isRegionOut == 1 {
			if payTime.Valid && !payTime.Time.IsZero() {
				t := payTime.Time.Format("2006-01")
				if _, ok := regionOutPaidAmount[t]; !ok {
					regionOutPaidAmount[t] = make(map[string]float64)
				}

				regionOutPaidAmount[t]["freightPaid"] += freightPaid.Float64
				regionOutPaidAmount[t]["serviceFeePaid"] += serviceFeePaid.Float64
			}
		}

		if lo.Contains([]int{1, 2, 3, 4, 5, 8, 9}, waybillStatus) && isRegionOut == 0 {
			if createTime.Valid && !createTime.Time.IsZero() {
				t := createTime.Time.Format("2006-01")
				if _, ok := totalAmount[t]; !ok {
					totalAmount[t] = make(map[string]float64)
				}

				var f, s float64
				if lo.Contains([]int{4, 5, 8}, waybillStatus) {
					f = freightPaid.Float64
					s = serviceFeePaid.Float64
				} else {
					f = freightAmount
					s = serviceCharge
				}

				totalAmount[t]["freightPaid"] += f
				totalAmount[t]["serviceFeePaid"] += s
			}
		}

		if lo.Contains([]int{1, 2, 3, 4, 5, 8, 9}, waybillStatus) && isRegionOut == 1 {
			if createTime.Valid && !createTime.Time.IsZero() {
				t := createTime.Time.Format("2006-01")
				if _, ok := regionOutTotalAmount[t]; !ok {
					regionOutTotalAmount[t] = make(map[string]float64)
				}

				var f, s float64
				if lo.Contains([]int{4, 5, 8}, waybillStatus) {
					f = freightPaid.Float64
					s = serviceFeePaid.Float64
				} else {
					f = freightAmount
					s = serviceCharge
				}

				regionOutTotalAmount[t]["freightPaid"] += f
				regionOutTotalAmount[t]["serviceFeePaid"] += s
			}
		}
	}

	sortKey := []string{}
	currentYears := []string{"2024", "2025"}
	monthes := []string{"01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11", "12"}
	for _, v := range currentYears {
		for _, month := range monthes {
			sortKey = append(sortKey, v+"-"+month)
		}
	}

	f := excelize.NewFile()
	defer func() {
		if err := f.Close(); err != nil {
			logger.Stdout.Error(err.Error())
		}
	}()

	_, err = f.NewSheet("实际已收运费")
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	f.SetCellValue("实际已收运费", "A1", "日期")
	f.SetCellValue("实际已收运费", "B1", "已收运费/万元")
	f.SetCellValue("实际已收运费", "C1", "已收服务费/万元")
	f.SetCellValue("实际已收运费", "D1", "已收总运费/万元")

	start := 2
	for _, vv := range sortKey {
		if _, ok := paidAmount[vv]; ok {
			vvv := paidAmount[vv]
			f.SetCellValue("实际已收运费", "A"+strconv.Itoa(start), vv)
			f.SetCellValue("实际已收运费", "B"+strconv.Itoa(start), fmt.Sprintf("%0.2f", vvv["freightPaid"]/10000))
			f.SetCellValue("实际已收运费", "C"+strconv.Itoa(start), fmt.Sprintf("%0.2f", vvv["serviceFeePaid"]/10000))
			f.SetCellValue("实际已收运费", "D"+strconv.Itoa(start), fmt.Sprintf("%0.2f", (vvv["freightPaid"]+vvv["serviceFeePaid"])/10000))
			start++
		}
	}

	_, err = f.NewSheet("理论已收运费")
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	f.SetCellValue("理论已收运费", "A1", "日期")
	f.SetCellValue("理论已收运费", "B1", "已收运费/万元")
	f.SetCellValue("理论已收运费", "C1", "已收服务费/万元")
	f.SetCellValue("理论已收运费", "D1", "已收总运费/万元")

	start = 2
	for _, vv := range sortKey {
		if _, ok := totalAmount[vv]; ok {
			vvv := totalAmount[vv]
			f.SetCellValue("理论已收运费", "A"+strconv.Itoa(start), vv)
			f.SetCellValue("理论已收运费", "B"+strconv.Itoa(start), fmt.Sprintf("%0.2f", vvv["freightPaid"]/10000))
			f.SetCellValue("理论已收运费", "C"+strconv.Itoa(start), fmt.Sprintf("%0.2f", vvv["serviceFeePaid"]/10000))
			f.SetCellValue("理论已收运费", "D"+strconv.Itoa(start), fmt.Sprintf("%0.2f", (vvv["freightPaid"]+vvv["serviceFeePaid"])/10000))
			start++
		}
	}

	_, err = f.NewSheet("实际已收运费(区外)")
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	f.SetCellValue("实际已收运费(区外)", "A1", "日期")
	f.SetCellValue("实际已收运费(区外)", "B1", "已收运费/万元")
	f.SetCellValue("实际已收运费(区外)", "C1", "已收服务费/万元")
	f.SetCellValue("实际已收运费(区外)", "D1", "已收总运费/万元")

	start = 2
	for _, vv := range sortKey {
		if _, ok := regionOutPaidAmount[vv]; ok {
			vvv := regionOutPaidAmount[vv]
			f.SetCellValue("实际已收运费(区外)", "A"+strconv.Itoa(start), vv)
			f.SetCellValue("实际已收运费(区外)", "B"+strconv.Itoa(start), fmt.Sprintf("%0.2f", vvv["freightPaid"]/10000))
			f.SetCellValue("实际已收运费(区外)", "C"+strconv.Itoa(start), fmt.Sprintf("%0.2f", vvv["serviceFeePaid"]/10000))
			f.SetCellValue("实际已收运费(区外)", "D"+strconv.Itoa(start), fmt.Sprintf("%0.2f", (vvv["freightPaid"]+vvv["serviceFeePaid"])/10000))
			start++
		}
	}

	_, err = f.NewSheet("理论已收运费(区外)")
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	f.SetCellValue("理论已收运费(区外)", "A1", "日期")
	f.SetCellValue("理论已收运费(区外)", "B1", "已收运费/万元")
	f.SetCellValue("理论已收运费(区外)", "C1", "已收服务费/万元")
	f.SetCellValue("理论已收运费(区外)", "D1", "已收总运费/万元")

	start = 2
	for _, vv := range sortKey {
		if _, ok := regionOutTotalAmount[vv]; ok {
			vvv := regionOutTotalAmount[vv]
			f.SetCellValue("理论已收运费(区外)", "A"+strconv.Itoa(start), vv)
			f.SetCellValue("理论已收运费(区外)", "B"+strconv.Itoa(start), fmt.Sprintf("%0.2f", vvv["freightPaid"]/10000))
			f.SetCellValue("理论已收运费(区外)", "C"+strconv.Itoa(start), fmt.Sprintf("%0.2f", vvv["serviceFeePaid"]/10000))
			f.SetCellValue("理论已收运费(区外)", "D"+strconv.Itoa(start), fmt.Sprintf("%0.2f", (vvv["freightPaid"]+vvv["serviceFeePaid"])/10000))
			start++
		}
	}

	i, _ := f.GetSheetIndex("实际已收运费")
	f.SetActiveSheet(i)

	tt := time.Now().Add(-24 * time.Hour)
	xlsxName := fmt.Sprintf("%s应收已收运费数据.xlsx", tt.Format("20060102"))
	if err := f.SaveAs(xlsxName); err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	// 发送文件到飞书指定用户
	fileKey, err := lark.UploadXlsFile(xlsxName)
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	if err := lark.SendFileMessage(lark.ReceivesID.YuHongCen, fileKey); err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	os.Remove(xlsxName)
	logger.Stdout.Info(fmt.Sprintf("%s应收已收运费数据已发送", tt.Format("20060102")))
}

// ShangWuMonth 商务月业务量统计
func ShangWuMonth() {
	rows, err := model.DB.Table("tms_shipper AS a").
		Joins("JOIN tms_transport_note AS b ON a.company_id = b.create_company_id").
		Select([]string{"a.company_name", "b.finished_time", "b.freight_paid", "b.service_fee_paid"}).
		Where("a.shipper_type = ? AND b.is_delete = ?", 1, 0).
		Where("a.shipper_from IN (?)", []string{"商务客户转介绍", "商务市场直拓", "项目客户", "宁城子公司", "元宝山子公司",
			"多蒙德项目客户"}).
		Where("b.waybill_status IN (?)", []int{4, 5, 8}).
		Where("b.is_delete = ?", 0).
		Where("b.finished_time BETWEEN ? AND ?", "2025-01-01", time.Now().Format("2006-01")+"-01").
		Rows()
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	mm := make(map[string]map[string]float64)
	var total1, total2, total3, total4, total5, total6, total7, total8, total9, total10, total11, total12 float64
	for rows.Next() {
		var companyName string
		var finishedTime time.Time
		var freightPaid float64
		var serviceFeePaid float64
		if err := rows.Scan(&companyName, &finishedTime, &freightPaid, &serviceFeePaid); err != nil {
			logger.Stdout.Error(err.Error())
			return
		}

		if _, ok := mm[companyName]; !ok {
			mm[companyName] = map[string]float64{
				"1":  0.0,
				"2":  0.0,
				"3":  0.0,
				"4":  0.0,
				"5":  0.0,
				"6":  0.0,
				"7":  0.0,
				"8":  0.0,
				"9":  0.0,
				"10": 0.0,
				"11": 0.0,
				"12": 0.0,
			}
		}

		m := mm[companyName]
		if finishedTime.Month() == time.January {
			m["1"] += freightPaid + serviceFeePaid
			total1 += freightPaid + serviceFeePaid
		}
		if finishedTime.Month() == time.February {
			m["2"] += freightPaid + serviceFeePaid
			total2 += freightPaid + serviceFeePaid
		}
		if finishedTime.Month() == time.March {
			m["3"] += freightPaid + serviceFeePaid
			total3 += freightPaid + serviceFeePaid
		}
		if finishedTime.Month() == time.April {
			m["4"] += freightPaid + serviceFeePaid
			total4 += freightPaid + serviceFeePaid
		}
		if finishedTime.Month() == time.May {
			m["5"] += freightPaid + serviceFeePaid
			total5 += freightPaid + serviceFeePaid
		}
		if finishedTime.Month() == time.June {
			m["6"] += freightPaid + serviceFeePaid
			total6 += freightPaid + serviceFeePaid
		}
		if finishedTime.Month() == time.July {
			m["7"] += freightPaid + serviceFeePaid
			total7 += freightPaid + serviceFeePaid
		}
		if finishedTime.Month() == time.August {
			m["8"] += freightPaid + serviceFeePaid
			total8 += freightPaid + serviceFeePaid
		}
		if finishedTime.Month() == time.September {
			m["9"] += freightPaid + serviceFeePaid
			total9 += freightPaid + serviceFeePaid
		}
		if finishedTime.Month() == time.October {
			m["10"] += freightPaid + serviceFeePaid
			total10 += freightPaid + serviceFeePaid
		}
		if finishedTime.Month() == time.November {
			m["11"] += freightPaid + serviceFeePaid
			total11 += freightPaid + serviceFeePaid
		}
		if finishedTime.Month() == time.December {
			m["12"] += freightPaid + serviceFeePaid
			total12 += freightPaid + serviceFeePaid
		}
		mm[companyName] = m
	}

	f, err := excelize.OpenFile("template/shangwu-month-template.xlsx")
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	defer f.Close()

	start := 4
	for k, v := range mm {
		f.SetCellValue("Sheet1", fmt.Sprintf("A%d", start), k)
		f.SetCellValue("Sheet1", fmt.Sprintf("B%d", start), v["1"])
		f.SetCellValue("Sheet1", fmt.Sprintf("C%d", start), v["2"])
		f.SetCellValue("Sheet1", fmt.Sprintf("D%d", start), v["3"])
		f.SetCellValue("Sheet1", fmt.Sprintf("E%d", start), v["4"])
		f.SetCellValue("Sheet1", fmt.Sprintf("F%d", start), v["5"])
		f.SetCellValue("Sheet1", fmt.Sprintf("G%d", start), v["6"])
		f.SetCellValue("Sheet1", fmt.Sprintf("H%d", start), v["7"])
		f.SetCellValue("Sheet1", fmt.Sprintf("I%d", start), v["8"])
		f.SetCellValue("Sheet1", fmt.Sprintf("J%d", start), v["9"])
		f.SetCellValue("Sheet1", fmt.Sprintf("K%d", start), v["10"])
		f.SetCellValue("Sheet1", fmt.Sprintf("L%d", start), v["11"])
		f.SetCellValue("Sheet1", fmt.Sprintf("M%d", start), v["12"])
		start++
	}

	f.SetCellValue("Sheet1", fmt.Sprintf("A%d", start), "合计")
	f.SetCellValue("Sheet1", fmt.Sprintf("B%d", start), total1)
	f.SetCellValue("Sheet1", fmt.Sprintf("C%d", start), total2)
	f.SetCellValue("Sheet1", fmt.Sprintf("D%d", start), total3)
	f.SetCellValue("Sheet1", fmt.Sprintf("E%d", start), total4)
	f.SetCellValue("Sheet1", fmt.Sprintf("F%d", start), total5)
	f.SetCellValue("Sheet1", fmt.Sprintf("G%d", start), total6)
	f.SetCellValue("Sheet1", fmt.Sprintf("H%d", start), total7)
	f.SetCellValue("Sheet1", fmt.Sprintf("I%d", start), total8)
	f.SetCellValue("Sheet1", fmt.Sprintf("J%d", start), total9)
	f.SetCellValue("Sheet1", fmt.Sprintf("K%d", start), total10)
	f.SetCellValue("Sheet1", fmt.Sprintf("L%d", start), total11)
	f.SetCellValue("Sheet1", fmt.Sprintf("M%d", start), total12)

	if err := f.SaveAs("商务月业务量统计.xlsx"); err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	// 发送文件到飞书指定用户
	fileKey, err := lark.UploadXlsFile("商务月业务量统计.xlsx")
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	if err := lark.SendFileMessage(lark.ReceivesID.WangBingXin, fileKey); err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	os.Remove("商务月业务量统计.xlsx")
	logger.Stdout.Info("商务月业务量统计.xlsx已发送")
}
