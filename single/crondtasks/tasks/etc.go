package tasks

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"math"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"sync"
	"sync/atomic"
	"time"
	"wlhy/model"
	"wlhy/thirdparty/jinron"
	"wlhy/thirdparty/oss"
	"wlhy/toolbox"
	"wlhy/toolbox/email"
	"wlhy/toolbox/logger"

	"github.com/samber/lo"
	"github.com/xuri/excelize/v2"
)

func Etc() {
	startTime := time.Now().Add(-time.Hour*24).Format("2006-01-02") + " 00:00:00"
	endTime := time.Now().Format("2006-01-02") + " 00:00:00"

	e := jinron.NewETC()

	// 实时单处理
	etcWaybill(e, startTime, endTime, 0)
	etcWaybillChunk(e)

	// 获取ETC发票
	etcInvoice(e)

	// 处理已开票运单数据
	// etcProcessInvoices(true)
}

func EtcStatistics() {
	e := jinron.NewETC()
	// 统计数据
	etcStatistics(e)
	etcHistoryStatistics(e)
}

const (
	EtcPending    = 0 // 待处理
	EtcSuccess    = 1 // 处理成功
	EtcStarted    = 2 // 已开始
	EtcFinished   = 3 // 已结束
	EtcFailed     = 4 // 处理失败
	EtcNoInvoice  = 5 // 未开票
	EtcToHistory  = 6 // 转历史单
	EtcToBeChunk  = 7 // 待拆分
	EtcProcessing = 8 // 处理中
)

// EtcStateOptions 等待更新的字段
type EtcStateOptions struct {
	EtcResult         string
	EtcCode           int
	IsException       bool
	EtcFee            *float64
	EtcInvoiceUrl     *string
	EtcInvoiceExplain *string
	EtcIsProcess      *int
}

// changeEtcState 修改etc状态
func changeEtcState(isChunk bool, transportationNumber string, state int, options EtcStateOptions) {
	updateData := map[string]any{
		"is_etc_invoice":   state,
		"etc_waybill_type": 0, // 实时单
		"etc_channel":      2, // 金润
	}

	if options.EtcIsProcess != nil {
		updateData["etc_is_process"] = *options.EtcIsProcess
	}
	if options.EtcFee != nil {
		updateData["etc_fee"] = *options.EtcFee
	}
	if options.EtcInvoiceUrl != nil {
		updateData["etc_invoice_url"] = *options.EtcInvoiceUrl
	}
	if options.EtcInvoiceExplain != nil {
		updateData["etc_invoice_explain"] = *options.EtcInvoiceExplain
	}

	updateData["etc_use_status"] = 0 // 正常
	if options.IsException {
		updateData["etc_use_status"] = 1 // 异常
	}

	if options.EtcResult != "" {
		updateData["etc_result"] = options.EtcResult
	}
	if options.EtcCode != 0 {
		updateData["etc_code"] = options.EtcCode
	}

	// 请求etc渠道商时间
	if state == EtcStarted || state == EtcFinished || options.IsException {
		updateData["etc_apply_time"] = time.Now().Format("2006-01-02 15:04:05")
	}

	// etc流程结束时间
	if state == EtcNoInvoice || state == EtcSuccess {
		updateData["etc_finished_time"] = time.Now().Format("2006-01-02 15:04:05")
	}

	if isChunk {
		if err := model.DB.Table("tms_etc_chunks").
			Where("transportation_number_chunk = ?", transportationNumber).
			Updates(updateData).Error; err != nil {
			logger.Stdout.Error(err.Error())
		}
	} else {
		if err := model.DB.Table("tms_transport_note").
			Where("transportation_number = ?", transportationNumber).
			Updates(updateData).Error; err != nil {
			logger.Stdout.Error(err.Error())
		}
	}
}

// EtcWaybillStartParam 运单参数
type EtcWaybillStartParam struct {
	TransportationNumber   string    `gorm:"column:transportation_number"`    // 运单号
	TransportationPlate    string    `gorm:"column:transportation_plate"`     // 车牌号
	VehicleLicenseColor    string    `gorm:"column:vehicle_license_color"`    // 车牌颜色
	LoadingName            string    `gorm:"column:loading_name"`             // 装货地址省市区
	LoadingAddress         string    `gorm:"column:loading_address"`          // 装货地址
	UnloadName             string    `gorm:"column:unload_name"`              // 卸货地址省市区
	UnloadAddress          string    `gorm:"column:unload_address"`           // 卸货地址
	LoadingTime            time.Time `gorm:"column:loading_time"`             // 装货时间
	UnloadTime             time.Time `gorm:"column:unload_time"`              // 卸货时间
	FreightPaid            float64   `gorm:"column:freight_paid"`             // 已付付款金额
	ServiceFeePaid         float64   `gorm:"column:service_fee_paid"`         // 已付服务费
	TransportationDriver   string    `gorm:"column:transportation_driver"`    // 运输司机名称
	ElectronicAgreementUrl string    `gorm:"column:electronic_agreement_url"` // 电子协议地址
	ActualHaulDistance     float64   `gorm:"column:actual_haul_distance"`     // 实际运输距离
	FreightAmount          float64   `gorm:"column:freight_amount"`           // 应付运费
	ServiceCharge          float64   `gorm:"column:service_charge"`           // 应付服务费
}

// EtcChunk 运单拆分
type EtcChunk struct {
	ID                        int       `gorm:"column:id"`                          // 主键id
	TransportationNumber      string    `gorm:"column:transportation_number"`       // 运单号
	TransportationNumberChunk string    `gorm:"column:transportation_number_chunk"` // 虚拟运单号
	EtcWaybillType            int       `gorm:"column:etc_waybill_type"`            // etc开票运单类型(0-实时运单,1-历史运 2-未开始)
	EtcUseStatus              int       `gorm:"column:etc_use_status"`              // ETC可开票状态，(0-正常,1-异常)
	IsEtcInvoice              int       `gorm:"column:is_etc_invoice"`              // 运单ETC开票状态:0-未开票，1-已开票，2-已调用运单开始接口，3-已调用运单结束接口，4-开票失败，5-开票流程完成但无票
	EtcFee                    float64   `gorm:"column:etc_fee"`                     // 通行费/etc费用
	EtcInvoiceExplain         string    `gorm:"column:etc_invoice_explain"`         // ETC开票说明
	EtcCode                   int       `gorm:"column:etc_code"`                    // 开始上报ETC返回的code
	EtcResult                 string    `gorm:"column:etc_result"`                  // etc返回结果
	EtcInvoiceUrl             string    `gorm:"column:etc_invoice_url"`             // etc发票下载链接，可能会有多张发票
	EtcIsProcess              int       `gorm:"column:etc_is_process"`              // etc数据处理状态，0:未处理，1:已处理
	CreateTime                time.Time `gorm:"column:create_time"`                 // 创建时间
	EtcRequest                string    `gorm:"column:etc_request"`                 // 上报ETC请求数据
	EtcApplyTime              time.Time `gorm:"column:etc_apply_time"`              // ETC申请时间
	EtcFinishedTime           time.Time `gorm:"column:etc_finished_time"`           // ETC完成时间
}

func etcWaybill(e *jinron.ETC, startTime, endTime string, minDistance float64) {
	logger.Stdout.Info("etc waybillStart start")

	// 获取全部拆分运单构建map
	var chunkNotes []EtcChunk
	if err := model.DB.Table("tms_etc_chunks").
		Select("transportation_number_chunk").
		Find(&chunkNotes).Error; err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	chunkNotesMap := make(map[string]bool)
	for _, note := range chunkNotes {
		chunkNotesMap[note.TransportationNumberChunk] = true
	}

	var success, fail, filter int64
	var notes []EtcWaybillStartParam

	if err := model.DB.Table("tms_transport_note AS a").
		Joins("JOIN tms_order_history AS b ON a.order_id = b.id AND a.order_version_no = b.version_no").
		Joins("JOIN tms_vehicle AS c ON a.transportation_plate = c.vehicle_license_number").
		Joins("JOIN tms_electronic_agreement AS d ON a.transportation_number = d.waybill_number AND d.electronic_agreement_type = 1").
		Select([]string{"a.transportation_number", "a.transportation_plate", "c.vehicle_license_color",
			"b.loading_name", "b.loading_address", "b.unload_name", "b.unload_address", "a.loading_time",
			"a.unload_time", "a.freight_paid", "a.service_fee_paid", "a.transportation_driver",
			"d.electronic_agreement_url", "a.actual_haul_distance", "a.freight_amount", "a.service_charge"}).
		Where("a.waybill_status NOT IN (?)", []int{6, 7}).
		Where("a.is_etc_invoice = ?", 0).
		Where("a.etc_use_status = ?", 0).
		Where("a.sys_org_code = ?", "A03").
		Where("a.unload_time BETWEEN ? AND ?", startTime, endTime).
		Where("a.freight_amount >= ?", 1000).
		Order("a.unload_time DESC").
		Find(&notes).Error; err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	// 车辆所有人信息
	vehicleOwnerMap := make(map[string]string)
	vehicleRows, err := model.DB.Table("tms_vehicle").
		Select([]string{"vehicle_license_number", "vehicle_owner"}).
		Where("is_delete = ?", 0).
		Rows()
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	for vehicleRows.Next() {
		var vehicleLicenseNumber string
		var vehicleOwner string
		if err := vehicleRows.Scan(&vehicleLicenseNumber, &vehicleOwner); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}
		vehicleOwnerMap[vehicleLicenseNumber] = vehicleOwner
	}

	var wg sync.WaitGroup
	limit := make(chan bool, 20)

	for _, note := range notes {
		limit <- true
		wg.Add(1)
		go func(note EtcWaybillStartParam) {
			defer func() {
				if r := recover(); r != nil {
					logger.Stdout.Error(fmt.Sprintf("%v", r))
				}
				<-limit
				wg.Done()
			}()

			// 装卸货地址
			loadingAddress := note.LoadingName + strings.ReplaceAll(note.LoadingAddress, note.LoadingName, "")
			unloadAddress := note.UnloadName + strings.ReplaceAll(note.UnloadAddress, note.UnloadName, "")

			// 运费
			fee := 0
			if note.FreightPaid > 0 {
				fee = int(note.FreightPaid+note.ServiceFeePaid) * 100
			} else {
				fee = int(note.FreightAmount+note.ServiceCharge) * 100
			}

			// 根据车辆所有人公司过滤
			if vehicleOwnerMap[note.TransportationPlate] == "敖汉旗中运运输有限公司" {
				atomic.AddInt64(&filter, 1)
				return
			}

			// 实际运输距离小于最小可开票运输距离
			if note.ActualHaulDistance <= minDistance {
				changeEtcState(false, note.TransportationNumber, EtcFailed, EtcStateOptions{
					IsException: true,
					EtcResult:   "实际运输距离小于最小可开票运输距离",
					EtcCode:     0,
				})
				logger.Stdout.Warn(fmt.Sprintf("%s 实际运输距离小于最小可开票运输距离\n", note.TransportationNumber))
				atomic.AddInt64(&fail, 1)
				return
			}

			// 装货时间距离当前时间超过72小时，将运单设置为历史单，由历史单上传功能处理
			if time.Since(note.LoadingTime).Hours() >= 72 {
				changeEtcState(false, note.TransportationNumber, EtcToHistory, EtcStateOptions{
					IsException: false,
					EtcResult:   "装货时间距当前时间超出72小时,转为历史单处理",
					EtcCode:     0,
				})
				logger.Stdout.Warn(fmt.Sprintf("%s 装货时间距当前时间超出72小时,转为历史单处理\n", note.TransportationNumber))
				atomic.AddInt64(&fail, 1)
				return
			}

			// 判断装卸货时间是否超出96小时，若超出96小时则拆分进行上传
			if note.UnloadTime.Sub(note.LoadingTime).Hours() >= 96 {
				changeEtcState(false, note.TransportationNumber, EtcToBeChunk, EtcStateOptions{
					IsException: true,
					EtcResult:   "装卸货时间超出96小时,待拆分处理",
					EtcCode:     0,
				})
				logger.Stdout.Error(fmt.Sprintf("%s 装卸货时间超出96小时,待拆分处理\n", note.TransportationNumber))
				atomic.AddInt64(&fail, 1)

				// 拆分数据并入库
				var chunks []EtcChunk
				endHours := math.Ceil(note.UnloadTime.Sub(note.LoadingTime).Hours() / 72)
				for i := range int(endHours) {
					// 拆分运单已存在
					if _, ok := chunkNotesMap[note.TransportationNumber+strconv.Itoa(i)]; ok {
						continue
					}

					startTime := note.LoadingTime.Add(time.Duration(i) * 72 * time.Hour)
					predictEndTime := note.LoadingTime.Add(time.Duration(i+1) * 72 * time.Hour)
					endTime := note.LoadingTime.Add(time.Duration(i+1) * 72 * time.Hour)

					var etcReqJson []byte
					var etcWaybillType int
					if time.Since(startTime).Hours() > 68 {
						etcWaybillType = 1
						etcReq1 := jinron.WaybillStartReq{
							Num:               note.TransportationNumber + strconv.Itoa(i),
							PlateNum:          note.TransportationPlate,
							PlateColor:        note.VehicleLicenseColor,
							SourceAddr:        loadingAddress,
							DestAddr:          unloadAddress,
							StartTime:         startTime,
							PredictEndTime:    predictEndTime,
							Fee:               fee,
							DriverContractUrl: note.ElectronicAgreementUrl,
						}
						etcReqJson, _ = json.Marshal(etcReq1)
					} else {
						etcWaybillType = 0
						etcReq2 := jinron.WaybillActualFullReq{
							Num:               note.TransportationNumber + strconv.Itoa(i),
							PlateNum:          note.TransportationPlate,
							PlateColor:        note.VehicleLicenseColor,
							StartTime:         startTime,
							SourceAddr:        loadingAddress,
							DestAddr:          unloadAddress,
							PredictEndTime:    predictEndTime,
							Fee:               fee,
							RealDestAddr:      unloadAddress,
							EndTime:           endTime,
							DriverContractUrl: note.ElectronicAgreementUrl,
						}
						etcReqJson, _ = json.Marshal(etcReq2)
					}

					chunks = append(chunks, EtcChunk{
						TransportationNumber:      note.TransportationNumber,
						TransportationNumberChunk: note.TransportationNumber + strconv.Itoa(i),
						EtcWaybillType:            etcWaybillType,
						CreateTime:                time.Now(),
						EtcRequest:                string(etcReqJson),
					})
				}

				if len(chunks) > 0 {
					if err := model.DB.Table("tms_etc_chunks").Create(&chunks).Error; err != nil {
						logger.Stdout.Error(err.Error())
					}
				}

				return
			}

			result, err := e.WaybillActualFull(&jinron.WaybillActualFullReq{
				Num:               note.TransportationNumber,
				PlateNum:          note.TransportationPlate,
				PlateColor:        note.VehicleLicenseColor,
				StartTime:         note.LoadingTime,
				SourceAddr:        loadingAddress,
				DestAddr:          unloadAddress,
				PredictEndTime:    note.LoadingTime.Add(3 * 24 * time.Hour),
				Fee:               fee,
				RealDestAddr:      unloadAddress,
				EndTime:           note.UnloadTime,
				DriverContractUrl: note.ElectronicAgreementUrl,
			})
			if err != nil {
				if strings.Contains(err.Error(), "operation timed out") {
					atomic.AddInt64(&fail, 1)
					return
				}

				changeEtcState(false, note.TransportationNumber, EtcFailed, EtcStateOptions{
					IsException: true,
					EtcResult:   err.Error(),
					EtcCode:     result.Code,
				})
				atomic.AddInt64(&fail, 1)
				return
			}

			changeEtcState(false, note.TransportationNumber, EtcFinished, EtcStateOptions{
				IsException: false,
				EtcResult:   result.Message,
				EtcCode:     result.Code,
			})
			atomic.AddInt64(&success, 1)
		}(note)
	}

	wg.Wait()

	logger.Stdout.Info(fmt.Sprintf("总数:%d,成功:%d,失败:%d,过滤:%d", len(notes), success, fail, filter))
	logger.Stdout.Info("etc waybillStart end")
}

func etcWaybillChunk(e *jinron.ETC) {
	logger.Stdout.Info("etc waybillChunk start")

	var wg sync.WaitGroup
	limit := make(chan bool, 20)

	// 1. 调用etc
	var totalApply, successApply, failApply int64
	var notes []EtcChunk
	if err := model.DB.Table("tms_etc_chunks").
		Where("etc_waybill_type = ?", 0).
		Where("etc_use_status = ?", 0).
		Where("is_etc_invoice = ?", 0).
		Find(&notes).Error; err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	for _, note := range notes {
		wg.Add(1)
		limit <- true
		go func(note EtcChunk) {
			defer func() {
				if r := recover(); r != nil {
					logger.Stdout.Error(fmt.Sprintf("%v", r))
				}
				wg.Done()
				<-limit
			}()

			var req jinron.WaybillActualFullReq
			if err := json.Unmarshal([]byte(note.EtcRequest), &req); err != nil {
				logger.Stdout.Error(err.Error())
				return
			}
			atomic.AddInt64(&totalApply, 1)

			result, err := e.WaybillActualFull(&req)
			if err != nil {
				if strings.Contains(err.Error(), "operation timed out") {
					atomic.AddInt64(&failApply, 1)
					return
				}

				changeEtcState(true, note.TransportationNumberChunk, EtcFailed, EtcStateOptions{
					IsException: true,
					EtcResult:   err.Error(),
					EtcCode:     result.Code,
				})
				logger.Stdout.Error(err.Error())
				atomic.AddInt64(&failApply, 1)
				return
			}

			changeEtcState(true, note.TransportationNumberChunk, EtcFinished, EtcStateOptions{
				IsException: false,
				EtcResult:   result.Message,
				EtcCode:     result.Code,
			})
			atomic.AddInt64(&successApply, 1)
		}(note)
	}
	wg.Wait()

	logger.Stdout.Info(fmt.Sprintf("申请ETC总数:%d,成功:%d,失败:%d,过滤:%d", totalApply, successApply, failApply, totalApply-successApply-failApply))

	// 2. 查询etc
	var totalQuery, successQuery, failQuery, todoQuery int64
	if err := model.DB.Table("tms_etc_chunks").
		Where("etc_waybill_type = ?", 0).
		Where("etc_use_status = ?", 0).
		Where("is_etc_invoice IN (?)", []int{3, 8}).
		Find(&notes).Error; err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	for _, note := range notes {
		wg.Add(1)
		limit <- true
		go func(note EtcChunk) {
			defer func() {
				if r := recover(); r != nil {
					logger.Stdout.Error(fmt.Sprintf("%v", r))
				}
				wg.Done()
				<-limit
			}()

			atomic.AddInt64(&totalQuery, 1)

			result, err := e.QueryInvoice(note.TransportationNumberChunk)
			if err != nil {
				atomic.AddInt64(&failQuery, 1)
				logger.Stdout.Error(fmt.Sprintf("%s %v", note.TransportationNumberChunk, err))
				if strings.Contains(err.Error(), "operation timed out") {
					return
				}

				if strings.Contains(err.Error(), "未查询到运单信息") ||
					strings.Contains(err.Error(), "运单编号错误或该运单不存在") {
					changeEtcState(true, note.TransportationNumberChunk, EtcPending, EtcStateOptions{
						IsException: false,
						EtcResult:   err.Error(),
						EtcCode:     result.Code,
					})
					return
				}

				if strings.Contains(err.Error(), "运单状态未结束不能发起查询") {
					changeEtcState(true, note.TransportationNumberChunk, EtcPending, EtcStateOptions{
						IsException: false,
						EtcResult:   err.Error(),
						EtcCode:     result.Code,
					})
					return
				}
				return
			}

			// 待开票和开票中
			if result.Data.WaybillStatus == 1 || result.Data.WaybillStatus == 2 {
				atomic.AddInt64(&todoQuery, 1)

				resultData, _ := json.Marshal(result)
				if len(result.Data.Result) > 0 {
					etcFee := 0
					etcInvoiceUrls := []string{}
					for _, v := range result.Data.Result {
						etcFee += v.Fee
						etcInvoiceUrls = append(etcInvoiceUrls, v.InvoiceUrl)
					}
					etcInvoiceUrl, _ := json.Marshal(etcInvoiceUrls)

					opEtcFee := float64(etcFee / 100)
					opEtcInvoiceUrl := string(etcInvoiceUrl)
					changeEtcState(true, note.TransportationNumberChunk, EtcProcessing, EtcStateOptions{
						IsException:   false,
						EtcResult:     string(resultData),
						EtcCode:       result.Code,
						EtcFee:        &opEtcFee,
						EtcInvoiceUrl: &opEtcInvoiceUrl,
					})
				} else {
					changeEtcState(true, note.TransportationNumberChunk, EtcProcessing, EtcStateOptions{
						IsException: false,
						EtcResult:   string(resultData),
						EtcCode:     result.Code,
					})
				}
				return
			}

			resultData, _ := json.Marshal(result)

			// 开票完成
			if result.Data.WaybillStatus == 3 {
				atomic.AddInt64(&successQuery, 1)
				// 开票已完成但未开出票
				if len(result.Data.Result) == 0 {
					changeEtcState(true, note.TransportationNumberChunk, EtcNoInvoice, EtcStateOptions{
						IsException: false,
						EtcResult:   string(resultData),
						EtcCode:     result.Code,
					})
					return
				}

				// 开票成功
				etcFee := 0
				etcInvoiceUrls := []string{}
				for _, v := range result.Data.Result {
					etcFee += v.Fee
					etcInvoiceUrls = append(etcInvoiceUrls, v.InvoiceUrl)
				}
				etcInvoiceUrl, _ := json.Marshal(etcInvoiceUrls)

				opEtcFee := float64(etcFee / 100)
				opEtcInvoiceUrl := string(etcInvoiceUrl)
				changeEtcState(true, note.TransportationNumberChunk, EtcSuccess, EtcStateOptions{
					IsException:   false,
					EtcResult:     string(resultData),
					EtcCode:       result.Code,
					EtcFee:        &opEtcFee,
					EtcInvoiceUrl: &opEtcInvoiceUrl,
				})
				return
			}

			// 开票失败
			if result.Data.WaybillStatus == 4 {
				changeEtcState(true, note.TransportationNumberChunk, EtcFailed, EtcStateOptions{
					IsException: false,
					EtcResult:   string(resultData),
					EtcCode:     result.Code,
				})
				logger.Stdout.Error(fmt.Sprintf("%s 开票失败\n", note.TransportationNumberChunk))
				atomic.AddInt64(&failQuery, 1)
			}
		}(note)

	}
	logger.Stdout.Info(fmt.Sprintf("查询ETC总数:%d,已开票:%d,开票失败:%d,开票中:%d", totalQuery, successQuery, failQuery, todoQuery))

	// 3. 处理etc
	var totalProcess int64
	if err := model.DB.Table("tms_etc_chunks").
		Where("etc_waybill_type = ?", 0).
		Where("etc_use_status = ?", 0).
		Where("etc_is_process = ?", 0).
		Find(&notes).Error; err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	batch := make(map[string][]EtcChunk)
	for _, note := range notes {
		batch[note.TransportationNumber] = append(batch[note.TransportationNumber], note)
	}

	// 4. 更新
	for transportationNumber, notes := range batch {
		var originResult jinron.QueryInvoiceResp

		var originalNoteResult string
		model.DB.Table("tms_transport_note").
			Where("transportation_number = ?", transportationNumber).
			Select([]string{"etc_result"}).
			Scan(&originalNoteResult)
		if originalNoteResult != "" && json.Valid([]byte(originalNoteResult)) {
			if err := json.Unmarshal([]byte(originalNoteResult), &originResult); err != nil {
				logger.Stdout.Error(err.Error())
				return
			}
		}

		etcFee := 0
		etcInvoiceUrls := []string{}
		chunkIDs := []int{}
		for k, note := range notes {
			if note.IsEtcInvoice == 1 {
				var result jinron.QueryInvoiceResp
				if err := json.Unmarshal([]byte(note.EtcResult), &result); err != nil {
					logger.Stdout.Error(err.Error())
					continue
				}

				if k == 0 {
					if originResult.Data.WaybillNum == "" {
						originResult = result
						originResult.Data.WaybillNum = transportationNumber
					} else {
						originResult.Data.Result = append(originResult.Data.Result, result.Data.Result...)
					}
				} else {
					originResult.Data.Result = append(originResult.Data.Result, result.Data.Result...)
				}
				chunkIDs = append(chunkIDs, note.ID)
			}
		}

		originResultJson, _ := json.Marshal(originResult)
		etcInvoiceUrl, _ := json.Marshal(etcInvoiceUrls)

		// 去重
		uniM := make(map[string]bool)
		var uni []jinron.QueryInvoiceResult
		for _, v := range originResult.Data.Result {
			if _, ok := uniM[v.InvoiceNum]; ok {
				continue
			}
			uniM[v.InvoiceNum] = true
			uni = append(uni, v)
		}

		originResult.Data.Result = uni
		for _, v := range originResult.Data.Result {
			etcFee += v.Fee
			etcInvoiceUrls = append(etcInvoiceUrls, v.InvoiceUrl)
		}
		opEtcFee := float64(etcFee / 100)
		opEtcInvoiceUrl := string(etcInvoiceUrl)
		changeEtcState(false, transportationNumber, EtcSuccess, EtcStateOptions{
			IsException:   false,
			EtcResult:     string(originResultJson),
			EtcCode:       originResult.Code,
			EtcFee:        &opEtcFee,
			EtcInvoiceUrl: &opEtcInvoiceUrl,
		})

		if err := model.DB.Table("tms_etc_chunks").
			Where("id IN (?)", chunkIDs).
			Updates(map[string]any{
				"etc_is_process": 1,
			}).Error; err != nil {
			logger.Stdout.Error(err.Error())
		}
		totalProcess++
	}

	logger.Stdout.Info(fmt.Sprintf("处理ETC总数:%d", totalProcess))
	logger.Stdout.Info("etc waybillChunk end")
}

func etcInvoice(e *jinron.ETC) {
	logger.Stdout.Info("etc invoice start")

	var total, todo, success, fail int64

	rows, err := model.DB.Table("tms_transport_note").
		Select([]string{"transportation_number"}).
		Where("is_etc_invoice IN (?)", []int{3, 8}).
		Rows()
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	var wg sync.WaitGroup
	limit := make(chan bool, 20)

	for rows.Next() {
		var transportationNumber string

		if err := rows.Scan(&transportationNumber); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}

		limit <- true
		wg.Add(1)
		go func(transportationNumber string) {
			defer func() {
				if r := recover(); r != nil {
					logger.Stdout.Error(fmt.Sprintf("%v", r))
				}
				<-limit
				wg.Done()
			}()

			atomic.AddInt64(&total, 1)

			result, err := e.QueryInvoice(transportationNumber)
			if err != nil {
				atomic.AddInt64(&fail, 1)

				logger.Stdout.Error(fmt.Sprintf("%s %v", transportationNumber, err))
				if strings.Contains(err.Error(), "operation timed out") {
					return
				}
				if strings.Contains(err.Error(), "未查询到运单信息") ||
					strings.Contains(err.Error(), "运单编号错误或该运单不存在") {

					changeEtcState(false, transportationNumber, EtcPending, EtcStateOptions{
						IsException: false,
						EtcResult:   err.Error(),
						EtcCode:     result.Code,
					})
					return
				}

				if strings.Contains(err.Error(), "运单状态未结束不能发起查询") {
					changeEtcState(false, transportationNumber, EtcPending, EtcStateOptions{
						IsException: false,
						EtcResult:   err.Error(),
						EtcCode:     result.Code,
					})
					return
				}
				return
			}

			// 待开票和开票中
			if result.Data.WaybillStatus == 1 || result.Data.WaybillStatus == 2 {
				atomic.AddInt64(&todo, 1)

				resultData, _ := json.Marshal(result)
				if len(result.Data.Result) > 0 {
					etcFee := 0
					etcInvoiceUrls := []string{}
					for _, v := range result.Data.Result {
						etcFee += v.Fee
						etcInvoiceUrls = append(etcInvoiceUrls, v.InvoiceUrl)
					}
					etcInvoiceUrl, _ := json.Marshal(etcInvoiceUrls)

					opEtcFee := float64(etcFee / 100)
					opEtcInvoiceUrl := string(etcInvoiceUrl)
					changeEtcState(false, transportationNumber, EtcProcessing, EtcStateOptions{
						IsException:   false,
						EtcResult:     string(resultData),
						EtcCode:       result.Code,
						EtcFee:        &opEtcFee,
						EtcInvoiceUrl: &opEtcInvoiceUrl,
					})
				} else {
					changeEtcState(false, transportationNumber, EtcProcessing, EtcStateOptions{
						IsException: false,
						EtcResult:   string(resultData),
						EtcCode:     result.Code,
					})
				}

				return
			}

			resultData, _ := json.Marshal(result)

			// 开票完成
			if result.Data.WaybillStatus == 3 {
				atomic.AddInt64(&success, 1)

				// 开票已完成但未开出票
				if len(result.Data.Result) == 0 {
					changeEtcState(false, transportationNumber, EtcNoInvoice, EtcStateOptions{
						IsException: false,
						EtcResult:   string(resultData),
						EtcCode:     result.Code,
					})
					return
				}

				// 开票成功
				etcFee := 0
				etcInvoiceUrls := []string{}
				for _, v := range result.Data.Result {
					etcFee += v.Fee
					etcInvoiceUrls = append(etcInvoiceUrls, v.InvoiceUrl)
				}
				etcInvoiceUrl, _ := json.Marshal(etcInvoiceUrls)

				opEtcFee := float64(etcFee / 100)
				opEtcInvoiceUrl := string(etcInvoiceUrl)
				changeEtcState(false, transportationNumber, EtcSuccess, EtcStateOptions{
					IsException:   false,
					EtcResult:     string(resultData),
					EtcCode:       result.Code,
					EtcFee:        &opEtcFee,
					EtcInvoiceUrl: &opEtcInvoiceUrl,
				})
			}

			// 开票失败
			if result.Data.WaybillStatus == 4 {
				changeEtcState(false, transportationNumber, EtcFailed, EtcStateOptions{
					IsException: false,
					EtcResult:   string(resultData),
					EtcCode:     result.Code,
				})
				logger.Stdout.Error(fmt.Sprintf("%s 开票失败\n", transportationNumber))
				atomic.AddInt64(&fail, 1)
			}
		}(transportationNumber)
	}

	wg.Wait()

	logger.Stdout.Info(fmt.Sprintf("总数:%d,已开票:%d,开票失败:%d,开票中:%d", total, success, fail, todo))
	logger.Stdout.Info("etc invoice end")
}

func etcProcessInvoices(isDownload bool) {
	logger.Stdout.Info("etc processInvoices start")

	rows, err := model.DB.Table("tms_transport_note AS a").
		Joins("JOIN tms_shipper AS b ON a.create_company_id = b.company_id").
		Joins("JOIN tms_order_history AS c ON a.order_id = c.id AND a.order_version_no = c.version_no").
		Select([]string{"a.transportation_number", "b.company_name", "a.freight_paid", "a.service_fee_paid",
			"a.freight_amount", "a.service_charge", "a.finished_time", "a.etc_result", "c.loading_name",
			"c.loading_address", "c.unload_name", "c.unload_address"}).
		Where("a.is_etc_invoice = ?", 1).
		Where("a.etc_is_process = ?", 0).
		Where("b.shipper_type = ? AND b.is_delete = ?", 1, 0).
		Rows()
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	f := excelize.NewFile()
	defer f.Close()

	f.SetCellValue("Sheet1", "A1", "运单号")
	f.SetCellValue("Sheet1", "B1", "公司名称")
	f.SetCellValue("Sheet1", "C1", "司机运费")
	f.SetCellValue("Sheet1", "D1", "服务费")
	f.SetCellValue("Sheet1", "E1", "通行费金额")
	f.SetCellValue("Sheet1", "F1", "通行费税额")
	f.SetCellValue("Sheet1", "G1", "通行费价税合计")
	f.SetCellValue("Sheet1", "H1", "通行费占比")
	f.SetCellValue("Sheet1", "I1", "通行费金额占比")
	f.SetCellValue("Sheet1", "J1", "付款时间")
	f.SetCellValue("Sheet1", "K1", "起运地")
	f.SetCellValue("Sheet1", "L1", "目的地")

	invoices := make(map[string][]string)

	transportationNumbers := []string{}

	start := 2
	for rows.Next() {
		var transportationNumber string
		var companyName string
		var freightPaid sql.NullFloat64
		var serviceFeePaid sql.NullFloat64
		var freightAmount float64
		var serviceCharge float64
		var finishedTime sql.NullTime
		var etcResult string
		var loadingName string
		var loadingAddress string
		var unloadName string
		var unloadAddress string

		if err := rows.Scan(&transportationNumber, &companyName, &freightPaid, &serviceFeePaid, &freightAmount, &serviceCharge, &finishedTime, &etcResult, &loadingName, &loadingAddress, &unloadName, &unloadAddress); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}

		loadingAddress = loadingName + strings.ReplaceAll(loadingAddress, loadingName, "")
		unloadAddress = unloadName + strings.ReplaceAll(unloadAddress, unloadName, "")

		freight := freightAmount
		if freightPaid.Valid && freightPaid.Float64 > 0 {
			freight = freightPaid.Float64
		}

		service := serviceCharge
		if serviceFeePaid.Valid && serviceFeePaid.Float64 > 0 {
			service = serviceFeePaid.Float64
		}

		var result jinron.QueryInvoiceResp
		if err := json.Unmarshal([]byte(etcResult), &result); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}

		fee := 0
		totalTaxAmount := 0
		totalAmount := 0
		for _, v := range result.Data.Result {
			fee += v.Fee
			totalTaxAmount += v.TotalTaxAmount
			totalAmount += v.TotalAmount
		}

		etcFee := float64(fee) / 100
		etcTax := float64(totalTaxAmount) / 100
		etcTotal := float64(totalAmount) / 100

		f.SetCellValue("Sheet1", "A"+strconv.Itoa(start), transportationNumber)
		f.SetCellValue("Sheet1", "B"+strconv.Itoa(start), companyName)
		f.SetCellValue("Sheet1", "C"+strconv.Itoa(start), fmt.Sprintf("%0.2f", freight))
		f.SetCellValue("Sheet1", "D"+strconv.Itoa(start), fmt.Sprintf("%0.2f", service))
		f.SetCellValue("Sheet1", "E"+strconv.Itoa(start), fmt.Sprintf("%0.2f", etcFee))
		f.SetCellValue("Sheet1", "F"+strconv.Itoa(start), fmt.Sprintf("%0.2f", etcTax))
		f.SetCellValue("Sheet1", "G"+strconv.Itoa(start), fmt.Sprintf("%0.2f", etcTotal))
		f.SetCellValue("Sheet1", "H"+strconv.Itoa(start), fmt.Sprintf("%0.2f", etcTotal/freight))
		f.SetCellValue("Sheet1", "I"+strconv.Itoa(start), fmt.Sprintf("%0.2f", etcTax/freight))
		f.SetCellValue("Sheet1", "J"+strconv.Itoa(start), finishedTime.Time.Format("2006-01-02 15:04:05"))
		f.SetCellValue("Sheet1", "K"+strconv.Itoa(start), loadingAddress)
		f.SetCellValue("Sheet1", "L"+strconv.Itoa(start), unloadAddress)

		start++

		if isDownload {
			for _, v := range result.Data.Result {
				invoices[companyName] = append(invoices[companyName], v.InvoiceNum+"|"+v.InvoiceUrl)
			}
		}
		transportationNumbers = append(transportationNumbers, transportationNumber)
	}

	if len(transportationNumbers) == 0 {
		logger.Stdout.Info("etc invoice no data")
		return
	}

	excelName := "ETC发票统计数据.xlsx"
	if err := f.SaveAs(excelName); err != nil {
		logger.Stdout.Error(err.Error())
	}

	fileUrl := ""
	dir := "ETC发票"
	zipName := ""
	if isDownload {
		for k, v := range invoices {
			currentDir := filepath.Join(dir, k)
			if _, err := os.Stat(currentDir); os.IsNotExist(err) {
				if err := os.MkdirAll(currentDir, 0777); err != nil {
					logger.Stdout.Error(err.Error())
				}
			}

			for _, vv := range v {
				t := strings.Split(vv, "|")
				filename := filepath.Join(currentDir, t[0]+".pdf")
				if err := toolbox.DownloadFile(filename, t[1]); err != nil {
					logger.Stdout.Error(err.Error())
				}
			}
		}

		zipName = fmt.Sprintf("%s-ETC发票.zip", time.Now().Format("20060102150405"))
		if err := toolbox.Zip(zipName, dir); err != nil {
			logger.Stdout.Error(err.Error())
			return
		}

		bucket, err := oss.NewOSSByBucket("cfhszy")
		if err != nil {
			logger.Stdout.Error(err.Error())
			return
		}
		object := fmt.Sprintf("etc/%s", zipName)
		if err = bucket.PutObjectFromFile(object, zipName); err != nil {
			logger.Stdout.Error(err.Error())
			return
		}
		fileUrl, err = bucket.SignObjectUrl(object)
		if err != nil {
			logger.Stdout.Error(err.Error())
			return
		}

		// 删除临时文件
		os.RemoveAll(dir)
		os.RemoveAll(zipName)
	}

	if len(transportationNumbers) > 0 {
		if err := model.DB.Table("tms_transport_note").
			Where("transportation_number IN (?)", transportationNumbers).
			Updates(map[string]any{
				"etc_is_process": 1,
			}).Error; err != nil {
			logger.Stdout.Error(err.Error())
		}
	}

	body := ""
	if fileUrl != "" {
		body = "ETC发票下载地址：" + fileUrl
	}
	e := &email.Email{
		To:      []string{"<EMAIL>"},
		Subject: "【红山智运】 - " + time.Now().Format("20060102") + "ETC发票数据",
		Body:    body,
		Attach:  "ETC发票统计数据.xlsx",
	}

	if err := e.SendEmail(); err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	// 删除临时文件
	os.RemoveAll(excelName)

	logger.Stdout.Info("etc processInvoices end")
}

func etcStatistics(e *jinron.ETC) {
	logger.Stdout.Info("etc statistics start")

	beginTime := time.Now().Add(-time.Duration(24*7) * time.Hour).Format("2006-01-02")

	rows, err := model.DB.Debug().Table("tms_transport_note AS a").
		Joins("JOIN tms_shipper AS b ON a.create_company_id = b.company_id").
		Select([]string{"b.company_name", "a.transportation_number", "a.freight_amount", "a.service_charge",
			"a.loading_address", "a.unload_address", "a.etc_apply_time", "a.etc_result"}).
		Where("a.is_etc_invoice IN (?)", []int{1, 8}).
		Where("a.etc_use_status = ?", 0).
		Where("a.etc_waybill_type = ?", 0).
		Where("a.loading_time >= ?", beginTime).
		Where("b.shipper_type = 1 AND b.is_delete = 0").
		Order("a.loading_time ASC").
		Rows()
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	f := excelize.NewFile()
	defer f.Close()

	var start int64 = 2
	f.SetCellValue("Sheet1", "A1", "客户公司")
	f.SetCellValue("Sheet1", "B1", "运单号")
	f.SetCellValue("Sheet1", "C1", "运费")
	f.SetCellValue("Sheet1", "D1", "服务费")
	f.SetCellValue("Sheet1", "E1", "开票状态")
	f.SetCellValue("Sheet1", "F1", "运单开始时间")
	f.SetCellValue("Sheet1", "G1", "运单结束时间")
	f.SetCellValue("Sheet1", "H1", "ETC发票申请时间")
	f.SetCellValue("Sheet1", "I1", "车牌号")
	f.SetCellValue("Sheet1", "J1", "开票时间")
	f.SetCellValue("Sheet1", "K1", "交易金额")
	f.SetCellValue("Sheet1", "L1", "金额")
	f.SetCellValue("Sheet1", "M1", "税率")
	f.SetCellValue("Sheet1", "N1", "税额")
	f.SetCellValue("Sheet1", "O1", "价税合计")
	f.SetCellValue("Sheet1", "P1", "发票号码")

	var wg sync.WaitGroup
	var mu sync.Mutex
	limit := make(chan bool, 20)
	for rows.Next() {
		var companyName string
		var transportationNumber string
		var freightAmount float64
		var serviceCharge float64
		var loadingAddress string
		var unloadAddress string
		var etcApplyTime sql.NullTime
		var etcResult sql.NullString

		if err := rows.Scan(&companyName, &transportationNumber, &freightAmount, &serviceCharge, &loadingAddress, &unloadAddress, &etcApplyTime, &etcResult); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}
		if !etcResult.Valid {
			continue
		}

		limit <- true
		wg.Add(1)
		go func(companyName, transportationNumber, loadingAddress, unloadAddress string, freightAmount, serviceCharge float64, etcResult string, etcApplyTime sql.NullTime) {
			defer func() {
				if r := recover(); r != nil {
					logger.Stdout.Error(fmt.Sprintf("%v", r))
				}
				wg.Done()
				<-limit
			}()

			var result *jinron.QueryInvoiceResp
			if etcResult != "" && json.Valid([]byte(etcResult)) {
				if err := json.Unmarshal([]byte(etcResult), &result); err != nil {
					logger.Stdout.Error(err.Error())
					return
				}
			} else {
				result, err = e.QueryInvoice(transportationNumber)
				if err != nil {
					logger.Stdout.Error(err.Error())
					return
				}
			}

			if lo.Contains([]int{1, 2, 3}, result.Data.WaybillStatus) && len(result.Data.Result) > 0 {
				for _, v := range result.Data.Result {
					mu.Lock()

					f.SetCellValue("Sheet1", fmt.Sprintf("A%d", start), companyName)
					f.SetCellValue("Sheet1", fmt.Sprintf("B%d", start), transportationNumber)
					f.SetCellValue("Sheet1", fmt.Sprintf("C%d", start), freightAmount)
					f.SetCellValue("Sheet1", fmt.Sprintf("D%d", start), serviceCharge)

					waybillStatusDisplay := "已开票"
					if v.WaybillStatus == 1 || v.WaybillStatus == 2 {
						waybillStatusDisplay = "开票中"
					} else if v.WaybillStatus == 4 {
						waybillStatusDisplay = "开票失败"
					}
					f.SetCellValue("Sheet1", fmt.Sprintf("E%d", start), waybillStatusDisplay)
					f.SetCellValue("Sheet1", fmt.Sprintf("F%d", start), v.WaybillStartTime)
					f.SetCellValue("Sheet1", fmt.Sprintf("G%d", start), v.WaybillEndTime)

					applyTime := ""
					if etcApplyTime.Valid {
						applyTime = etcApplyTime.Time.Format("2006-01-02 15:04:05")
					}
					f.SetCellValue("Sheet1", fmt.Sprintf("H%d", start), applyTime)

					f.SetCellValue("Sheet1", fmt.Sprintf("I%d", start), v.PlateNum)
					f.SetCellValue("Sheet1", fmt.Sprintf("J%d", start), v.InvoiceMakeTime)
					f.SetCellValue("Sheet1", fmt.Sprintf("K%d", start), float64(v.Fee)/100)
					f.SetCellValue("Sheet1", fmt.Sprintf("L%d", start), float64(v.Amount)/100)
					f.SetCellValue("Sheet1", fmt.Sprintf("M%d", start), v.TaxRate)
					f.SetCellValue("Sheet1", fmt.Sprintf("N%d", start), float64(v.TotalTaxAmount)/100)
					f.SetCellValue("Sheet1", fmt.Sprintf("O%d", start), float64(v.TotalAmount)/100)
					f.SetCellValue("Sheet1", fmt.Sprintf("P%d", start), v.InvoiceNum)
					start++
					mu.Unlock()
				}
			}
		}(companyName, transportationNumber, loadingAddress, unloadAddress, freightAmount, serviceCharge, etcResult.String, etcApplyTime)
	}
	wg.Wait()

	xlsxName := fmt.Sprintf("%s7日ETC发票统计数据.xlsx", time.Now().Format("20060102"))
	if err := f.SaveAs(xlsxName); err != nil {
		logger.Stdout.Error(err.Error())
	}

	// 发送文件到飞书指定用户
	fileKey, err := lark.UploadXlsFile(xlsxName)
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	if err := lark.SendFileMessage(lark.ReceivesID.DeJiaNing, fileKey); err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	os.Remove(xlsxName)

	logger.Stdout.Info(fmt.Sprintf("%s7日ETC发票统计数据已发送", time.Now().Format("20060102")))

	logger.Stdout.Info("etc statistics end")
}

func etcHistoryStatistics(e *jinron.ETC) {
	logger.Stdout.Info("etc history statistics start")

	beginTime := time.Now().Add(-time.Duration(24*7) * time.Hour).Format("2006-01-02")
	finishTime := time.Now().Format("2006-01-02")

	rows, err := model.DB.Debug().Table("tms_transport_note AS a").
		Joins("JOIN tms_shipper AS b ON a.create_company_id = b.company_id").
		Select([]string{"b.company_name", "a.transportation_number", "a.freight_amount", "a.service_charge",
			"a.loading_address", "a.unload_address", "a.etc_apply_time", "a.etc_result"}).
		Where("a.is_etc_invoice IN (?)", []int{1, 8}).
		Where("a.etc_use_status = ?", 0).
		Where("a.etc_waybill_type = ?", 1).
		Where("a.etc_apply_time BETWEEN ? AND ?", beginTime, finishTime).
		Where("b.shipper_type = 1 AND b.is_delete = 0").
		Order("a.loading_time ASC").
		Rows()
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	f := excelize.NewFile()
	defer f.Close()

	var start int64 = 2
	f.SetCellValue("Sheet1", "A1", "客户公司")
	f.SetCellValue("Sheet1", "B1", "运单号")
	f.SetCellValue("Sheet1", "C1", "运费")
	f.SetCellValue("Sheet1", "D1", "服务费")
	f.SetCellValue("Sheet1", "E1", "开票状态")
	f.SetCellValue("Sheet1", "F1", "运单开始时间")
	f.SetCellValue("Sheet1", "G1", "运单结束时间")
	f.SetCellValue("Sheet1", "H1", "ETC发票申请时间")
	f.SetCellValue("Sheet1", "I1", "车牌号")
	f.SetCellValue("Sheet1", "J1", "开票时间")
	f.SetCellValue("Sheet1", "K1", "交易金额")
	f.SetCellValue("Sheet1", "L1", "金额")
	f.SetCellValue("Sheet1", "M1", "税率")
	f.SetCellValue("Sheet1", "N1", "税额")
	f.SetCellValue("Sheet1", "O1", "价税合计")

	var wg sync.WaitGroup
	var mu sync.Mutex
	limit := make(chan bool, 20)
	for rows.Next() {
		var companyName string
		var transportationNumber string
		var freightAmount float64
		var serviceCharge float64
		var loadingAddress string
		var unloadAddress string
		var etcApplyTime sql.NullTime
		var etcResult sql.NullString

		if err := rows.Scan(&companyName, &transportationNumber, &freightAmount, &serviceCharge, &loadingAddress, &unloadAddress, &etcApplyTime, &etcResult); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}
		if !etcResult.Valid {
			continue
		}

		limit <- true
		wg.Add(1)
		go func(companyName, transportationNumber, loadingAddress, unloadAddress string, freightAmount, serviceCharge float64, etcResult string, etcApplyTime sql.NullTime) {
			defer func() {
				if r := recover(); r != nil {
					logger.Stdout.Error(fmt.Sprintf("%v", r))
				}
				wg.Done()
				<-limit
			}()

			var result *jinron.QueryInvoiceResp
			if etcResult != "" && json.Valid([]byte(etcResult)) {
				if err := json.Unmarshal([]byte(etcResult), &result); err != nil {
					logger.Stdout.Error(err.Error())
					return
				}
			} else {
				result, err = e.QueryInvoice(transportationNumber)
				if err != nil {
					logger.Stdout.Error(fmt.Sprintf("%s %v", transportationNumber, err))
					return
				}
			}

			if lo.Contains([]int{1, 2, 3}, result.Data.WaybillStatus) && len(result.Data.Result) > 0 {
				for _, v := range result.Data.Result {
					mu.Lock()

					f.SetCellValue("Sheet1", fmt.Sprintf("A%d", start), companyName)
					f.SetCellValue("Sheet1", fmt.Sprintf("B%d", start), transportationNumber)
					f.SetCellValue("Sheet1", fmt.Sprintf("C%d", start), freightAmount)
					f.SetCellValue("Sheet1", fmt.Sprintf("D%d", start), serviceCharge)

					waybillStatusDisplay := "已开票"
					if v.WaybillStatus == 1 || v.WaybillStatus == 2 {
						waybillStatusDisplay = "开票中"
					} else if v.WaybillStatus == 4 {
						waybillStatusDisplay = "开票失败"
					}
					f.SetCellValue("Sheet1", fmt.Sprintf("E%d", start), waybillStatusDisplay)
					f.SetCellValue("Sheet1", fmt.Sprintf("F%d", start), v.WaybillStartTime)
					f.SetCellValue("Sheet1", fmt.Sprintf("G%d", start), v.WaybillEndTime)

					applyTime := ""
					if etcApplyTime.Valid {
						applyTime = etcApplyTime.Time.Format("2006-01-02 15:04:05")
					}
					f.SetCellValue("Sheet1", fmt.Sprintf("H%d", start), applyTime)

					f.SetCellValue("Sheet1", fmt.Sprintf("I%d", start), v.PlateNum)
					f.SetCellValue("Sheet1", fmt.Sprintf("J%d", start), v.InvoiceMakeTime)
					f.SetCellValue("Sheet1", fmt.Sprintf("K%d", start), float64(v.Fee)/100)
					f.SetCellValue("Sheet1", fmt.Sprintf("L%d", start), float64(v.Amount)/100)
					f.SetCellValue("Sheet1", fmt.Sprintf("M%d", start), v.TaxRate)
					f.SetCellValue("Sheet1", fmt.Sprintf("N%d", start), float64(v.TotalTaxAmount)/100)
					f.SetCellValue("Sheet1", fmt.Sprintf("O%d", start), float64(v.TotalAmount)/100)
					start++
					mu.Unlock()
				}
			}
		}(companyName, transportationNumber, loadingAddress, unloadAddress, freightAmount, serviceCharge, etcResult.String, etcApplyTime)
	}
	wg.Wait()

	xlsxName := fmt.Sprintf("%s7日历史ETC发票统计数据.xlsx", time.Now().Format("20060102"))
	if err := f.SaveAs(xlsxName); err != nil {
		logger.Stdout.Error(err.Error())
	}

	// 发送文件到飞书指定用户
	fileKey, err := lark.UploadXlsFile(xlsxName)
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	if err := lark.SendFileMessage(lark.ReceivesID.DeJiaNing, fileKey); err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	os.Remove(xlsxName)

	logger.Stdout.Info(fmt.Sprintf("%s7日历史ETC发票统计数据已发送", time.Now().Format("20060102")))

	logger.Stdout.Info("etc history statistics end")
}
