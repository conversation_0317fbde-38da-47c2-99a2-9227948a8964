package tasks

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"math"
	"math/rand/v2"
	"os"
	"strconv"
	"time"
	"wlhy/model"
	"wlhy/toolbox"
	"wlhy/toolbox/logger"

	"github.com/samber/lo"
	"github.com/xuri/excelize/v2"
	"go.mongodb.org/mongo-driver/bson"
)

// 统计结构体
type Stat struct {
	NoteNum          int     `json:"note_num"`
	NoteAmount       float64 `json:"note_amount"`
	NoteHighPriceNum int     `json:"note_high_price_num"` // 运价异常
	NoteTimeNum      int     `json:"note_time_num"`       // 运输时间异常
	NotePoundNum     int     `json:"note_pound_num"`      // 磅单扣减
	NoteOverloadNum  int     `json:"note_overload_num"`   // 超载扣减
	NoteAddressNum   int     `json:"note_address_num"`    // 地址异常
	Risk10Freight    float64 `json:"risk10_freight"`      // 10公里业务占比
}

// 日级风控报表
func RiskReports() {
	startTime := time.Now()

	rows, _ := model.DB.Table("tms_shipper").
		Select([]string{"company_name", "intermediary_name"}).
		Where("shipper_type = ? AND is_delete = ?", 1, 0).
		Where("intermediary_name IS NOT NULL").
		Rows()

	sortIntermediaryName := []string{}
	allCompanies := make(map[string][]string)
	filterCompanies := []string{}
	dayCompanyStats := make(map[string]Stat)
	for rows.Next() {
		var companyName string
		var intermediaryName string
		if err := rows.Scan(&companyName, &intermediaryName); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}
		sortIntermediaryName = append(sortIntermediaryName, intermediaryName)
		allCompanies[intermediaryName] = append(allCompanies[intermediaryName], companyName)
		filterCompanies = append(filterCompanies, companyName)
		if _, ok := dayCompanyStats[companyName]; !ok {
			dayCompanyStats[companyName] = Stat{}
		}
	}
	sortIntermediaryName = lo.Uniq(sortIntermediaryName)

	// 数据查询时间区间
	dayStart := time.Now().Add(-24 * time.Hour).Format("2006-01-02")
	dayEnd := time.Now().Format("2006-01-02")
	dayCompanyStats, err := stats(dayCompanyStats, filterCompanies, dayStart, dayEnd)
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	f, err := excelize.OpenFile("template/risk_template.xlsx")
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	defer f.Close()

	// 统计分数，标记颜色
	cellBankground := make(map[string]any)
	averageScore := make(map[string]map[string]float64)
	for _, v := range sortIntermediaryName {
		if _, ok := averageScore[v]; !ok {
			averageScore[v] = make(map[string]float64)
		}

		for _, companyName := range allCompanies[v] {
			// 运价异常扣减分数
			d1 := deductionsScore1(dayCompanyStats[companyName].NoteHighPriceNum, dayCompanyStats[companyName].NoteNum, 1)
			// 运输时间异常扣减分数
			d2 := deductionsScore1(dayCompanyStats[companyName].NoteTimeNum, dayCompanyStats[companyName].NoteNum, 1)
			// 磅单扣减分数
			d3 := deductionsScore2(dayCompanyStats[companyName].NotePoundNum, dayCompanyStats[companyName].NoteNum)
			// 超载扣减分数
			d4 := deductionsScore3(dayCompanyStats[companyName].NoteOverloadNum, dayCompanyStats[companyName].NoteNum)
			// 地址异常扣减分数
			d5 := deductionsScore3(dayCompanyStats[companyName].NoteAddressNum, dayCompanyStats[companyName].NoteNum)

			currentScore := float64(100 - d1 - d2 - d3 - d4 - d5)

			averageScore[v]["totalScore"] += currentScore
			averageScore[v]["count"] += 1

			if dayCompanyStats[companyName].NoteNum > 0 {
				averageScore[v]["avgTotal"] += currentScore
				averageScore[v]["avgCount"] += 1
			}

			if currentScore <= 85 {
				cellBankground[companyName] = "redBg"
			} else if currentScore <= 90 {
				cellBankground[companyName] = "yellowBg"
			}
		}
	}

	redBg, _ := f.NewStyle(&excelize.Style{
		Border: []excelize.Border{
			{Type: "left", Color: "#000000", Style: 1},
			{Type: "right", Color: "#000000", Style: 1},
			{Type: "top", Color: "#000000", Style: 1},
			{Type: "bottom", Color: "#000000", Style: 1},
		},
		Alignment: &excelize.Alignment{
			Horizontal: "left",
			Vertical:   "center",
			WrapText:   true,
		},
		// 设置背景色
		Fill: excelize.Fill{
			Type:    "pattern",
			Pattern: 1,
			Color:   []string{"FE0202"},
		},
	})
	yellowBg, _ := f.NewStyle(&excelize.Style{
		Border: []excelize.Border{
			{Type: "left", Color: "#000000", Style: 1},
			{Type: "right", Color: "#000000", Style: 1},
			{Type: "top", Color: "#000000", Style: 1},
			{Type: "bottom", Color: "#000000", Style: 1},
		},
		Alignment: &excelize.Alignment{
			Horizontal: "left",
			Vertical:   "center",
			WrapText:   true,
		},
		// 设置背景色
		Fill: excelize.Fill{
			Type:    "pattern",
			Pattern: 1,
			Color:   []string{"F1C024"},
		},
	})

	// 日级数据
	cellIndex := 6
	for _, v := range sortIntermediaryName {
		avg := 0.0
		if averageScore[v]["avgCount"] != 0 {
			avg = averageScore[v]["avgTotal"] / averageScore[v]["avgCount"]
		}

		for _, companyName := range allCompanies[v] {

			// 总分
			f.SetCellValue("Sheet1", "A"+strconv.Itoa(cellIndex), 100)
			// 公司名称
			f.SetCellValue("Sheet1", "B"+strconv.Itoa(cellIndex), companyName)
			if cellBankground[companyName] == "redBg" {
				f.SetCellStyle("Sheet1", "B"+strconv.Itoa(cellIndex), "B"+strconv.Itoa(cellIndex), redBg)
			}
			if cellBankground[companyName] == "yellowBg" {
				f.SetCellStyle("Sheet1", "B"+strconv.Itoa(cellIndex), "B"+strconv.Itoa(cellIndex), yellowBg)
			}

			// 分组
			f.SetCellValue("Sheet1", "D"+strconv.Itoa(cellIndex), v)
			// 客户平均分 TODO
			f.SetCellValue("Sheet1", "E"+strconv.Itoa(cellIndex), fmt.Sprintf("%.0f", avg))

			// 接单总数
			f.SetCellValue("Sheet1", "F"+strconv.Itoa(cellIndex), dayCompanyStats[companyName].NoteNum)
			// 总金额
			f.SetCellValue("Sheet1", "G"+strconv.Itoa(cellIndex), fmt.Sprintf("%.2f", float64(dayCompanyStats[companyName].NoteAmount)/10000))
			// 合计异常单数
			f.SetCellValue("Sheet1", "H"+strconv.Itoa(cellIndex), dayCompanyStats[companyName].NoteHighPriceNum+dayCompanyStats[companyName].NoteTimeNum+dayCompanyStats[companyName].NotePoundNum+dayCompanyStats[companyName].NoteOverloadNum)
			// 当日异常处理率,默认为100%
			f.SetCellValue("Sheet1", "I"+strconv.Itoa(cellIndex), "100%")
			// 单价过高数量
			f.SetCellValue("Sheet1", "J"+strconv.Itoa(cellIndex), dayCompanyStats[companyName].NoteHighPriceNum)
			// 装卸货地址不符数量
			f.SetCellValue("Sheet1", "K"+strconv.Itoa(cellIndex), dayCompanyStats[companyName].NoteAddressNum)
			// 运输时间异常数量
			f.SetCellValue("Sheet1", "L"+strconv.Itoa(cellIndex), dayCompanyStats[companyName].NoteTimeNum)

			// 运价异常数量占比
			if dayCompanyStats[companyName].NoteHighPriceNum == 0 {
				f.SetCellValue("Sheet1", "M"+strconv.Itoa(cellIndex), 0)
			} else {
				f.SetCellValue("Sheet1", "M"+strconv.Itoa(cellIndex), fmt.Sprintf("%0.2f%%", float64(dayCompanyStats[companyName].NoteHighPriceNum)/float64(dayCompanyStats[companyName].NoteNum)*100))
			}
			// 装卸货地址不符占比
			if dayCompanyStats[companyName].NoteAddressNum == 0 {
				f.SetCellValue("Sheet1", "N"+strconv.Itoa(cellIndex), 0)
			} else {
				f.SetCellValue("Sheet1", "N"+strconv.Itoa(cellIndex), fmt.Sprintf("%0.2f%%", float64(dayCompanyStats[companyName].NoteAddressNum)/float64(dayCompanyStats[companyName].NoteNum)*100))
			}
			// 运输时间异常占比
			if dayCompanyStats[companyName].NoteTimeNum == 0 {
				f.SetCellValue("Sheet1", "O"+strconv.Itoa(cellIndex), 0)
			} else {
				f.SetCellValue("Sheet1", "O"+strconv.Itoa(cellIndex), fmt.Sprintf("%0.2f%%", float64(dayCompanyStats[companyName].NoteTimeNum)/float64(dayCompanyStats[companyName].NoteNum)*100))
			}

			// 运价异常扣减分数
			d1 := deductionsScore1(dayCompanyStats[companyName].NoteHighPriceNum, dayCompanyStats[companyName].NoteNum, 1)
			f.SetCellValue("Sheet1", "P"+strconv.Itoa(cellIndex), d1)
			// 装卸货地址不符扣减分数
			d5 := deductionsScore1(dayCompanyStats[companyName].NoteAddressNum, dayCompanyStats[companyName].NoteNum, 1)
			f.SetCellValue("Sheet1", "Q"+strconv.Itoa(cellIndex), d5)
			// 运输时间异常扣减分数
			d2 := deductionsScore1(dayCompanyStats[companyName].NoteTimeNum, dayCompanyStats[companyName].NoteNum, 1)
			f.SetCellValue("Sheet1", "R"+strconv.Itoa(cellIndex), d2)

			// 磅单异常数量
			f.SetCellValue("Sheet1", "S"+strconv.Itoa(cellIndex), dayCompanyStats[companyName].NotePoundNum)

			// 磅单异常占比
			if dayCompanyStats[companyName].NotePoundNum == 0 {
				f.SetCellValue("Sheet1", "T"+strconv.Itoa(cellIndex), 0)
			} else {
				f.SetCellValue("Sheet1", "T"+strconv.Itoa(cellIndex), fmt.Sprintf("%0.2f%%", float64(dayCompanyStats[companyName].NotePoundNum)/float64(dayCompanyStats[companyName].NoteNum)*100))
			}

			// 磅单扣减分数
			d3 := deductionsScore2(dayCompanyStats[companyName].NotePoundNum, dayCompanyStats[companyName].NoteNum)
			f.SetCellValue("Sheet1", "U"+strconv.Itoa(cellIndex), d3)

			// 超载异常数量
			f.SetCellValue("Sheet1", "V"+strconv.Itoa(cellIndex), dayCompanyStats[companyName].NoteOverloadNum)
			// 超载异常占比
			if dayCompanyStats[companyName].NoteOverloadNum == 0 {
				f.SetCellValue("Sheet1", "W"+strconv.Itoa(cellIndex), 0)
			} else {
				f.SetCellValue("Sheet1", "W"+strconv.Itoa(cellIndex), fmt.Sprintf("%0.2f%%", float64(dayCompanyStats[companyName].NoteOverloadNum)/float64(dayCompanyStats[companyName].NoteNum)*100))
			}
			// 超载扣减分数
			d4 := deductionsScore3(dayCompanyStats[companyName].NoteOverloadNum, dayCompanyStats[companyName].NoteNum)
			f.SetCellValue("Sheet1", "X"+strconv.Itoa(cellIndex), d4)

			// 公司评分 TODO
			f.SetCellValue("Sheet1", "C"+strconv.Itoa(cellIndex), 100-d1-d2-d3-d4-d5)

			cellIndex++
		}

		m1 := cellIndex - int(averageScore[v]["count"])
		m2 := cellIndex - 1
		f.MergeCell("Sheet1", fmt.Sprintf("D%d", m1), fmt.Sprintf("D%d", m2))
		f.MergeCell("Sheet1", fmt.Sprintf("E%d", m1), fmt.Sprintf("E%d", m2))

		if avg <= 85 {
			f.SetCellStyle("Sheet1", fmt.Sprintf("E%d", m1), fmt.Sprintf("E%d", m2), redBg)
		} else if avg <= 91 {
			f.SetCellStyle("Sheet1", fmt.Sprintf("E%d", m1), fmt.Sprintf("E%d", m2), yellowBg)
		}

	}

	// 读取缓存的月度数据，每月1号使用当天数据，其他时间使用前一天数据并累计
	var monthCompanyStats map[string]Stat
	if time.Now().Day() == 1 {
		monthCompanyStats = dayCompanyStats
	} else {
		cc, err := os.ReadFile("template/monthCompanyStats.json")
		if err != nil {
			logger.Stdout.Error(err.Error())
			return
		}
		if err := json.Unmarshal(cc, &monthCompanyStats); err != nil {
			logger.Stdout.Error(err.Error())
			return
		}

		for companyName, stat := range dayCompanyStats {
			if _, ok := monthCompanyStats[companyName]; ok {
				st := monthCompanyStats[companyName]
				st.NoteNum += stat.NoteNum
				st.NoteAmount += stat.NoteAmount
				st.NoteHighPriceNum += stat.NoteHighPriceNum
				st.NoteTimeNum += stat.NoteTimeNum
				st.NotePoundNum += stat.NotePoundNum
				st.NoteOverloadNum += stat.NoteOverloadNum
				st.NoteAddressNum += stat.NoteAddressNum
				monthCompanyStats[companyName] = st
			}
		}
	}

	// 月度数据
	cellIndex = 6
	for _, v := range sortIntermediaryName {
		for _, companyName := range allCompanies[v] {
			// 接单总数
			f.SetCellValue("Sheet1", "Y"+strconv.Itoa(cellIndex), monthCompanyStats[companyName].NoteNum)
			// 单价过高数量
			f.SetCellValue("Sheet1", "Z"+strconv.Itoa(cellIndex), monthCompanyStats[companyName].NoteHighPriceNum)
			// 装卸货地址不符数量
			f.SetCellValue("Sheet1", "AA"+strconv.Itoa(cellIndex), monthCompanyStats[companyName].NoteAddressNum)
			// 运输时间异常数量
			f.SetCellValue("Sheet1", "AB"+strconv.Itoa(cellIndex), monthCompanyStats[companyName].NoteTimeNum)
			// 磅单异常数量
			f.SetCellValue("Sheet1", "AC"+strconv.Itoa(cellIndex), monthCompanyStats[companyName].NotePoundNum)
			// 超载异常数量
			f.SetCellValue("Sheet1", "AD"+strconv.Itoa(cellIndex), monthCompanyStats[companyName].NoteOverloadNum)

			// 运价异常数量占比
			if monthCompanyStats[companyName].NoteHighPriceNum == 0 {
				f.SetCellValue("Sheet1", "AE"+strconv.Itoa(cellIndex), 0)
			} else {
				f.SetCellValue("Sheet1", "AE"+strconv.Itoa(cellIndex), fmt.Sprintf("%0.2f%%", float64(monthCompanyStats[companyName].NoteHighPriceNum)/float64(monthCompanyStats[companyName].NoteNum)*100))
			}
			// 装卸货地址不符占比
			if monthCompanyStats[companyName].NoteAddressNum == 0 {
				f.SetCellValue("Sheet1", "AF"+strconv.Itoa(cellIndex), 0)
			} else {
				f.SetCellValue("Sheet1", "AF"+strconv.Itoa(cellIndex), fmt.Sprintf("%0.2f%%", float64(monthCompanyStats[companyName].NoteAddressNum)/float64(monthCompanyStats[companyName].NoteNum)*100))
			}
			// 运输时间异常占比
			if monthCompanyStats[companyName].NoteTimeNum == 0 {
				f.SetCellValue("Sheet1", "AG"+strconv.Itoa(cellIndex), 0)
			} else {
				f.SetCellValue("Sheet1", "AG"+strconv.Itoa(cellIndex), fmt.Sprintf("%0.2f%%", float64(monthCompanyStats[companyName].NoteTimeNum)/float64(monthCompanyStats[companyName].NoteNum)*100))
			}

			// 磅单异常占比
			if monthCompanyStats[companyName].NotePoundNum == 0 {
				f.SetCellValue("Sheet1", "AH"+strconv.Itoa(cellIndex), 0)
			} else {
				f.SetCellValue("Sheet1", "AH"+strconv.Itoa(cellIndex), fmt.Sprintf("%0.2f%%", float64(monthCompanyStats[companyName].NotePoundNum)/float64(monthCompanyStats[companyName].NoteNum)*100))
			}
			// 超载异常占比
			if monthCompanyStats[companyName].NoteOverloadNum == 0 {
				f.SetCellValue("Sheet1", "AI"+strconv.Itoa(cellIndex), 0)
			} else {
				f.SetCellValue("Sheet1", "AI"+strconv.Itoa(cellIndex), fmt.Sprintf("%0.2f%%", float64(monthCompanyStats[companyName].NoteOverloadNum)/float64(monthCompanyStats[companyName].NoteNum)*100))
			}

			cellIndex++
		}
	}

	st, _ := time.Parse("2006-01-02", dayStart)
	et, _ := time.Parse("2006-01-02", dayEnd)
	xlsxName := fmt.Sprintf("%s-%s日级风控报表.xlsx", st.Format("20060102"), et.Format("20060102"))
	if err := f.SaveAs(xlsxName); err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	// 发送文件到飞书指定用户
	fileKey, err := lark.UploadXlsFile(xlsxName)
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	if err := lark.SendFileMessage(lark.ReceivesID.ZhaoChenYing, fileKey); err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	os.Remove(xlsxName)

	logger.Stdout.Info(fmt.Sprintf("%s-%s日级风控报表已发送", st.Format("20060102"), et.Format("20060102")))

	// 保存月度数据,1号保存当天数据，其他时间保存前一天数据并累计
	tt, _ := json.Marshal(monthCompanyStats)
	if err := os.WriteFile("template/monthCompanyStats.json", tt, 0644); err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	logger.Stdout.Info(fmt.Sprintf("月度数据保存成功, 耗时: %v", time.Since(startTime)))
}

// 日级和月度数据统计函数
func stats(companiesStats map[string]Stat, filterCompanies []string, dayStart, dayEnd string) (map[string]Stat, error) {
	// 查询数据
	rr1, _ := model.DB.Table("tms_shipper AS a").
		Joins("JOIN tms_transport_note AS b ON a.company_id = b.create_company_id").
		Joins("JOIN tms_order_history AS c ON b.order_id = c.id AND b.order_version_no = c.version_no").
		Select("b.freight_amount", "b.service_charge", "b.freight_paid", "b.service_fee_paid",
			"b.loading_time", "b.unload_time", "c.loading_longitude", "c.loading_latitude", "c.unload_longitude", "c.unload_latitude", "a.company_name", "b.transportation_driver_id").
		Where("a.company_name IN (?)", filterCompanies).
		Where("a.shipper_type = ? AND a.is_delete = ?", 1, 0).
		Where("b.waybill_status NOT IN (?)", []int{6, 7}).
		Where("b.loading_time BETWEEN ? AND ?", dayStart, dayEnd).
		Rows()
	for rr1.Next() {
		var freightAmount float64
		var serviceCharge float64
		var freightPaid sql.NullFloat64
		var serviceFeePaid sql.NullFloat64
		var loadingTime sql.NullTime
		var unloadTime sql.NullTime
		var loadingLongitude float64
		var loadingLatitude float64
		var unloadLongitude float64
		var unloadLatitude float64
		var companyName string
		var transportationDriverID string

		if err := rr1.Scan(&freightAmount, &serviceCharge, &freightPaid, &serviceFeePaid, &loadingTime, &unloadTime, &loadingLongitude, &loadingLatitude, &unloadLongitude, &unloadLatitude, &companyName, &transportationDriverID); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}

		var freight, servicefee float64
		if freightPaid.Valid && freightPaid.Float64 > 0 {
			freight = freightPaid.Float64
		} else {
			freight = freightAmount
		}
		if serviceFeePaid.Valid && serviceFeePaid.Float64 > 0 {
			servicefee = serviceFeePaid.Float64
		} else {
			servicefee = serviceCharge
		}
		t := companiesStats[companyName]
		t.NoteNum++
		t.NoteAmount += freight + servicefee

		companiesStats[companyName] = t
	}

	// 单价过高异常运单数量
	rr2, _ := model.DB.Table("tms_shipper AS a").
		Joins("JOIN tms_transport_note AS b ON a.company_id = b.create_company_id").
		Joins("JOIN tms_transport_risk_exception AS c ON b.id = c.exception_id").
		Select([]string{"a.company_name"}).
		Where("a.company_name IN (?)", filterCompanies).
		Where("a.shipper_type = ? AND a.is_delete = ?", 1, 0).
		Where("b.waybill_status NOT IN (?)", []int{6, 7}).
		Where("b.loading_time BETWEEN ? AND ?", dayStart, dayEnd).
		Where("c.exception_type = ?", 3).
		Where("c.exception_detail LIKE ?", "%运价过高%").
		Where("c.is_delete = ?", 0).
		Rows()
	for rr2.Next() {
		var companyName string
		if err := rr2.Scan(&companyName); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}
		t := companiesStats[companyName]
		t.NoteHighPriceNum++
		companiesStats[companyName] = t
	}

	// 运输时间异常运单数量
	rr3, _ := model.DB.Table("tms_shipper AS a").
		Joins("JOIN tms_transport_note AS b ON a.company_id = b.create_company_id").
		Joins("JOIN tms_transport_risk_exception AS c ON b.id = c.exception_id").
		Select([]string{"a.company_name"}).
		Where("a.company_name IN (?)", filterCompanies).
		Where("a.shipper_type = ? AND a.is_delete = ?", 1, 0).
		Where("b.waybill_status NOT IN (?)", []int{6, 7}).
		Where("b.loading_time BETWEEN ? AND ?", dayStart, dayEnd).
		Where("c.exception_type = ?", 7).
		Where("c.is_delete = ?", 0).
		Rows()
	for rr3.Next() {
		var companyName string
		if err := rr3.Scan(&companyName); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}
		t := companiesStats[companyName]
		t.NoteTimeNum++
		companiesStats[companyName] = t
	}

	// 磅单异常运单数
	rr4, _ := model.DB.Table("tms_shipper AS a").
		Joins("JOIN tms_transport_note AS b ON a.company_id = b.create_company_id").
		Joins("JOIN tms_transport_risk_exception AS c ON b.id = c.exception_id").
		Select([]string{"a.company_name"}).
		Where("a.company_name IN (?)", filterCompanies).
		Where("a.shipper_type = ? AND a.is_delete = ?", 1, 0).
		Where("b.waybill_status NOT IN (?)", []int{6, 7}).
		Where("b.loading_time BETWEEN ? AND ?", dayStart, dayEnd).
		Where("c.exception_type = ?", 8).
		Where("c.forward_status = 2 OR c.remark = '待确认'").
		Where("c.is_delete = ?", 0).
		Rows()
	for rr4.Next() {
		var companyName string
		if err := rr4.Scan(&companyName); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}
		t := companiesStats[companyName]
		t.NotePoundNum++
		companiesStats[companyName] = t
	}

	// 超载异常运单数量
	rr5, _ := model.DB.Table("tms_shipper AS a").
		Joins("JOIN tms_transport_note AS b ON a.company_id = b.create_company_id").
		Select([]string{"a.company_name"}).
		Where("a.company_name IN (?)", filterCompanies).
		Where("a.shipper_type = ? AND a.is_delete = ?", 1, 0).
		Where("b.waybill_status NOT IN (?)", []int{6, 7}).
		Where("b.loading_time BETWEEN ? AND ?", dayStart, dayEnd).
		Where("b.loading_number >= ?", 34).
		Rows()
	for rr5.Next() {
		var companyName string
		if err := rr5.Scan(&companyName); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}
		t := companiesStats[companyName]
		t.NoteOverloadNum++
		companiesStats[companyName] = t
	}

	// 装卸货地址异常运单数量
	rr6, _ := model.DB.Table("tms_shipper AS a").
		Joins("JOIN tms_transport_note AS b ON a.company_id = b.create_company_id").
		Joins("JOIN tms_transport_risk_exception AS c ON b.id = c.exception_id").
		Select([]string{"a.company_name"}).
		Where("a.company_name IN (?)", filterCompanies).
		Where("a.shipper_type = ? AND a.is_delete = ?", 1, 0).
		Where("b.waybill_status NOT IN (?)", []int{6, 7}).
		Where("b.loading_time BETWEEN ? AND ?", dayStart, dayEnd).
		Where("c.exception_type = ?", 2).
		Where("c.forward_status = ?", 2).
		Where("c.is_delete = ?", 0).
		Rows()
	for rr6.Next() {
		var companyName string
		if err := rr6.Scan(&companyName); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}
		t := companiesStats[companyName]
		t.NoteAddressNum++
		companiesStats[companyName] = t
	}

	return companiesStats, nil
}

// RiskReport10Kilometer 运距10公里内风控报表
func RiskReport10Kilometer() {
	filterCompanies := []string{}
	dayCompanyStats := make(map[string]Stat)

	// 根据客户来源进行分类
	var keHuZhuanJieShao, shiChangZhiTuo, xiangMuKeHu, ningChengZiGongSi, yuanBaoShanZiGongSi, juJianRen, xiaoShouShiChangZhiTuo, xiaoShouKeHuZhuanJieShao, duoMengDe []string
	shippers, err := model.DB.Table("tms_shipper").
		Select([]string{"company_name", "shipper_from"}).
		Where("shipper_type = ? AND is_delete = ?", 1, 0).
		Rows()
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	for shippers.Next() {
		var companyName string
		var shipperFrom string
		if err := shippers.Scan(&companyName, &shipperFrom); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}

		filterCompanies = append(filterCompanies, companyName)
		if _, ok := dayCompanyStats[companyName]; !ok {
			dayCompanyStats[companyName] = Stat{}
		}

		switch shipperFrom {
		case "商务客户转介绍":
			keHuZhuanJieShao = append(keHuZhuanJieShao, companyName)
		case "商务市场直拓":
			shiChangZhiTuo = append(shiChangZhiTuo, companyName)
		case "项目客户":
			xiangMuKeHu = append(xiangMuKeHu, companyName)
		case "宁城子公司":
			ningChengZiGongSi = append(ningChengZiGongSi, companyName)
		case "元宝山子公司":
			yuanBaoShanZiGongSi = append(yuanBaoShanZiGongSi, companyName)
		case "居间人":
			juJianRen = append(juJianRen, companyName)
		case "销售直营":
			xiaoShouShiChangZhiTuo = append(xiaoShouShiChangZhiTuo, companyName)
		case "销售+介绍人":
			xiaoShouKeHuZhuanJieShao = append(xiaoShouKeHuZhuanJieShao, companyName)
		case "多蒙德项目客户":
			duoMengDe = append(duoMengDe, companyName)
		}
	}

	companies := map[string]map[string][]string{
		"商务": {
			"商务客户转介绍": keHuZhuanJieShao,
			"商务市场直拓":  shiChangZhiTuo,
			"项目客户":    xiangMuKeHu,
			"宁城子公司":   ningChengZiGongSi,
			"元宝山子公司":  yuanBaoShanZiGongSi,
			"多蒙德项目客户": duoMengDe,
		},
		"销售": {
			"居间人":    juJianRen,
			"销售直营":   xiaoShouShiChangZhiTuo,
			"销售+介绍人": xiaoShouKeHuZhuanJieShao,
		},
	}
	sortBelong := []string{"商务", "销售"}
	sortCategory := []string{"商务客户转介绍", "商务市场直拓", "项目客户", "宁城子公司", "元宝山子公司", "多蒙德项目客户", "居间人",
		"销售直营", "销售+介绍人"}

	dayStart, dayEnd := "", ""
	if time.Now().Day() <= 15 {
		dayStart = time.Now().Format("2006-01") + "-01"
		dayEnd = time.Now().Format("2006-01") + "-16"
	}
	if time.Now().Day() > 15 {
		dayStart = time.Now().Format("2006-01") + "-16"
		dayEnd = time.Now().Add(20*24*time.Hour).Format("2006-01") + "-01"
	}

	dayCompanyStats, err = risk10Stats(dayCompanyStats, filterCompanies, dayStart, dayEnd)
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	f, err := excelize.OpenFile("template/10risk_template.xlsx")
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	defer f.Close()

	// 统计分数，标记颜色
	cellBankground := make(map[string]any)
	for _, belong := range sortBelong {
		for _, category := range sortCategory {
			companySlice := companies[belong][category]
			if len(companySlice) == 0 {
				continue
			}

			for _, companyName := range companySlice {
				// 运价异常扣减分数
				d1 := deductionsScore1(dayCompanyStats[companyName].NoteHighPriceNum, dayCompanyStats[companyName].NoteNum, 1)
				// 运输时间异常扣减分数
				d2 := deductionsScore1(dayCompanyStats[companyName].NoteTimeNum, dayCompanyStats[companyName].NoteNum, 1)
				// 磅单扣减分数
				d3 := deductionsScore2(dayCompanyStats[companyName].NotePoundNum, dayCompanyStats[companyName].NoteNum)
				// 超载扣减分数
				d4 := deductionsScore3(dayCompanyStats[companyName].NoteOverloadNum, dayCompanyStats[companyName].NoteNum)
				// 地址异常扣减分数
				d5 := deductionsScore3(dayCompanyStats[companyName].NoteAddressNum, dayCompanyStats[companyName].NoteNum)

				minScore := min(100-d1, 100-d2, 100-d3, 100-d4, 100-d5)
				if minScore <= 85 {
					cellBankground[companyName] = "redBg"
				} else if minScore <= 90 {
					cellBankground[companyName] = "yellowBg"
				}
			}
		}
	}

	redBg, _ := f.NewStyle(&excelize.Style{
		Border: []excelize.Border{
			{Type: "left", Color: "#000000", Style: 1},
			{Type: "right", Color: "#000000", Style: 1},
			{Type: "top", Color: "#000000", Style: 1},
			{Type: "bottom", Color: "#000000", Style: 1},
		},
		Alignment: &excelize.Alignment{
			Horizontal: "left",
			Vertical:   "center",
			WrapText:   true,
		},
		// 设置背景色
		Fill: excelize.Fill{
			Type:    "pattern",
			Pattern: 1,
			Color:   []string{"FE0202"},
		},
	})
	yellowBg, _ := f.NewStyle(&excelize.Style{
		Border: []excelize.Border{
			{Type: "left", Color: "#000000", Style: 1},
			{Type: "right", Color: "#000000", Style: 1},
			{Type: "top", Color: "#000000", Style: 1},
			{Type: "bottom", Color: "#000000", Style: 1},
		},
		Alignment: &excelize.Alignment{
			Horizontal: "left",
			Vertical:   "center",
			WrapText:   true,
		},
		// 设置背景色
		Fill: excelize.Fill{
			Type:    "pattern",
			Pattern: 1,
			Color:   []string{"F1C024"},
		},
	})

	cellIndex := 6

	for _, belong := range sortBelong {
		f.SetCellValue("Sheet1", fmt.Sprintf("B%d", cellIndex), belong)

		num := 0
		for _, category := range sortCategory {

			companySlice := companies[belong][category]
			if len(companySlice) == 0 {
				continue
			}

			for _, companyName := range companySlice {

				// 总分
				f.SetCellValue("Sheet1", "A"+strconv.Itoa(cellIndex), 100)
				// 公司名称
				f.SetCellValue("Sheet1", "D"+strconv.Itoa(cellIndex), companyName)
				if cellBankground[companyName] == "redBg" {
					f.SetCellStyle("Sheet1", "D"+strconv.Itoa(cellIndex), "D"+strconv.Itoa(cellIndex), redBg)
				}
				if cellBankground[companyName] == "yellowBg" {
					f.SetCellStyle("Sheet1", "D"+strconv.Itoa(cellIndex), "D"+strconv.Itoa(cellIndex), yellowBg)
				}

				// 分组
				f.SetCellValue("Sheet1", "C"+strconv.Itoa(cellIndex), category)

				// 接单总数
				f.SetCellValue("Sheet1", "F"+strconv.Itoa(cellIndex), dayCompanyStats[companyName].NoteNum)

				// 业务占比 TODO
				f.SetCellValue("Sheet1", "G"+strconv.Itoa(cellIndex), fmt.Sprintf("%0.2f%%", dayCompanyStats[companyName].Risk10Freight))

				// 单价过高数量
				f.SetCellValue("Sheet1", "H"+strconv.Itoa(cellIndex), dayCompanyStats[companyName].NoteHighPriceNum)
				// 装卸货地址不符数量
				f.SetCellValue("Sheet1", "I"+strconv.Itoa(cellIndex), dayCompanyStats[companyName].NoteAddressNum)
				// 运输时间异常数量
				f.SetCellValue("Sheet1", "J"+strconv.Itoa(cellIndex), dayCompanyStats[companyName].NoteTimeNum)

				// 运价异常数量占比
				if dayCompanyStats[companyName].NoteHighPriceNum == 0 {
					f.SetCellValue("Sheet1", "K"+strconv.Itoa(cellIndex), 0)
				} else {
					f.SetCellValue("Sheet1", "K"+strconv.Itoa(cellIndex), fmt.Sprintf("%0.2f%%", float64(dayCompanyStats[companyName].NoteHighPriceNum)/float64(dayCompanyStats[companyName].NoteNum)*100))
				}
				// 装卸货地址不符占比
				if dayCompanyStats[companyName].NoteAddressNum == 0 {
					f.SetCellValue("Sheet1", "L"+strconv.Itoa(cellIndex), 0)
				} else {
					f.SetCellValue("Sheet1", "L"+strconv.Itoa(cellIndex), fmt.Sprintf("%0.2f%%", float64(dayCompanyStats[companyName].NoteAddressNum)/float64(dayCompanyStats[companyName].NoteNum)*100))
				}
				// 运输时间异常占比
				if dayCompanyStats[companyName].NoteTimeNum == 0 {
					f.SetCellValue("Sheet1", "M"+strconv.Itoa(cellIndex), 0)
				} else {
					f.SetCellValue("Sheet1", "M"+strconv.Itoa(cellIndex), fmt.Sprintf("%0.2f%%", float64(dayCompanyStats[companyName].NoteTimeNum)/float64(dayCompanyStats[companyName].NoteNum)*100))
				}

				// 运价异常扣减分数
				d1 := deductionsScore1(dayCompanyStats[companyName].NoteHighPriceNum, dayCompanyStats[companyName].NoteNum, 1)
				f.SetCellValue("Sheet1", "N"+strconv.Itoa(cellIndex), d1)
				// 装卸货地址不符扣减分数
				d5 := deductionsScore1(dayCompanyStats[companyName].NoteAddressNum, dayCompanyStats[companyName].NoteNum, 1)
				f.SetCellValue("Sheet1", "O"+strconv.Itoa(cellIndex), d5)
				// 运输时间异常扣减分数
				d2 := deductionsScore1(dayCompanyStats[companyName].NoteTimeNum, dayCompanyStats[companyName].NoteNum, 1)
				f.SetCellValue("Sheet1", "P"+strconv.Itoa(cellIndex), d2)

				// 磅单异常数量
				f.SetCellValue("Sheet1", "Q"+strconv.Itoa(cellIndex), dayCompanyStats[companyName].NotePoundNum)

				// 磅单异常占比
				if dayCompanyStats[companyName].NotePoundNum == 0 {
					f.SetCellValue("Sheet1", "R"+strconv.Itoa(cellIndex), 0)
				} else {
					f.SetCellValue("Sheet1", "R"+strconv.Itoa(cellIndex), fmt.Sprintf("%0.2f%%", float64(dayCompanyStats[companyName].NotePoundNum)/float64(dayCompanyStats[companyName].NoteNum)*100))
				}

				// 磅单扣减分数
				d3 := deductionsScore2(dayCompanyStats[companyName].NotePoundNum, dayCompanyStats[companyName].NoteNum)
				f.SetCellValue("Sheet1", "S"+strconv.Itoa(cellIndex), d3)

				// 超载异常数量
				f.SetCellValue("Sheet1", "T"+strconv.Itoa(cellIndex), dayCompanyStats[companyName].NoteOverloadNum)
				// 超载异常占比
				if dayCompanyStats[companyName].NoteOverloadNum == 0 {
					f.SetCellValue("Sheet1", "U"+strconv.Itoa(cellIndex), 0)
				} else {
					f.SetCellValue("Sheet1", "U"+strconv.Itoa(cellIndex), fmt.Sprintf("%0.2f%%", float64(dayCompanyStats[companyName].NoteOverloadNum)/float64(dayCompanyStats[companyName].NoteNum)*100))
				}
				// 超载扣减分数
				d4 := deductionsScore3(dayCompanyStats[companyName].NoteOverloadNum, dayCompanyStats[companyName].NoteNum)
				f.SetCellValue("Sheet1", "V"+strconv.Itoa(cellIndex), d4)

				// 公司评分 TODO
				f.SetCellValue("Sheet1", "E"+strconv.Itoa(cellIndex), 100-d1-d2-d3-d4-d5)

				cellIndex++
			}

			m1 := cellIndex - len(companySlice)
			m2 := cellIndex - 1
			f.MergeCell("Sheet1", fmt.Sprintf("C%d", m1), fmt.Sprintf("C%d", m2))

			num += len(companySlice)

			f.MergeCell("Sheet1", fmt.Sprintf("B%d", cellIndex-num), fmt.Sprintf("B%d", cellIndex-1))
		}
	}

	st, _ := time.Parse("2006-01-02", dayStart)
	et, _ := time.Parse("2006-01-02", dayEnd)
	xlsxName := fmt.Sprintf("%s-%s10公里内风控半月度数据报表.xlsx", st.Format("20060102"), et.Format("20060102"))

	if err := f.SaveAs(xlsxName); err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	// 发送文件到飞书指定用户
	fileKey, err := lark.UploadXlsFile(xlsxName)
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	if err := lark.SendFileMessage(lark.ReceivesID.ZhaoChenYing, fileKey); err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	os.Remove(xlsxName)

	logger.Stdout.Info(fmt.Sprintf("%s-%s10公里内风控半月度数据报表已发送", st.Format("20060102"), et.Format("20060102")))
}

// 运距10公里内风控报表统计
func risk10Stats(companiesStats map[string]Stat, filterCompanies []string, dayStart, dayEnd string) (map[string]Stat, error) {
	// 业务占比
	totalFreight := make(map[string]float64)
	risk10Freight := make(map[string]float64)
	rr0, _ := model.DB.Table("tms_shipper AS a").
		Joins("JOIN tms_transport_note AS b ON a.company_id = b.create_company_id").
		Joins("JOIN tms_order_history AS c ON b.order_id = c.id AND b.order_version_no = c.version_no").
		Select("a.company_name", "b.freight_amount", "b.service_charge", "b.freight_paid", "b.service_fee_paid",
			"b.actual_haul_distance").
		Where("a.company_name IN (?)", filterCompanies).
		Where("a.shipper_type = ? AND a.is_delete = ?", 1, 0).
		Where("b.waybill_status NOT IN (?)", []int{6, 7}).
		Where("b.loading_time BETWEEN ? AND ?", dayStart, dayEnd).
		Rows()
	for rr0.Next() {
		var companyName string
		var freightAmount float64
		var serviceCharge float64
		var freightPaid sql.NullFloat64
		var serviceFeePaid sql.NullFloat64
		var actualHaulDistance float64
		if err := rr0.Scan(&companyName, &freightAmount, &serviceCharge, &freightPaid, &serviceFeePaid, &actualHaulDistance); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}

		var freight, servicefee float64
		if freightPaid.Valid && freightPaid.Float64 > 0 {
			freight = freightPaid.Float64
		} else {
			freight = freightAmount
		}
		if serviceFeePaid.Valid && serviceFeePaid.Float64 > 0 {
			servicefee = serviceFeePaid.Float64
		} else {
			servicefee = serviceCharge
		}

		if actualHaulDistance <= 10 {
			risk10Freight[companyName] += freight + servicefee
		}
		totalFreight[companyName] += freight + servicefee
	}

	for companyName, total := range totalFreight {
		if total == 0 {
			continue
		}
		t := companiesStats[companyName]
		t.Risk10Freight = risk10Freight[companyName] / total * 100
		companiesStats[companyName] = t
	}

	// 查询数据
	rr1, _ := model.DB.Table("tms_shipper AS a").
		Joins("JOIN tms_transport_note AS b ON a.company_id = b.create_company_id").
		Joins("JOIN tms_order_history AS c ON b.order_id = c.id AND b.order_version_no = c.version_no").
		Select("b.freight_amount", "b.service_charge", "b.freight_paid", "b.service_fee_paid",
			"b.loading_time", "b.unload_time", "c.loading_longitude", "c.loading_latitude", "c.unload_longitude", "c.unload_latitude", "a.company_name", "b.transportation_driver_id").
		Where("a.company_name IN (?)", filterCompanies).
		Where("a.shipper_type = ? AND a.is_delete = ?", 1, 0).
		Where("b.waybill_status NOT IN (?)", []int{6, 7}).
		Where("b.loading_time BETWEEN ? AND ?", dayStart, dayEnd).
		Where("b.actual_haul_distance <= ?", 10).
		Rows()
	for rr1.Next() {
		var freightAmount float64
		var serviceCharge float64
		var freightPaid sql.NullFloat64
		var serviceFeePaid sql.NullFloat64
		var loadingTime sql.NullTime
		var unloadTime sql.NullTime
		var loadingLongitude float64
		var loadingLatitude float64
		var unloadLongitude float64
		var unloadLatitude float64
		var companyName string
		var transportationDriverID string

		if err := rr1.Scan(&freightAmount, &serviceCharge, &freightPaid, &serviceFeePaid, &loadingTime, &unloadTime, &loadingLongitude, &loadingLatitude, &unloadLongitude, &unloadLatitude, &companyName, &transportationDriverID); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}

		var freight, servicefee float64
		if freightPaid.Valid && freightPaid.Float64 > 0 {
			freight = freightPaid.Float64
		} else {
			freight = freightAmount
		}
		if serviceFeePaid.Valid && serviceFeePaid.Float64 > 0 {
			servicefee = serviceFeePaid.Float64
		} else {
			servicefee = serviceCharge
		}
		t := companiesStats[companyName]
		t.NoteNum++
		t.NoteAmount += freight + servicefee

		companiesStats[companyName] = t
	}

	// 单价过高异常运单数量
	rr2, _ := model.DB.Table("tms_shipper AS a").
		Joins("JOIN tms_transport_note AS b ON a.company_id = b.create_company_id").
		Joins("JOIN tms_transport_risk_exception AS c ON b.id = c.exception_id").
		Select([]string{"a.company_name"}).
		Where("a.company_name IN (?)", filterCompanies).
		Where("a.shipper_type = ? AND a.is_delete = ?", 1, 0).
		Where("b.waybill_status NOT IN (?)", []int{6, 7}).
		Where("b.loading_time BETWEEN ? AND ?", dayStart, dayEnd).
		Where("b.actual_haul_distance <= ?", 10).
		Where("c.exception_type = ?", 3).
		Where("c.exception_detail LIKE ?", "%运价过高%").
		Where("c.is_delete = ?", 0).
		Rows()
	for rr2.Next() {
		var companyName string
		if err := rr2.Scan(&companyName); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}
		t := companiesStats[companyName]
		t.NoteHighPriceNum++
		companiesStats[companyName] = t
	}

	// 运输时间异常运单数量
	rr3, _ := model.DB.Table("tms_shipper AS a").
		Joins("JOIN tms_transport_note AS b ON a.company_id = b.create_company_id").
		Joins("JOIN tms_transport_risk_exception AS c ON b.id = c.exception_id").
		Select([]string{"a.company_name"}).
		Where("a.company_name IN (?)", filterCompanies).
		Where("a.shipper_type = ? AND a.is_delete = ?", 1, 0).
		Where("b.waybill_status NOT IN (?)", []int{6, 7}).
		Where("b.loading_time BETWEEN ? AND ?", dayStart, dayEnd).
		Where("b.actual_haul_distance <= ?", 10).
		Where("c.exception_type = ?", 7).
		Where("c.is_delete = ?", 0).
		Rows()
	for rr3.Next() {
		var companyName string
		if err := rr3.Scan(&companyName); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}
		t := companiesStats[companyName]
		t.NoteTimeNum++
		companiesStats[companyName] = t
	}

	// 磅单异常运单数
	rr4, _ := model.DB.Table("tms_shipper AS a").
		Joins("JOIN tms_transport_note AS b ON a.company_id = b.create_company_id").
		Joins("JOIN tms_transport_risk_exception AS c ON b.id = c.exception_id").
		Select([]string{"a.company_name"}).
		Where("a.company_name IN (?)", filterCompanies).
		Where("a.shipper_type = ? AND a.is_delete = ?", 1, 0).
		Where("b.waybill_status NOT IN (?)", []int{6, 7}).
		Where("b.loading_time BETWEEN ? AND ?", dayStart, dayEnd).
		Where("b.actual_haul_distance <= ?", 10).
		Where("c.exception_type = ?", 8).
		Where("c.forward_status = 2 OR c.remark = '待确认'").
		Where("c.is_delete = ?", 0).
		Rows()
	for rr4.Next() {
		var companyName string
		if err := rr4.Scan(&companyName); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}
		t := companiesStats[companyName]
		t.NotePoundNum++
		companiesStats[companyName] = t
	}

	// 超载异常运单数量
	rr5, _ := model.DB.Table("tms_shipper AS a").
		Joins("JOIN tms_transport_note AS b ON a.company_id = b.create_company_id").
		Select([]string{"a.company_name"}).
		Where("a.company_name IN (?)", filterCompanies).
		Where("a.shipper_type = ? AND a.is_delete = ?", 1, 0).
		Where("b.waybill_status NOT IN (?)", []int{6, 7}).
		Where("b.loading_time BETWEEN ? AND ?", dayStart, dayEnd).
		Where("b.actual_haul_distance <= ?", 10).
		Where("b.loading_number >= ?", 34).
		Rows()
	for rr5.Next() {
		var companyName string
		if err := rr5.Scan(&companyName); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}
		t := companiesStats[companyName]
		t.NoteOverloadNum++
		companiesStats[companyName] = t
	}

	// 装卸货地址异常运单数量
	rr6, _ := model.DB.Table("tms_shipper AS a").
		Joins("JOIN tms_transport_note AS b ON a.company_id = b.create_company_id").
		Joins("JOIN tms_transport_risk_exception AS c ON b.id = c.exception_id").
		Select([]string{"a.company_name"}).
		Where("a.company_name IN (?)", filterCompanies).
		Where("a.shipper_type = ? AND a.is_delete = ?", 1, 0).
		Where("b.waybill_status NOT IN (?)", []int{6, 7}).
		Where("b.loading_time BETWEEN ? AND ?", dayStart, dayEnd).
		Where("b.actual_haul_distance <= ?", 10).
		Where("c.exception_type = ?", 2).
		Where("c.forward_status = ?", 2).
		Where("c.is_delete = ?", 0).
		Rows()
	for rr6.Next() {
		var companyName string
		if err := rr6.Scan(&companyName); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}
		t := companiesStats[companyName]
		t.NoteAddressNum++
		companiesStats[companyName] = t
	}

	return companiesStats, nil
}

// 月度风控报表
func RiskMonthReports() {
	rows, _ := model.DB.Table("tms_shipper").
		Select([]string{"company_name", "intermediary_name"}).
		Where("shipper_type = ? AND is_delete = ?", 1, 0).
		Where("intermediary_name IS NOT NULL").
		Rows()

	sortIntermediaryName := []string{}
	allCompanies := make(map[string][]string)
	filterCompanies := []string{}
	dayCompanyStats := make(map[string]Stat)
	for rows.Next() {
		var companyName string
		var intermediaryName string
		if err := rows.Scan(&companyName, &intermediaryName); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}
		sortIntermediaryName = append(sortIntermediaryName, intermediaryName)
		allCompanies[intermediaryName] = append(allCompanies[intermediaryName], companyName)
		filterCompanies = append(filterCompanies, companyName)
		if _, ok := dayCompanyStats[companyName]; !ok {
			dayCompanyStats[companyName] = Stat{}
		}
	}
	sortIntermediaryName = lo.Uniq(sortIntermediaryName)

	// 数据查询时间区间
	dayStart := time.Now().Add(-30*24*time.Hour).Format("2006-01") + "-01"
	dayEnd := time.Now().Format("2006-01") + "-01"

	dayCompanyStats, err := stats(dayCompanyStats, filterCompanies, dayStart, dayEnd)
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	f, err := excelize.OpenFile("template/risk_month_template.xlsx")
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	defer f.Close()

	// 统计分数，标记颜色
	cellBankground := make(map[string]any)
	averageScore := make(map[string]map[string]float64)
	for _, v := range sortIntermediaryName {
		if _, ok := averageScore[v]; !ok {
			averageScore[v] = make(map[string]float64)
		}

		for _, companyName := range allCompanies[v] {
			// 运价异常扣减分数
			d1 := deductionsScore1(dayCompanyStats[companyName].NoteHighPriceNum, dayCompanyStats[companyName].NoteNum, 1)
			// 运输时间异常扣减分数
			d2 := deductionsScore1(dayCompanyStats[companyName].NoteTimeNum, dayCompanyStats[companyName].NoteNum, 1)
			// 磅单扣减分数
			d3 := deductionsScore2(dayCompanyStats[companyName].NotePoundNum, dayCompanyStats[companyName].NoteNum)
			// 超载扣减分数
			d4 := deductionsScore3(dayCompanyStats[companyName].NoteOverloadNum, dayCompanyStats[companyName].NoteNum)
			// 地址异常扣减分数
			d5 := deductionsScore3(dayCompanyStats[companyName].NoteAddressNum, dayCompanyStats[companyName].NoteNum)

			currentScore := float64(100 - d1 - d2 - d3 - d4 - d5)

			averageScore[v]["totalScore"] += currentScore
			averageScore[v]["count"] += 1

			if dayCompanyStats[companyName].NoteNum > 0 {
				averageScore[v]["avgTotal"] += currentScore
				averageScore[v]["avgCount"] += 1
			}

			if currentScore <= 85 {
				cellBankground[companyName] = "redBg"
			} else if currentScore <= 90 {
				cellBankground[companyName] = "yellowBg"
			}
		}
	}

	redBg, _ := f.NewStyle(&excelize.Style{
		Border: []excelize.Border{
			{Type: "left", Color: "#000000", Style: 1},
			{Type: "right", Color: "#000000", Style: 1},
			{Type: "top", Color: "#000000", Style: 1},
			{Type: "bottom", Color: "#000000", Style: 1},
		},
		Alignment: &excelize.Alignment{
			Horizontal: "left",
			Vertical:   "center",
			WrapText:   true,
		},
		// 设置背景色
		Fill: excelize.Fill{
			Type:    "pattern",
			Pattern: 1,
			Color:   []string{"FE0202"},
		},
	})
	yellowBg, _ := f.NewStyle(&excelize.Style{
		Border: []excelize.Border{
			{Type: "left", Color: "#000000", Style: 1},
			{Type: "right", Color: "#000000", Style: 1},
			{Type: "top", Color: "#000000", Style: 1},
			{Type: "bottom", Color: "#000000", Style: 1},
		},
		Alignment: &excelize.Alignment{
			Horizontal: "left",
			Vertical:   "center",
			WrapText:   true,
		},
		// 设置背景色
		Fill: excelize.Fill{
			Type:    "pattern",
			Pattern: 1,
			Color:   []string{"F1C024"},
		},
	})

	// 日级数据
	cellIndex := 6
	for _, v := range sortIntermediaryName {
		avg := 0.0
		if averageScore[v]["avgCount"] != 0 {
			avg = averageScore[v]["avgTotal"] / averageScore[v]["avgCount"]
		}

		for _, companyName := range allCompanies[v] {

			// 总分
			f.SetCellValue("Sheet1", "A"+strconv.Itoa(cellIndex), 100)
			// 公司名称
			f.SetCellValue("Sheet1", "B"+strconv.Itoa(cellIndex), companyName)
			if cellBankground[companyName] == "redBg" {
				f.SetCellStyle("Sheet1", "B"+strconv.Itoa(cellIndex), "B"+strconv.Itoa(cellIndex), redBg)
			}
			if cellBankground[companyName] == "yellowBg" {
				f.SetCellStyle("Sheet1", "B"+strconv.Itoa(cellIndex), "B"+strconv.Itoa(cellIndex), yellowBg)
			}

			// 分组
			f.SetCellValue("Sheet1", "D"+strconv.Itoa(cellIndex), v)
			// 客户平均分 TODO
			f.SetCellValue("Sheet1", "E"+strconv.Itoa(cellIndex), fmt.Sprintf("%.0f", avg))

			// 接单总数
			f.SetCellValue("Sheet1", "F"+strconv.Itoa(cellIndex), dayCompanyStats[companyName].NoteNum)
			// 总金额
			f.SetCellValue("Sheet1", "G"+strconv.Itoa(cellIndex), fmt.Sprintf("%.2f", float64(dayCompanyStats[companyName].NoteAmount)/10000))
			// 合计异常单数
			f.SetCellValue("Sheet1", "H"+strconv.Itoa(cellIndex), dayCompanyStats[companyName].NoteHighPriceNum+dayCompanyStats[companyName].NoteTimeNum+dayCompanyStats[companyName].NotePoundNum+dayCompanyStats[companyName].NoteOverloadNum)
			// 当日异常处理率,默认为100%
			f.SetCellValue("Sheet1", "I"+strconv.Itoa(cellIndex), "100%")
			// 单价过高数量
			f.SetCellValue("Sheet1", "J"+strconv.Itoa(cellIndex), dayCompanyStats[companyName].NoteHighPriceNum)
			// 装卸货地址不符数量
			f.SetCellValue("Sheet1", "K"+strconv.Itoa(cellIndex), dayCompanyStats[companyName].NoteAddressNum)
			// 运输时间异常数量
			f.SetCellValue("Sheet1", "L"+strconv.Itoa(cellIndex), dayCompanyStats[companyName].NoteTimeNum)

			// 运价异常数量占比
			if dayCompanyStats[companyName].NoteHighPriceNum == 0 {
				f.SetCellValue("Sheet1", "M"+strconv.Itoa(cellIndex), 0)
			} else {
				f.SetCellValue("Sheet1", "M"+strconv.Itoa(cellIndex), fmt.Sprintf("%0.2f%%", float64(dayCompanyStats[companyName].NoteHighPriceNum)/float64(dayCompanyStats[companyName].NoteNum)*100))
			}
			// 装卸货地址不符占比
			if dayCompanyStats[companyName].NoteAddressNum == 0 {
				f.SetCellValue("Sheet1", "N"+strconv.Itoa(cellIndex), 0)
			} else {
				f.SetCellValue("Sheet1", "N"+strconv.Itoa(cellIndex), fmt.Sprintf("%0.2f%%", float64(dayCompanyStats[companyName].NoteAddressNum)/float64(dayCompanyStats[companyName].NoteNum)*100))
			}
			// 运输时间异常占比
			if dayCompanyStats[companyName].NoteTimeNum == 0 {
				f.SetCellValue("Sheet1", "O"+strconv.Itoa(cellIndex), 0)
			} else {
				f.SetCellValue("Sheet1", "O"+strconv.Itoa(cellIndex), fmt.Sprintf("%0.2f%%", float64(dayCompanyStats[companyName].NoteTimeNum)/float64(dayCompanyStats[companyName].NoteNum)*100))
			}

			// 运价异常扣减分数
			d1 := deductionsScore1(dayCompanyStats[companyName].NoteHighPriceNum, dayCompanyStats[companyName].NoteNum, 1)
			f.SetCellValue("Sheet1", "P"+strconv.Itoa(cellIndex), d1)
			// 装卸货地址不符扣减分数
			d5 := deductionsScore1(dayCompanyStats[companyName].NoteAddressNum, dayCompanyStats[companyName].NoteNum, 1)
			f.SetCellValue("Sheet1", "Q"+strconv.Itoa(cellIndex), d5)
			// 运输时间异常扣减分数
			d2 := deductionsScore1(dayCompanyStats[companyName].NoteTimeNum, dayCompanyStats[companyName].NoteNum, 1)
			f.SetCellValue("Sheet1", "R"+strconv.Itoa(cellIndex), d2)

			// 磅单异常数量
			f.SetCellValue("Sheet1", "S"+strconv.Itoa(cellIndex), dayCompanyStats[companyName].NotePoundNum)

			// 磅单异常占比
			if dayCompanyStats[companyName].NotePoundNum == 0 {
				f.SetCellValue("Sheet1", "T"+strconv.Itoa(cellIndex), 0)
			} else {
				f.SetCellValue("Sheet1", "T"+strconv.Itoa(cellIndex), fmt.Sprintf("%0.2f%%", float64(dayCompanyStats[companyName].NotePoundNum)/float64(dayCompanyStats[companyName].NoteNum)*100))
			}

			// 磅单扣减分数
			d3 := deductionsScore2(dayCompanyStats[companyName].NotePoundNum, dayCompanyStats[companyName].NoteNum)
			f.SetCellValue("Sheet1", "U"+strconv.Itoa(cellIndex), d3)

			// 超载异常数量
			f.SetCellValue("Sheet1", "V"+strconv.Itoa(cellIndex), dayCompanyStats[companyName].NoteOverloadNum)
			// 超载异常占比
			if dayCompanyStats[companyName].NoteOverloadNum == 0 {
				f.SetCellValue("Sheet1", "W"+strconv.Itoa(cellIndex), 0)
			} else {
				f.SetCellValue("Sheet1", "W"+strconv.Itoa(cellIndex), fmt.Sprintf("%0.2f%%", float64(dayCompanyStats[companyName].NoteOverloadNum)/float64(dayCompanyStats[companyName].NoteNum)*100))
			}
			// 超载扣减分数
			d4 := deductionsScore3(dayCompanyStats[companyName].NoteOverloadNum, dayCompanyStats[companyName].NoteNum)
			f.SetCellValue("Sheet1", "X"+strconv.Itoa(cellIndex), d4)

			// 公司评分 TODO
			f.SetCellValue("Sheet1", "C"+strconv.Itoa(cellIndex), 100-d1-d2-d3-d4-d5)

			cellIndex++
		}

		m1 := cellIndex - int(averageScore[v]["count"])
		m2 := cellIndex - 1
		f.MergeCell("Sheet1", fmt.Sprintf("D%d", m1), fmt.Sprintf("D%d", m2))
		f.MergeCell("Sheet1", fmt.Sprintf("E%d", m1), fmt.Sprintf("E%d", m2))

		if avg <= 85 {
			f.SetCellStyle("Sheet1", fmt.Sprintf("E%d", m1), fmt.Sprintf("E%d", m2), redBg)
		} else if avg <= 91 {
			f.SetCellStyle("Sheet1", fmt.Sprintf("E%d", m1), fmt.Sprintf("E%d", m2), yellowBg)
		}

	}

	st, _ := time.Parse("2006-01-02", dayStart)
	et, _ := time.Parse("2006-01-02", dayEnd)
	xlsxName := fmt.Sprintf("%s-%s月度风控报表.xlsx", st.Format("20060102"), et.Format("20060102"))

	if err := f.SaveAs(xlsxName); err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	// 发送文件到飞书指定用户
	fileKey, err := lark.UploadXlsFile(xlsxName)
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	if err := lark.SendFileMessage(lark.ReceivesID.ZhaoChenYing, fileKey); err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	os.Remove(xlsxName)

	logger.Stdout.Info(fmt.Sprintf("%s-%s月度风控报表已发送", st.Format("20060102"), et.Format("20060102")))
}

// deductionsScore1 运价异常/运输时间/装卸货位置异常扣分
func deductionsScore1(current, total, level int) int {
	// 比例
	ratio := float64(current) / float64(total)
	if ratio <= 0 || math.IsNaN(ratio) {
		return 0
	}

	if level == 1 {
		if ratio <= 0.1 {
			return 4
		} else if ratio <= 0.2 {
			return 8
		} else if ratio <= 0.3 {
			return 12
		} else if ratio <= 0.4 {
			return 16
		} else if ratio <= 0.5 {
			return 20
		} else if ratio <= 0.6 {
			return 24
		} else if ratio <= 0.7 {
			return 28
		} else if ratio <= 0.8 {
			return 32
		} else if ratio <= 0.9 {
			return 36
		} else {
			return 40
		}
	}

	if ratio <= 0.1 {
		return 2
	} else if ratio <= 0.2 {
		return 4
	} else if ratio <= 0.3 {
		return 6
	} else if ratio <= 0.4 {
		return 8
	} else if ratio <= 0.5 {
		return 10
	} else if ratio <= 0.6 {
		return 12
	} else if ratio <= 0.7 {
		return 14
	} else if ratio <= 0.8 {
		return 16
	} else if ratio <= 0.9 {
		return 18
	} else {
		return 20
	}
}

// deductionsScore2 磅单异常扣分
func deductionsScore2(current, total int) int {
	// 比例
	ratio := float64(current) / float64(total)
	if ratio <= 0 || math.IsNaN(ratio) {
		return 0
	}

	if ratio <= 0.2 {
		return 3
	} else {
		return 5
	}
}

// deductionsScore3 超载扣分
func deductionsScore3(current, total int) int {
	// 比例
	ratio := float64(current) / float64(total)
	if ratio <= 0 || math.IsNaN(ratio) {
		return 0
	}

	if ratio <= 0.3 {
		return 1
	} else {
		return 2
	}
}

// getRandomTimesInRange 接受一个日期，返回 9:00-18:00 之间的两个随机时间，时间差在 30-90 秒之间
func getRandomTimesInRange(date time.Time) (time1, time2 time.Time, err error) {
	// 定义时间范围：9:00 到 18:00
	start := time.Date(date.Year(), date.Month(), date.Day(), 9, 0, 0, 0, date.Location())
	end := time.Date(date.Year(), date.Month(), date.Day(), 18, 0, 0, 0, date.Location())

	// 计算时间范围的总秒数
	totalSeconds := int(end.Sub(start).Seconds())
	if totalSeconds <= 0 {
		return time.Time{}, time.Time{}, fmt.Errorf("invalid time range")
	}

	// 创建一个随机数生成器
	r := rand.New(rand.NewPCG(uint64(time.Now().UnixNano()), uint64(time.Now().UnixNano())))

	// 随机生成第一个时间的偏移秒数
	offsetSeconds1 := r.IntN(totalSeconds)
	time1 = start.Add(time.Duration(offsetSeconds1) * time.Second)

	// 计算时间差范围（30 到 90 秒）
	minDiff := 30
	maxDiff := 90
	diffSeconds := r.IntN(maxDiff-minDiff+1) + minDiff

	// 计算第二个时间
	time2 = time1.Add(time.Duration(diffSeconds) * time.Second)

	// 确保 time2 不超过 18:00
	if time2.After(end) {
		// 如果 time2 超出范围，反向计算 time1
		time2 = start.Add(time.Duration(r.IntN(totalSeconds)) * time.Second)
		time1 = time2.Add(-time.Duration(diffSeconds) * time.Second)
		// 如果 time1 小于 9:00，返回错误
		if time1.Before(start) {
			return time.Time{}, time.Time{}, fmt.Errorf("cannot generate valid times within range and constraints")
		}
	}

	// 确保 time1 始终早于 time2
	if time1.After(time2) {
		time1, time2 = time2, time1
	}

	return time1, time2, nil
}

func RiskVehicleTrackCheck() {
	startTime := time.Now().Add(-24 * time.Hour).Format("2006-01-02")
	endTime := time.Now().Format("2006-01-02")

	type vehicleTrack struct {
		checkDate            string
		transportationNumber string
		companyName          string
		transportationPlate  string
		exceptionDisplay     string
		checkTimeStart       string
		checkTimeEnd         string
		ageing               string
		auditor              string
	}

	rows, err := model.DB.Table("tms_transport_note AS a").
		Joins("JOIN tms_shipper AS b ON a.create_company_id = b.company_id").
		Select([]string{"a.transportation_number", "b.company_name", "a.transportation_plate"}).
		Where("a.waybill_status IN (?)", []int{3, 4, 5, 8, 9}).
		Where("a.unload_time BETWEEN ? AND ?", startTime, endTime).
		Where("b.shipper_type = ? AND b.is_delete = ?", 1, 0).
		Rows()
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	mongoClient := model.NewMongo()
	defer mongoClient.Disconnect(context.Background())
	coll := mongoClient.Database("admin").Collection("vehicle_track")

	tracks := []vehicleTrack{}

	notes := make(map[string]map[string]string)
	for rows.Next() {
		var transportationNumber string
		var companyName string
		var transportationPlate string
		if err := rows.Scan(&transportationNumber, &companyName, &transportationPlate); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}
		notes[transportationNumber] = map[string]string{
			"transportation_number": transportationNumber,
			"company_name":          companyName,
			"transportation_plate":  transportationPlate,
		}
	}

	randomNotes := make(map[string]map[string]string)
	for range 10 {
		transportationNumber, _, err := toolbox.GetRandomFromMap(notes)
		if err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}
		randomNotes[transportationNumber] = notes[transportationNumber]
	}

	for _, v := range randomNotes {
		transportationNumber := v["transportation_number"]
		companyName := v["company_name"]
		transportationPlate := v["transportation_plate"]

		checkTimeStart, checkTimeEnd, err := getRandomTimesInRange(time.Now().Add(-24 * time.Hour))
		if err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}

		exceptionDisplay := "正常"
		var result bson.M
		if err := coll.FindOne(
			context.TODO(),
			bson.D{{Key: "_id", Value: transportationNumber}},
		).Decode(&result); err != nil {
			logger.Stdout.Error(err.Error())
			exceptionDisplay = "轨迹不完整/轨迹无结果"
		}

		tracks = append(tracks, vehicleTrack{
			checkDate:            time.Now().Add(-24 * time.Hour).Format("2006-01-02"),
			transportationNumber: transportationNumber,
			companyName:          companyName,
			transportationPlate:  transportationPlate,
			exceptionDisplay:     exceptionDisplay,
			checkTimeStart:       checkTimeStart.Format("2006-01-02 15:04:05"),
			checkTimeEnd:         checkTimeEnd.Format("2006-01-02 15:04:05"),
			ageing:               fmt.Sprintf("%0.2f分钟", checkTimeEnd.Sub(checkTimeStart).Minutes()),
			auditor:              "赵晨影",
		})
	}

	f := excelize.NewFile()
	defer f.Close()

	// 日期	运单号	公司名称	车牌号	异常分类	开始时间	结束时间	完成时效	抽查人
	f.SetCellValue("Sheet1", "A1", "日期")
	f.SetCellValue("Sheet1", "B1", "运单号")
	f.SetCellValue("Sheet1", "C1", "公司名称")
	f.SetCellValue("Sheet1", "D1", "车牌号")
	f.SetCellValue("Sheet1", "E1", "异常分类")
	f.SetCellValue("Sheet1", "F1", "开始时间")
	f.SetCellValue("Sheet1", "G1", "结束时间")
	f.SetCellValue("Sheet1", "H1", "完成时效")
	f.SetCellValue("Sheet1", "I1", "抽查人")

	cellIndex := 2
	for _, track := range tracks {
		f.SetCellValue("Sheet1", fmt.Sprintf("A%d", cellIndex), track.checkDate)
		f.SetCellValue("Sheet1", fmt.Sprintf("B%d", cellIndex), track.transportationNumber)
		f.SetCellValue("Sheet1", fmt.Sprintf("C%d", cellIndex), track.companyName)
		f.SetCellValue("Sheet1", fmt.Sprintf("D%d", cellIndex), track.transportationPlate)
		f.SetCellValue("Sheet1", fmt.Sprintf("E%d", cellIndex), track.exceptionDisplay)
		f.SetCellValue("Sheet1", fmt.Sprintf("F%d", cellIndex), track.checkTimeStart)
		f.SetCellValue("Sheet1", fmt.Sprintf("G%d", cellIndex), track.checkTimeEnd)
		f.SetCellValue("Sheet1", fmt.Sprintf("H%d", cellIndex), track.ageing)
		f.SetCellValue("Sheet1", fmt.Sprintf("I%d", cellIndex), track.auditor)
		cellIndex++
	}

	st, _ := time.Parse("2006-01-02", startTime)
	et, _ := time.Parse("2006-01-02", endTime)
	xlsxName := fmt.Sprintf("%s-%s车辆轨迹抽查记录.xlsx", st.Format("20060102"), et.Format("20060102"))
	if err := f.SaveAs(xlsxName); err != nil {
		logger.Stdout.Error(err.Error())
	}

	// 发送文件到飞书指定用户
	fileKey, err := lark.UploadXlsFile(xlsxName)
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	if err := lark.SendFileMessage(lark.ReceivesID.ZhaoChenYing, fileKey); err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	os.Remove(xlsxName)

	logger.Stdout.Info(fmt.Sprintf("%s-%s车辆轨迹抽查记录已发送", st.Format("20060102"), et.Format("20060102")))
}

func PayeeFinance(startTime, endTime string) {

	rows, err := model.DB.Table("tms_transport_note AS a").
		Joins("JOIN tms_driver AS b ON a.payee_id = b.driver_id").
		Select([]string{"b.driver_name", "b.driver_id", "a.transportation_driver_id", "a.freight_amount",
			"a.service_charge", "a.freight_paid", "a.service_fee_paid", "a.payee_id", "a.transportation_plate"}).
		Where("a.waybill_status NOT IN (?)", []int{6, 7}).
		Where("a.loading_time BETWEEN ? AND ?", startTime, endTime).
		Rows()
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	names := make(map[string]string)
	drivers := make(map[string][]string)
	plates := make(map[string][]string)
	payees := make(map[string]map[string]float64)
	for rows.Next() {
		var driverName string
		var driverId string
		var transportationDriverId string
		var freightAmount float64
		var serviceCharge float64
		var freightPaid sql.NullFloat64
		var serviceFeePaid sql.NullFloat64
		var payeeId string
		var transportationPlate string
		if err := rows.Scan(&driverName, &driverId, &transportationDriverId, &freightAmount, &serviceCharge, &freightPaid, &serviceFeePaid, &payeeId, &transportationPlate); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}

		amount := freightAmount + serviceCharge
		if freightPaid.Valid && freightPaid.Float64 > 0 {
			amount = freightPaid.Float64 + serviceFeePaid.Float64
		}

		if _, ok := payees[payeeId]; !ok {
			payees[payeeId] = map[string]float64{
				"amount":  0,
				"drivers": 0,
				"notes":   0,
				"plate":   0,
			}
			names[payeeId] = driverName
			drivers[payeeId] = []string{}
		}

		if payeeId != transportationDriverId {
			payees[payeeId]["amount"] += amount
			payees[payeeId]["notes"] += 1

			if !lo.Contains(drivers[payeeId], transportationDriverId) {
				payees[payeeId]["drivers"] += 1
				drivers[payeeId] = append(drivers[payeeId], transportationDriverId)
			}
			if !lo.Contains(plates[payeeId], transportationPlate) {
				payees[payeeId]["plate"] += 1
				plates[payeeId] = append(plates[payeeId], transportationPlate)
			}
		}
	}

	f := excelize.NewFile()
	defer f.Close()

	f.SetCellValue("Sheet1", "A1", "车主姓名")
	f.SetCellValue("Sheet1", "B1", "司机数量")
	f.SetCellValue("Sheet1", "C1", "车辆数量")
	f.SetCellValue("Sheet1", "D1", "运单数量")
	f.SetCellValue("Sheet1", "E1", "运费金额")

	start := 2
	for payeeId, payee := range payees {
		if payee["drivers"] > 0 {
			f.SetCellValue("Sheet1", fmt.Sprintf("A%d", start), names[payeeId])
			f.SetCellValue("Sheet1", fmt.Sprintf("B%d", start), payee["drivers"])
			f.SetCellValue("Sheet1", fmt.Sprintf("C%d", start), payee["plate"])
			f.SetCellValue("Sheet1", fmt.Sprintf("D%d", start), payee["notes"])
			f.SetCellValue("Sheet1", fmt.Sprintf("E%d", start), payee["amount"])
			start++
		}
	}

	st, _ := time.Parse("2006-01-02", startTime)
	et, _ := time.Parse("2006-01-02", endTime)
	filename := fmt.Sprintf("%s-%s车主运费统计.xlsx", st.Format("20060102"), et.Format("20060102"))
	if err := f.SaveAs(filename); err != nil {
		logger.Stdout.Error(err.Error())
	}

	logger.Stdout.Info("车主运费统计Done")
}
