package tasks

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"time"
	"wlhy/model"
	"wlhy/toolbox/logger"
)

func ExpiredCertificate() {
	driverLimits := 200
	vehicleLimits := 200

	// 扫描司机信息的过期证件
	driverRows, err := model.DB.Table("tms_driver").
		Select([]string{"id", "id_card_end_date", "driver_license_exp_date", "work_certificate_exp_date"}).
		Where("audit_status = ? OR attestation_status = ?", 2, 2).
		Where("is_delete = ?", 0).
		Rows()
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	driverTotal := 0
	for driverRows.Next() {
		var id string
		var idCardEndDate sql.NullTime
		var driverLicenseExpDate sql.NullTime
		var workCertificateExpDate sql.NullTime
		if err := driverRows.Scan(&id, &idCardEndDate, &driverLicenseExpDate, &workCertificateExpDate); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}

		driverUpdateData := make(map[string]any)
		if idCardEndDate.Valid && idCardEndDate.Time.Before(time.Now()) {
			// 司机身份证已过期
			idCardFailType := processFailType(1)
			auditFailType := addFailType(idCardFailType, 1, "证件已过期，请上传有效期内证件")
			driverUpdateData["audit_status"] = 3
			driverUpdateData["audit_fail_type"] = auditFailType
		}

		driverFailType := processFailType(2)
		if driverLicenseExpDate.Valid && driverLicenseExpDate.Time.Before(time.Now()) {
			// 司机驾驶证已过期
			attestationFailType := addFailType(driverFailType, 2, "证件已过期，请上传有效期内证件")
			driverUpdateData["attestation_status"] = 3
			driverUpdateData["attestation_fail_type"] = attestationFailType
		}

		if workCertificateExpDate.Valid && workCertificateExpDate.Time.Before(time.Now()) {
			// 司机从业资格证已过期
			attestationFailType := addFailType(driverFailType, 3, "证件已过期，请上传有效期内证件")
			driverUpdateData["attestation_status"] = 3
			driverUpdateData["attestation_fail_type"] = attestationFailType
		}

		if len(driverUpdateData) > 0 {
			driverTotal += 1
			if driverTotal >= driverLimits {
				break
			}
			// fmt.Printf("%+v\n", driverUpdateData)
			if err := model.DB.Table("tms_driver").
				Where("id = ?", id).
				Updates(driverUpdateData).Error; err != nil {
				logger.Stdout.Error(err.Error())
			}
		}
	}
	logger.Stdout.Info(fmt.Sprintf("司机信息过期证件共计%d条已更新审核状态\n", driverTotal))

	// 扫描车辆信息的过期证件
	vehicleRows, err := model.DB.Table("tms_vehicle").
		Select([]string{"id", "transport_permit_exp_date", "driving_permit_exp_date"}).
		Where("audit_status = ?", 1).
		Where("is_delete = ?", 0).
		Rows()
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	vehicleTotal := 0
	for vehicleRows.Next() {
		var id string
		var transportPermitExpDate sql.NullTime
		var drivingPermitExpDate sql.NullTime
		if err := vehicleRows.Scan(&id, &transportPermitExpDate, &drivingPermitExpDate); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}

		vehicleUpdateData := make(map[string]any)
		vehicleAuditFailType := processFailType(3)
		if transportPermitExpDate.Valid && transportPermitExpDate.Time.Before(time.Now()) {
			// 车辆道路运输证已过期
			tmpAuditFailType := addFailType(vehicleAuditFailType, 6, "证件已过期，请上传有效期内证件")
			vehicleUpdateData["audit_status"] = 2
			vehicleUpdateData["audit_fail_type"] = tmpAuditFailType
		}

		if drivingPermitExpDate.Valid && drivingPermitExpDate.Time.Before(time.Now()) {
			// 车辆行驶证已过期（年检页）
			tmpAuditFailType := addFailType(vehicleAuditFailType, 5, "证件已过期，请上传有效期内证件")
			vehicleUpdateData["audit_status"] = 2
			vehicleUpdateData["audit_fail_type"] = tmpAuditFailType
		}

		if len(vehicleUpdateData) > 0 {
			vehicleTotal += 1
			if vehicleTotal >= vehicleLimits {
				break
			}
			// fmt.Printf("%+v\n", vehicleUpdateData)
			if err := model.DB.Table("tms_vehicle").
				Where("id = ?", id).
				Updates(vehicleUpdateData).Error; err != nil {
				logger.Stdout.Error(err.Error())
			}
		}
	}
	logger.Stdout.Info(fmt.Sprintf("车辆信息过期证件共计%d条已更新审核状态\n", vehicleTotal))
}

type FailType struct {
	Type   int      `json:"type"`
	Reason []string `json:"reason"`
}

func processFailType(category int) []FailType {
	newFailType := []FailType{}

	if category == 1 {
		newFailType = []FailType{
			{Type: 1, Reason: []string{}},
		}
	}
	if category == 2 {
		newFailType = []FailType{
			{Type: 2, Reason: []string{}},
			{Type: 3, Reason: []string{}},
		}
	}
	if category == 3 {
		newFailType = []FailType{
			{Type: 4, Reason: []string{}},
			{Type: 5, Reason: []string{}},
			{Type: 6, Reason: []string{}},
			{Type: 7, Reason: []string{}},
			{Type: 8, Reason: []string{}},
			{Type: 9, Reason: []string{}},
			{Type: 10, Reason: []string{}},
		}
	}

	return newFailType
}

func addFailType(failType []FailType, Type int, Reason string) string {
	for k, v := range failType {
		if v.Type == Type {
			failType[k].Reason = []string{Reason}
		}
	}

	vv, _ := json.Marshal(failType)
	return string(vv)
}
