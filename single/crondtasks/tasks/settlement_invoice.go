package tasks

import (
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"sort"
	"strings"
	"text/template"
	"time"
	"wlhy/model"
	"wlhy/thirdparty/oss"
	"wlhy/toolbox"
	"wlhy/toolbox/logger"

	"github.com/samber/lo"
)

// SettlementInvoice 生成发票结算单
func SettlementInvoice() {
	// 基础设置
	funcMap := template.FuncMap{
		"dec": func(n int) int {
			return n - 1
		},
	}
	invoiceHtmlTemplatePath := "template/*.html"
	tmpl := template.Must(template.New("settlement_invoice.html").Funcs(funcMap).ParseGlob(invoiceHtmlTemplatePath))
	dirName := "./"

	cli, err := oss.NewOSSByBucket("cfhswlhyhz")
	if err != nil {
		logger.Stdout.Error(fmt.Sprintf("连接OSS失败: %v\n", err))
		return
	}

	// 数据模型
	type InvoiceT struct {
		ID                string  `gorm:"column:id"`
		InvoiceTitle      string  `gorm:"column:invoice_title"`
		InvoiceValue      float64 `gorm:"column:invoice_value"`
		InvoiceState      int     `gorm:"column:invoice_state"`
		TransportationIDs string  `gorm:"column:transportation_ids"`
		ApplyNumber       string  `gorm:"column:apply_number"`
	}

	type WaybillT struct {
		ID                       string    `gorm:"column:id"`
		UnivalentShould          float64   `gorm:"column:univalent_should"`
		RoadLossSettlementMethod int       `gorm:"column:road_loss_settlement_method"`
		FreightPaid              float64   `gorm:"column:freight_paid"`
		ServiceFeePaid           float64   `gorm:"column:service_fee_paid"`
		LoadingTime              time.Time `gorm:"column:loading_time"`
		UnloadTime               time.Time `gorm:"column:unload_time"`
		LoadingNumber            float64   `gorm:"column:loading_number"`
		UnloadNumber             float64   `gorm:"column:unload_number"`
		TransportationPlate      string    `gorm:"column:transportation_plate"`
		FinishedTime             time.Time `gorm:"column:finished_time"`
		OrderID                  string    `gorm:"column:order_id"`
		AppointShipper           string    `gorm:"column:appoint_shipper"`
		LoadingName              string    `gorm:"column:loading_name"`
		LoadingAddress           string    `gorm:"column:loading_address"`
		UnloadName               string    `gorm:"column:unload_name"`
		UnloadAddress            string    `gorm:"column:unload_address"`
		GoodsName                string    `gorm:"column:goods_name"`
		GoodsUntis               string    `gorm:"column:goods_untis"`
	}

	type OverviewT struct {
		SerialNumber   int
		AppointShipper string
		Address        string
		GoodsName      string
		GoodsUntis     string
		VehicleNum     int
		FinishedTime   string
		weight         float64
		price          float64
		invoiceAmount  float64
		Weight         string
		Price          string
		InvoiceAmount  string
		plates         []string
		finishedTimes  []time.Time
	}

	type ItemT struct {
		SerialNumber int
		Name         string
		Unit         string
		Plate        string
		Weight       float64
		Amount       float64
		TaxRate      float64
		Tax          float64
		Price        float64
		PriceMethod  int
		CodeVersion  string
		Code         string
	}

	var invoices []InvoiceT
	if err := model.DB.Table("tms_invoice_list").
		Select([]string{"id", "invoice_title", "invoice_value", "invoice_state", "transportation_ids", "apply_number"}).
		Where("is_generate_settlement_invoice = ?", 0).
		Where("invoice_state IN (?)", []int{0, 2}).
		Where("create_time >= ?", "2025-04-01").
		Find(&invoices).Error; err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	taxRows, err := model.DB.Table("sys_taxcompany AS a").
		Joins("JOIN tms_shipper AS b ON a.id = b.select_company_id").
		Select([]string{"a.platform_company_name", "b.company_name"}).
		Where("a.is_delete = ?", 0).
		Where("b.shipper_type = ? AND b.is_delete = ?", 1, 0).
		Rows()
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	taxMap := make(map[string]string)
	for taxRows.Next() {
		var platformCompanyName string
		var companyName string
		if err := taxRows.Scan(&platformCompanyName, &companyName); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}
		taxMap[companyName] = platformCompanyName
	}

	for _, invoice := range invoices {
		itemSerialNumber := 1
		overview := make(map[string]OverviewT)
		var items []ItemT
		var waybills []WaybillT

		if err := model.DB.Table("tms_transport_note AS a").
			Joins("JOIN tms_order_history AS b ON a.order_id = b.id AND a.order_version_no = b.version_no").
			Select([]string{"a.id", "a.univalent_should", "a.road_loss_settlement_method", "a.freight_paid",
				"a.service_fee_paid", "a.loading_time", "a.unload_time", "a.loading_number", "a.unload_number",
				"a.transportation_plate", "a.finished_time", "a.order_id", "b.appoint_shipper", "b.loading_name",
				"b.loading_address", "b.unload_name", "b.unload_address", "b.goods_name", "b.goods_untis"}).
			Where("a.id IN (?)", strings.Split(invoice.TransportationIDs, ",")).
			Find(&waybills).Error; err != nil {
			logger.Stdout.Error(err.Error())
			return
		}

		for _, waybill := range waybills {
			// 结算重量
			weight := waybill.LoadingNumber
			switch waybill.RoadLossSettlementMethod {
			case 1:
				weight = waybill.LoadingNumber
			case 2:
				weight = waybill.UnloadNumber
			case 3:
				weight = max(waybill.LoadingNumber, waybill.UnloadNumber)
			case 4:
				weight = min(waybill.LoadingNumber, waybill.UnloadNumber)
			}

			// 汇总数据
			if _, ok := overview[waybill.OrderID]; !ok {
				loadingAddress := waybill.LoadingName + strings.ReplaceAll(waybill.LoadingAddress, waybill.LoadingName, "")
				unloadAddress := waybill.UnloadName + strings.ReplaceAll(waybill.UnloadAddress, waybill.UnloadName, "")
				address := loadingAddress + "-" + unloadAddress

				overview[waybill.OrderID] = OverviewT{
					AppointShipper: waybill.AppointShipper,
					Address:        address,
					GoodsName:      waybill.GoodsName,
					GoodsUntis:     waybill.GoodsUntis,
					weight:         weight,
					price:          0,
					invoiceAmount:  waybill.FreightPaid + waybill.ServiceFeePaid,
					plates:         []string{waybill.TransportationPlate},
					finishedTimes:  []time.Time{waybill.FinishedTime},
				}
			} else {
				t := overview[waybill.OrderID]
				t.weight += weight
				t.invoiceAmount += waybill.FreightPaid + waybill.ServiceFeePaid
				t.plates = append(t.plates, waybill.TransportationPlate)
				t.finishedTimes = append(t.finishedTimes, waybill.FinishedTime)
				overview[waybill.OrderID] = t
			}

			// 明细数据
			items = append(items, ItemT{
				SerialNumber: itemSerialNumber,
				Name:         "无运输工具承运道路货物运输服务",
				Unit:         waybill.GoodsUntis,
				Plate:        waybill.TransportationPlate,
				Weight:       weight,
				Amount:       toolbox.RoundToDecimal(waybill.FreightPaid+waybill.ServiceFeePaid, 2),
				TaxRate:      0.09,
				Tax:          toolbox.RoundToDecimal((waybill.FreightPaid+waybill.ServiceFeePaid)/1.09*0.09, 2),
				Price:        toolbox.RoundToDecimal((waybill.FreightPaid+waybill.ServiceFeePaid)/weight, 3),
				PriceMethod:  3,
				CodeVersion:  "30.0",
				Code:         "3010502020200000000",
			})
			itemSerialNumber++
		}

		htmlFilename := filepath.Join(dirName, fmt.Sprintf("%s.html", invoice.ApplyNumber))
		pdfFilename := filepath.Join(dirName, fmt.Sprintf("%s.pdf", invoice.ApplyNumber))
		f, _ := os.Create(htmlFilename)

		// 处理数据
		totalWeight := 0.0
		totalInvoiceAmount := 0.0
		var overviewSlice []OverviewT
		overviewSerialNumber := 1
		for _, v := range overview {
			v.SerialNumber = overviewSerialNumber
			overviewSerialNumber++

			totalWeight += v.weight
			totalInvoiceAmount += v.invoiceAmount

			// 计算含税单价
			v.price = toolbox.RoundToDecimal(v.invoiceAmount/v.weight, 2)
			v.weight = toolbox.RoundToDecimal(v.weight, 2)
			v.invoiceAmount = toolbox.RoundToDecimal(v.invoiceAmount, 2)

			v.Price = fmt.Sprintf("%.2f", v.price)
			v.Weight = fmt.Sprintf("%.2f", v.weight)
			v.InvoiceAmount = fmt.Sprintf("%.2f", v.invoiceAmount)

			v.VehicleNum = len(v.plates)

			sort.Slice(v.finishedTimes, func(i, j int) bool {
				return v.finishedTimes[i].Before(v.finishedTimes[j])
			})
			v.FinishedTime = v.finishedTimes[0].Format("2006.01.02") + "-" + v.finishedTimes[len(v.finishedTimes)-1].Format("2006.01.02")

			overviewSlice = append(overviewSlice, v)
		}

		// 拆分数据
		overviewDataChunk := lo.Chunk(overviewSlice, 12)

		itemDataChunk := make([][]ItemT, 0)
		if len(items) > 12 {
			itemDataChunk1 := [][]ItemT{items[:12]}
			itemDataChunk2 := lo.Chunk(items[12:], 14)

			itemDataChunk = append(itemDataChunk, itemDataChunk1...)
			itemDataChunk = append(itemDataChunk, itemDataChunk2...)
		} else {
			itemDataChunk = [][]ItemT{items}
		}

		htmlData := struct {
			OverviewDataChunk   [][]OverviewT
			ItemDataChunk       [][]ItemT
			InvoiceTitle        string
			ApplyNumber         string
			TotalWeight         string
			TotalInvoiceAmount  string
			PlatformCompanyName string
		}{
			OverviewDataChunk:   overviewDataChunk,
			ItemDataChunk:       itemDataChunk,
			InvoiceTitle:        invoice.InvoiceTitle,
			ApplyNumber:         invoice.ApplyNumber,
			TotalWeight:         fmt.Sprintf("%.2f", toolbox.RoundToDecimal(totalWeight, 2)),
			TotalInvoiceAmount:  fmt.Sprintf("%.2f", toolbox.RoundToDecimal(totalInvoiceAmount, 2)),
			PlatformCompanyName: taxMap[invoice.InvoiceTitle],
		}

		// 生成结算html
		if err := tmpl.ExecuteTemplate(f, "settlement_invoice.html", htmlData); err != nil {
			logger.Stdout.Error(fmt.Sprintf("生成结算单html失败: %v\n", err))
			continue
		}

		// 生成pdf
		cmd := exec.Command("wkhtmltopdf", "--no-outline", "--margin-top", "0", "--margin-right", "0", "--margin-bottom", "0", "--margin-left", "0", "--disable-smart-shrinking", "--load-media-error-handling", "abort", "--page-width", "297mm", "--page-height", "210mm", htmlFilename, pdfFilename)
		if err := cmd.Run(); err != nil {
			logger.Stdout.Error(fmt.Sprintf("生成结算单pdf失败: %v\n", err))
			continue
		}

		// 上传OSS
		if err := cli.PutObjectFromFile(filepath.Join("settlement_invoices", filepath.Base(pdfFilename)), pdfFilename); err != nil {
			logger.Stdout.Error(fmt.Sprintf("上传结算单到OSS失败: %v\n", err))
			continue
		}

		// 更新结算单状态
		if err := model.DB.Table("tms_invoice_list").Where("id = ?", invoice.ID).Updates(map[string]any{
			"is_generate_settlement_invoice": 1,
			"settlement_invoice_url":         "https://cfhswlhyhz.oss-cn-hangzhou.aliyuncs.com/settlement_invoices/" + filepath.Base(pdfFilename),
		}).Error; err != nil {
			logger.Stdout.Error(fmt.Sprintf("更新结算单状态失败: %v\n", err))
			continue
		}

		// 删除生成的html
		os.Remove(htmlFilename)
		os.Remove(pdfFilename)
		logger.Stdout.Info(fmt.Sprintf("生成结算单成功: %s\n", invoice.ApplyNumber))
	}
}
