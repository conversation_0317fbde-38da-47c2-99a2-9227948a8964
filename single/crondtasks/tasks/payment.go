package tasks

import (
	"bytes"
	"database/sql"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"regexp"
	"strings"
	"time"
	"wlhy/model"
	"wlhy/thirdparty/cmb"
	"wlhy/toolbox"
	"wlhy/toolbox/logger"

	"github.com/samber/lo"
	"gorm.io/gorm"
)

// UpdateWithdrawalState 更新提现状态
func UpdateWithdrawalState() {
	startTime := time.Now()
	rows, err := model.DB.Table("tms_withdraw_application").
		Select([]string{"id", "application_status", "transfer_time", "applicant_name", "applicant_phone", "remark",
			"apply_amount"}).
		Where("is_delete = ?", 0).
		Where("application_status IN (?)", []int{1, 3}).
		Where("create_time >= ?", "2025-06-01").
		Rows()
	if err != nil {
		logger.Stdout.Error(err.<PERSON>rror())
		return
	}

	payingIDs := []string{}
	failIDs := []string{}
	failRecords := []map[string]string{}
	for rows.Next() {
		var id string
		var applicationStatus int
		var transferTime sql.NullTime
		var applicantName string
		var applicantPhone string
		var remark string
		var applyAmount float64
		if err := rows.Scan(&id, &applicationStatus, &transferTime, &applicantName, &applicantPhone, &remark,
			&applyAmount); err != nil {
			continue
		}

		if applicationStatus == 1 {
			payingIDs = append(payingIDs, id)
		}
		if applicationStatus == 3 && time.Since(transferTime.Time) > time.Hour*6 {
			failIDs = append(failIDs, id)
			failRecords = append(failRecords, map[string]string{
				"id":              id,
				"applicant_name":  applicantName,
				"applicant_phone": applicantPhone,
				"remark":          remark,
				"apply_amount":    fmt.Sprintf("%.2f", applyAmount),
				"transfer_time":   transferTime.Time.Format("2006-01-02 15:04:05"),
			})
		}
	}

	logger.Stdout.Info(fmt.Sprintf("提现状态待更新数量:%d, 提现失败数量:%d", len(payingIDs), len(failIDs)))

	// 获取登录token
	rdb := model.NewRedis(1)
	userID := "1732305280388886530"
	token, err := toolbox.HszyToken(rdb, userID)
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	if len(payingIDs) > 0 {
		// 分批次调用接口，每批100个
		payingChunk := lo.Chunk(payingIDs, 100)
		for _, v := range payingChunk {
			reWithdrawal(v, token, false)
		}
	}

	if len(failIDs) > 0 {
		// 分批次调用接口，每批100个
		failChunk := lo.Chunk(failIDs, 10)
		for _, v := range failChunk {
			reWithdrawal(v, token, true)
		}
	}

	// 发送飞书通知
	if len(failRecords) > 0 {
		var failMsg string
		for _, v := range failRecords {
			failMsg += fmt.Sprintf("<u>%s 手机号:%s 金额:%s 转账时间:%s 原因:%s</u>\n", v["applicant_name"], v["applicant_phone"], v["apply_amount"], v["transfer_time"], v["remark"])
		}
		failMsg = fmt.Sprintf("<b>以下司机提现失败，请及时联系司机处理</b>\n%+v", strings.TrimRight(failMsg, "\n"))

		lark.SendTextMessage(lark.ReceivesID.ChaiRuiPeng, failMsg)
		lark.SendTextMessage(lark.ReceivesID.MengFanXue, failMsg)
	}

	logger.Stdout.Info(fmt.Sprintf("重提现处理完毕，耗时:%v", time.Since(startTime)))
}

// reWithdrawal 重新调用提现相关的接口
func reWithdrawal(ids []string, token string, isRePay bool) {
	baseUrl := "https://op.cfhszy.com/gw/hszy-payment/ntocc/tmsWithdrawApplication/updateStatus"
	formData := map[string]any{
		"ids":           strings.Join(ids, ","),
		"paymentMethod": 6,
	}

	if isRePay {
		baseUrl = "https://op.cfhszy.com/gw/hszy-payment/ntocc/tmsWithdrawApplication/transferAccounts"
		formData["payPassword"] = "123456"
	}

	client := &http.Client{}
	jsonStr, err := json.Marshal(formData)
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	req, err := http.NewRequest("POST", baseUrl, bytes.NewBuffer(jsonStr))
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-Access-Token", token)

	resp, err := client.Do(req)
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		logger.Stdout.Error(resp.Status)
		return
	}
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	logger.Stdout.Info(string(body))
}

// RePay 重新支付
func RePay() {
	rows, err := model.DB.Table("tms_transport_pay_plan").
		Select([]string{"id", "company_no", "receive_mem", "waybill_no", "transport_reason"}).
		Where("state = ?", 3).
		Where("is_repay = ?", 0).
		Where("is_delete = ?", 0).
		Rows()
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	reg := regexp.MustCompile(`.*记账子单元\[(.*?)\]不存在或状态非\[有效\]$`)

	type plan struct {
		ids        []string
		companyNo  string
		sysOrgCode string
		waybillNo  []string
		dmanam     string
	}
	plans := make(map[string]plan)
	sysOrgCodes := map[string]string{
		"a82dc78ba61880beda018fa5eab6c944": "A03",
		"a82dc78ba61880beda018fa5eab6c955": "A07",
	}
	for rows.Next() {
		var id string
		var companyNo string
		var receiveMem string
		var waybillNo string
		var transportReason sql.NullString
		if err := rows.Scan(&id, &companyNo, &receiveMem, &waybillNo, &transportReason); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}

		if reg.MatchString(transportReason.String) {
			if _, ok := plans[receiveMem]; !ok {
				var dmanam string
				if err := model.DB.Table("sys_zhaoshang_account").
					Select([]string{"dmanam"}).
					Where("dmanbr = ?", receiveMem).
					Where("sys_org_code = ?", sysOrgCodes[companyNo]).
					Where("company_id = ?", companyNo).
					Where("is_delete = ?", 0).
					Scan(&dmanam).Error; err != nil {
					logger.Stdout.Error(err.Error())
					continue
				}

				plans[receiveMem] = plan{
					companyNo:  companyNo,
					sysOrgCode: sysOrgCodes[companyNo],
					waybillNo:  []string{waybillNo},
					dmanam:     dmanam,
					ids:        []string{id},
				}
				continue
			}

			p := plans[receiveMem]
			p.ids = append(p.ids, id)
			p.waybillNo = append(p.waybillNo, waybillNo)
			plans[receiveMem] = p
		}
	}
	if len(plans) == 0 {
		return
	}

	cmbClients := map[string]*cmb.CmbBank{
		"A03": cmb.GetHszy(),
		"A07": cmb.GetYbs(),
	}

	repayLogName := fmt.Sprintf("%s.log", time.Now().Format("********"))
	f, err := os.OpenFile("logs/"+repayLogName, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	defer f.Close()

	for dmanbr, v := range plans {
		client := cmbClients[v.sysOrgCode]
		if client == nil {
			logger.Stdout.Error(fmt.Sprintf("未找到%s的 cmb 客户端", dmanbr))
			continue
		}

		// 新建或恢复子单元
		_, err := client.SubAccountAdd(dmanbr, v.dmanam)
		if err != nil {
			logger.Stdout.Error(err.Error())
			if !strings.Contains(err.Error(), "子单元编号已存在") {
				continue
			}
		}

		err = model.DB.Transaction(func(tx *gorm.DB) error {
			// 恢复子单元状态
			if err := tx.Table("sys_zhaoshang_account").
				Where("dmanbr = ?", dmanbr).
				Where("sys_org_code = ?", v.sysOrgCode).
				Where("company_id = ?", v.companyNo).
				Where("is_delete = ?", 0).
				Updates(map[string]any{
					"status":      1,
					"update_time": time.Now().Format("2006-01-02 15:04:05"),
					"update_by":   "sys",
				}).
				Error; err != nil {
				return err
			}

			// 更新支付计划状态
			if err := tx.Table("tms_transport_pay_plan").
				Where("id IN (?)", v.ids).
				Updates(map[string]any{
					"is_repay": 1,
					"reason":   "请重新支付",
				}).
				Error; err != nil {
				return err
			}

			return nil
		})

		if err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}

		for _, waybillNo := range v.waybillNo {
			f.WriteString(fmt.Sprintf("Time:%s Dmanbr:%s WaybillNo: %s\n", time.Now().Format("2006-01-02 15:04:05"), dmanbr, waybillNo))
		}
	}
}
