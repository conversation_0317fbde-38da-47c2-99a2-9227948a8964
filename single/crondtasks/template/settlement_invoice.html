<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Document</title>
    <style>
      @font-face {
        font-family: customFont;
        /*本地字体文件路径*/
        src: url("https://cdn.cfhszy.com/cdn/fonts/DroidSansFallback.ttf");
      }

      * {
        margin: 0;
        padding: 0;
        font-family: "customFont";
      }

      body {
        width: 297mm;
        margin: 0;
        padding: 0;
      }

      .page {
        width: 297mm;
        height: 210mm;
        margin: 0;
        padding: 0;
      }

      .table {
        table-layout: fixed;
        width: 900px;
        font-size: 12px;
        border-collapse: collapse;
        position: relative;
        top: 30px;
        left: 105px;
      }

      .table tr td {
        word-wrap: break-word;
        text-align: center;
        border: 1px solid #000;
      }

      .item-table {
        table-layout: fixed;
        width: 900px;
        font-size: 12px;
        border-collapse: collapse;
        position: relative;
        top: 30px;
        left: 105px;
      }

      .item-table tr td {
        word-wrap: break-word;
        text-align: center;
        border: 1px solid #000;
      }
    </style>
  </head>

  <body>
    {{range $key,$list := .OverviewDataChunk}}
    <div class="page">
      <table class="table">
        <colgroup>
          <col style="width: 30px" />
          <col style="width: 80px" />
          <col style="width: 230px" />

          <col style="width: 120px" />
          <col style="width: 60px" />
          <col style="width: 60px" />
          <col style="width: 60px" />

          <col style="width: 60px" />
          <col style="width: 60px" />
          <col style="width: 140px" />
        </colgroup>

        {{if eq $key 0}}
        <tr>
          <td colspan="10" style="font-weight: bold">红山智运客户开票结算单</td>
        </tr>

        <tr>
          <td colspan="9" style="font-weight: bold">
            客户名称：{{$.InvoiceTitle}}
          </td>
          <td colspan="1" style="font-weight: bold">{{$.ApplyNumber}}</td>
        </tr>

        <tr style="font-weight: bold">
          <td>序号</td>
          <td>发货单位</td>
          <td>起始地-到达地</td>
          <td>结算区间</td>
          <td>车次</td>
          <td>品名</td>
          <td>计量单位</td>
          <td>数量</td>
          <td>含税价格</td>
          <td>开票金额</td>
        </tr>
        {{end}} {{range $list}}
        <tr>
          <td>{{.SerialNumber}}</td>
          <td>{{.AppointShipper}}</td>
          <td>{{.Address}}</td>
          <td>{{.FinishedTime}}</td>
          <td>{{.VehicleNum}}</td>
          <td>{{.GoodsName}}</td>
          <td>{{.GoodsUntis}}</td>
          <td>{{.Weight}}</td>
          <td>{{.Price}}</td>
          <td>{{.InvoiceAmount}}</td>
        </tr>
        {{end}} {{if eq $key (dec (len $.OverviewDataChunk))}}
        <tr>
          <td colspan="7">总计</td>
          <td>{{$.TotalWeight}}</td>
          <td></td>
          <td>{{$.TotalInvoiceAmount}}</td>
        </tr>

        <tr>
          <td colspan="10">{{$.PlatformCompanyName}}</td>
        </tr>

        <tr>
          <td colspan="3" style="text-align: left;padding-left: 10px;">客户核对签字</td>
          <td colspan="4" style="text-align: left;padding-left: 10px;">日期</td>
          <td colspan="3" style="text-align: left;padding-left: 10px;">出纳员签字</td>
        </tr>
        {{end}}
      </table>
    </div>
    {{end}} {{range $key,$list := .ItemDataChunk}}
    <div class="page">
      <table class="item-table">
        <colgroup>
          <col style="width: 34px" />
          <col style="width: 84px" />
          <col style="width: 34px" />
          <col style="width: 80px" />
          <col style="width: 54px" />
          <col style="width: 54px" />
          <col style="width: 34px" />
          <col style="width: 34px" />
          <col style="width: 34px" />
          <col style="width: 54px" />
          <col style="width: 24px" />
          <col style="width: 24px" />
          <col style="width: 54px" />
          <col style="width: 34px" />
          <col style="width: 34px" />
          <col style="width: 84px" />
          <col style="width: 34px" />
          <col style="width: 34px" />
          <col style="width: 24px" />
          <col style="width: 24px" />
          <col style="width: 34px" />
        </colgroup>
        {{if eq $key 0}}
        <tr>
          <td style="font-weight: bold">序号</td>
          <td style="font-weight: bold">货物或应税劳务、服务名称</td>
          <td style="font-weight: bold">计量单位</td>
          <td style="font-weight: bold">规格型号</td>
          <td style="font-weight: bold">数量</td>
          <td style="font-weight: bold">金额</td>
          <td style="font-weight: bold">税率</td>
          <td style="font-weight: bold">商品税目</td>
          <td style="font-weight: bold">折扣金额</td>
          <td style="font-weight: bold">税额</td>
          <td style="font-weight: bold">折扣税额</td>
          <td style="font-weight: bold">折扣率</td>
          <td style="font-weight: bold">单价</td>
          <td style="font-weight: bold">价格方式</td>
          <td style="font-weight: bold">税收分类编码版本号</td>
          <td style="font-weight: bold">税收分类编码</td>
          <td style="font-weight: bold">企业商品编码</td>
          <td style="font-weight: bold">使用优惠正则标识</td>
          <td style="font-weight: bold">零税率标识</td>
          <td style="font-weight: bold">优惠政策说明</td>
          <td style="font-weight: bold">中外合作油气田标识</td>
        </tr>
        {{end}} {{range $list}}
        <tr>
          <td>{{.SerialNumber}}</td>
          <td>{{.Name}}</td>
          <td>{{.Unit}}</td>
          <td>{{.Plate}}</td>
          <td>{{.Weight}}</td>
          <td>{{.Amount}}</td>
          <td>{{.TaxRate}}</td>
          <td></td>
          <td></td>
          <td>{{.Tax}}</td>
          <td></td>
          <td></td>
          <td>{{.Price}}</td>
          <td>{{.PriceMethod}}</td>
          <td>{{.CodeVersion}}</td>
          <td>{{.Code}}</td>
          <td></td>
          <td>0</td>
          <td></td>
          <td></td>
          <td>0</td>
        </tr>
        {{end}}
      </table>
    </div>
    {{end}}
  </body>
</html>
