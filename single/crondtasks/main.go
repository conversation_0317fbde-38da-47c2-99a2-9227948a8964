package main

import (
	"fmt"
	"wlhy/single/crondtasks/tasks"
	"wlhy/toolbox/logger"

	"github.com/robfig/cron/v3"
)

func main() {
	defer func() {
		if r := recover(); r != nil {
			logger.Stdout.Error(fmt.Sprintf("%v", r))
		}
	}()

	c := cron.New(cron.WithLogger(cron.DefaultLogger), cron.WithSeconds())

	// 更新证件过期的司机、车辆审核状态
	c.AddFunc("0 0 0 * * *", tasks.ExpiredCertificate)

	// 重置支付状态
	c.AddFunc("0 * * * * *", tasks.RePay)

	// 更新提现状态
	c.AddFunc("0 */10 * * * *", tasks.UpdateWithdrawalState)

	// 东海投保
	c.AddFunc("0 0 9 * * *", tasks.Insurance)

	// ETC ETC发票
	c.AddFunc("0 0 6 * * *", tasks.Etc)
	c.AddFunc("0 0 9 * * 1", tasks.EtcStatistics)

	// 发送日级运单报表
	c.AddFunc("0 0 8 * * *", tasks.BusinessReports)
	c.AddFunc("0 0 8 * * *", tasks.BusinessReportsFor100k)
	c.AddFunc("0 0 8 * * *", tasks.BusinessReportsForChildCompany)

	// 发送日级风控报表
	c.AddFunc("0 0 8 * * *", tasks.RiskVehicleTrackCheck)
	c.AddFunc("0 0 8 * * *", tasks.RiskReports)
	c.AddFunc("0 0 8 1,16 * *", tasks.RiskReport10Kilometer)
	c.AddFunc("0 0 8 1 * *", tasks.RiskMonthReports)
	c.AddFunc("0 0 8 1 * *", tasks.OverloadReportMonth)

	// 发送财务运单和发票数据
	// c.AddFunc("0 50 8 * * *", tasks.FinanceTransportNote)
	c.AddFunc("0 0 9 * * *", tasks.FinanceInvoice)
	c.AddFunc("0 0 9 * * *", tasks.FinanceInvoiceBatch)

	// 发送客户账户余额
	c.AddFunc("0 50 8 * * *", tasks.ShipperAccount)

	// 统计营业额数据并发送邮件
	c.AddFunc("0 0 7 * * 1", tasks.SumOfBusiness)
	c.AddFunc("0 0 7 1 * *", tasks.SumOfBusinessMonth)
	c.AddFunc("0 0 7 * * *", tasks.AgentFreightAmount)
	c.AddFunc("0 0 7 1 * *", tasks.AgentFreightAmountMonth)
	c.AddFunc("0 0 6 * * *", tasks.MonthFreightAmount)
	c.AddFunc("0 0 9 1 * *", tasks.ShangWuMonth)

	// 发送已收运费数据
	c.AddFunc("0 0 9 * * *", tasks.FinancePaidAmount)

	// 生成发票结算单
	c.AddFunc("0 */5 * * * *", tasks.SettlementInvoice)

	// 同步货主配置
	c.AddFunc("0 */30 * * * *", tasks.UpdateShipperConfig)

	// 更改运单状态
	c.AddFunc("0 * * * * *", tasks.UpdateShipperPayStatus)

	// 更改回单已签收但运单状态为待验收的运单状态为待支付
	c.AddFunc("0 * * * * *", tasks.UpdateTransportExceptionReceipt)

	// 查询运单数据并发送邮件
	c.AddFunc("0 40 8 1 * *", tasks.LocationTransportNote)

	// 发送玖汇磅单图片
	c.AddFunc("0 30 8 * * *", tasks.JiuHui)

	// 查询融通运单数据并发送邮件
	c.AddFunc("0 0 9 * * *", tasks.RongTong)

	// 抓取迪卡侬货号数据
	c.AddFunc("0 0 9 * * 1,3,5", tasks.CrawlerData)

	// 检查中物联余额
	c.AddFunc("0 0 9 * * *", tasks.CheckZWLBalance)

	// 多蒙德客户派油
	c.AddFunc("0 */10 * * * *", tasks.DuoMengDeOil)

	c.Start()

	// 保持主协程阻塞
	select {}
}
