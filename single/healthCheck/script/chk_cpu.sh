#!/bin/bash

# 设置CPU使用率阈值
THRESHOLD=80

# 获取CPU总占用率（用户态 + 内核态）并转换为浮动格式
cpu_usage=$(top -bn1 | grep "Cpu(s)" | sed "s/.*, *\([0-9.]*\)%* id.*/\1/" | awk '{print 100 - $1}')

# 输出当前的CPU占用率
# echo "当前总CPU占用率: ${cpu_usage}%"
echo "${cpu_usage}"

# 判断是否超过阈值
# if (($(echo "$cpu_usage > $THRESHOLD" | bc -l))); then
#     echo "CPU负载异常，警告：CPU占用率超过 ${THRESHOLD}%！"
# else
#     echo "CPU负载正常，低于 ${THRESHOLD}%。"
# fi
