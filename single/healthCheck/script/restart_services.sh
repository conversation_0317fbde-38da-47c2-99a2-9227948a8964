#!/bin/bash

# 获取所有包含 'hszy' 的进程 PID
pids=$(ps aux | grep 'hszy' | grep -v 'grep' | awk '{print $2}')

# 检查是否有匹配的进程
if [ -z "$pids" ]; then
    echo "没有找到包含 'hszy' 的进程。"
    exit 0
fi

# 结束这些进程
echo "正在结束以下进程："
echo "$pids"
for pid in $pids; do
    kill $pid
    echo "进程 $pid 已执行kill。"
done

# 监控进程是否已全部结束
echo "正在监控进程是否已全部结束..."
while true; do
    remaining_pids=$(ps aux | grep 'hszy' | grep -v 'grep' | awk '{print $2}')
    if [ -z "$remaining_pids" ]; then
        echo "所有包含 'hszy' 的进程已全部结束。"
        break
    fi
    echo "等待进程结束..."
    sleep 2
done

# 等待应用结束后重新启动
echo "正在重新启动 'hszy' 相关进程..."

nohup java -server -Xms256m -Xmx512m -XX:MetaspaceSize=128m -XX:-UseAdaptiveSizePolicy -XX:+DisableExplicitGC -XX:+PrintGCDetails -Dfile.encoding=UTF-8 -javaagent:/home/<USER>/opt/soft/elastic-apm-agent-1.52.1.jar -Delastic.apm.service_name=hszy-interface -Delastic.apm.secret_token=1234567890q1 -Delastic.apm.server_url=http://172.29.234.194:8200 -Delastic.apm.environment=prd -Delastic.apm.application_packages=com.hszy -Dspring.profiles.active=prd -jar /home/<USER>/opt/newbackend/bin/hszy-external-interface.jar >/dev/null 2>&1 &

nohup java -server -Xms1024m -Xmx1024m -XX:MetaspaceSize=128m -XX:-UseAdaptiveSizePolicy -XX:+DisableExplicitGC -XX:+PrintGCDetails -Dfile.encoding=UTF-8 -javaagent:/home/<USER>/opt/soft/elastic-apm-agent-1.52.1.jar -Delastic.apm.service_name=hszy-order -Delastic.apm.secret_token=1234567890q1 -Delastic.apm.server_url=http://172.29.234.194:8200 -Delastic.apm.environment=prd -Delastic.apm.application_packages=com.hszy -Dspring.profiles.active=prd -jar /home/<USER>/opt/newbackend/bin/hszy-order.jar >/dev/null 2>&1 &

nohup java -server -Xms256m -Xmx512m -XX:MetaspaceSize=128m -XX:-UseAdaptiveSizePolicy -XX:+DisableExplicitGC -XX:+PrintGCDetails -Dfile.encoding=UTF-8 -javaagent:/home/<USER>/opt/soft/elastic-apm-agent-1.52.1.jar -Delastic.apm.service_name=hszy-payment -Delastic.apm.secret_token=1234567890q1 -Delastic.apm.server_url=http://172.29.234.194:8200 -Delastic.apm.environment=prd -Delastic.apm.application_packages=com.hszy -Dspring.profiles.active=prd -jar /home/<USER>/opt/newbackend/bin/hszy-payment.jar >/dev/null 2>&1 &

nohup java -server -Xms256m -Xmx512m -XX:MetaspaceSize=128m -XX:-UseAdaptiveSizePolicy -XX:+DisableExplicitGC -XX:+PrintGCDetails -Dfile.encoding=UTF-8 -javaagent:/home/<USER>/opt/soft/elastic-apm-agent-1.52.1.jar -Delastic.apm.service_name=hszy-settlement -Delastic.apm.secret_token=1234567890q1 -Delastic.apm.server_url=http://172.29.234.194:8200 -Delastic.apm.environment=prd -Delastic.apm.application_packages=com.hszy -Dspring.profiles.active=prd -jar /home/<USER>/opt/newbackend/bin/hszy-settlement.jar >/dev/null 2>&1 &

nohup java -server -Xms256m -Xmx512m -XX:MetaspaceSize=128m -XX:-UseAdaptiveSizePolicy -XX:+DisableExplicitGC -XX:+PrintGCDetails -Dfile.encoding=UTF-8 -javaagent:/home/<USER>/opt/soft/elastic-apm-agent-1.52.1.jar -Delastic.apm.service_name=hszy-transport -Delastic.apm.secret_token=1234567890q1 -Delastic.apm.server_url=http://172.29.234.194:8200 -Delastic.apm.environment=prd -Delastic.apm.application_packages=com.hszy -Dspring.profiles.active=prd -jar /home/<USER>/opt/newbackend/bin/hszy-transport.jar >/dev/null 2>&1 &

nohup java -server -Xms256m -Xmx512m -XX:MetaspaceSize=128m -XX:-UseAdaptiveSizePolicy -XX:+DisableExplicitGC -XX:+PrintGCDetails -Dfile.encoding=UTF-8 -javaagent:/home/<USER>/opt/soft/elastic-apm-agent-1.52.1.jar -Delastic.apm.service_name=hszy-usercenter -Delastic.apm.secret_token=1234567890q1 -Delastic.apm.server_url=http://172.29.234.194:8200 -Delastic.apm.environment=prd -Delastic.apm.application_packages=com.hszy -Dspring.profiles.active=prd -jar /home/<USER>/opt/newbackend/bin/hszy-usercenter.jar >/dev/null 2>&1 &

echo "'hszy' 相关进程已重新启动。"
