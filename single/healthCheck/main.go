package main

import (
	"bytes"
	"embed"
	"flag"
	"fmt"
	"net/http"
	"os/exec"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

// 服务状态
var STATE string

// 服务状态值
const (
	Runing      = "running"
	TodoConfirm = "todoConfirm"
	Restart     = "restart"
	Stop        = "stop"
)

//go:embed script/*
var scripts embed.FS

var interval int64
var fall int
var wait int64
var cpuMax int

func main() {
	flag.Int64Var(&interval, "interval", 30, "健康检查间隔,单位: 秒")
	flag.IntVar(&fall, "fall", 5, "健康检查失败次数,单位: 次")
	flag.Int64Var(&wait, "wait", 300, "健康检查失败等待时间,单位: 秒")
	flag.IntVar(&cpuMax, "cpuMax", 90, "cpu最大使用率")
	flag.Parse()

	STATE = Runing

	r := gin.Default()

	r.GET("/health_check", func(ctx *gin.Context) {

		// 待确认状态，健康检查返回成功
		if STATE == TodoConfirm {
			fmt.Printf("当前服务状态:%s\n", STATE)
			ctx.JSON(http.StatusOK, gin.H{
				"state": "ok",
			})
			return
		}

		// 服务重启中，健康检查返回失败
		if STATE == Restart || STATE == Stop {
			fmt.Printf("当前服务状态:%s\n", STATE)
			ctx.JSON(http.StatusBadGateway, gin.H{
				"state": "fail",
			})
			return
		}

		// 健康检查
		if !healthCheck() {
			ctx.JSON(http.StatusBadGateway, gin.H{
				"state": "fail",
			})
			return
		}

		ctx.JSON(http.StatusOK, gin.H{
			"state": "ok",
		})
	})

	if err := r.Run(":10001"); err != nil {
		panic(err)
	}
}

// healthCheck 健康检查
func healthCheck() bool {
	isNormal := runHealthCheck()

	// cpu负载异常
	if !isNormal {
		STATE = TodoConfirm

		// 每隔1分钟检查一次，3次均为异常则判定为服务异常，重启服务
		currentState := []bool{}
		for i := 0; i < fall; i++ {
			time.Sleep(time.Second * time.Duration(interval))
			currentState = append(currentState, runHealthCheck())
		}

		isRestart := false
		for _, v := range currentState {
			if !v {
				isRestart = true
			}
		}
		// 重启服务
		if isRestart {
			go runRestartServices()
		}

		return false
	}

	return true
}

// runHealthCheck 执行健康检查脚本
func runHealthCheck() bool {
	chkCpu, err := scripts.ReadFile("script/chk_cpu.sh")
	if err != nil {
		fmt.Printf("读取脚本文件失败,ERROR: %v\n", err)
		return true
	}

	// 创建命令对象
	cmd := exec.Command("bash", "-c", string(chkCpu))

	// 捕获标准输出和错误输出
	var stdout, stderr bytes.Buffer
	cmd.Stdout = &stdout
	cmd.Stderr = &stderr

	// 执行命令
	if err := cmd.Run(); err != nil {
		fmt.Printf("执行CPU健康检查失败,ERROR: %v\nStderr: %s\nStdout: %s\n", err, stderr.String(), stdout.String())
		return true
	}

	// cpu负载异常
	cpuUsage, _ := strconv.Atoi(strings.TrimSuffix(stdout.String(), "\n"))
	if cpuUsage > cpuMax {
		fmt.Printf("CPU使用率异常，当前占用率: %d\n", cpuUsage)
		return false
	}

	fmt.Printf("当前服务状态: %s\n", STATE)
	return true
}

// runRestartServices 执行重启服务脚本
func runRestartServices() {
	STATE = Restart

	restartServices, err := scripts.ReadFile("script/restart_services.sh")
	if err != nil {
		fmt.Printf("读取脚本文件失败,ERROR: %v\n", err)
		STATE = Stop
		return
	}

	// 创建命令对象
	cmd := exec.Command("bash", "-c", string(restartServices))

	// 捕获标准输出和错误输出
	var stdout, stderr bytes.Buffer
	cmd.Stdout = &stdout
	cmd.Stderr = &stderr

	// 执行命令
	if err := cmd.Run(); err != nil {
		fmt.Printf("重新启动服务失败,ERROR: %v\nStderr: %s\nStdout: %s\n", err, stderr.String(), stdout.String())
		STATE = Stop
		return
	}

	// 等待3分钟,再次健康检查
	time.Sleep(time.Duration(wait) * time.Second)
	if runHealthCheck() {
		STATE = Runing
	} else {
		STATE = Stop
	}
}
