package main

import (
	"crypto"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"
	"strings"
)

type Signature struct {
	Algorithm      string
	PrivateKey     *rsa.PrivateKey
	PublicKey      *rsa.PublicKey
	PrivateKeyData string
	PublicKeyData  string
	TaiPingKeyData string
}

func NewSignature(privateKeyString, publicKeyString string) (*Signature, error) {
	// Decode the base64-encoded key
	privateKeyBytes, err := base64.StdEncoding.DecodeString(privateKeyString)
	if err != nil {
		return nil, err
	}

	// Parse the RSA private key
	privateKey, err := x509.ParsePKCS8PrivateKey(privateKeyBytes)
	if err != nil {
		return nil, err
	}

	// Decode the base64-encoded key
	publicKeyBytes, err := base64.StdEncoding.DecodeString(publicKeyString)
	if err != nil {
		return nil, err
	}

	// Parse the RSA public key
	publicKey, err := x509.ParsePKIXPublicKey(publicKeyBytes)
	if err != nil {
		return nil, err
	}

	return &Signature{
		PrivateKey: privateKey.(*rsa.PrivateKey),
		PublicKey:  publicKey.(*rsa.PublicKey),
		Algorithm:  "SHA256withRSA",
	}, nil
}

// 签名数据
func (s *Signature) Sign(data string) (string, error) {

	hash := sha256.Sum256([]byte(strings.ReplaceAll(base64.StdEncoding.EncodeToString([]byte(data)), "\n", "")))
	sign, err := rsa.SignPKCS1v15(rand.Reader, s.PrivateKey, crypto.SHA256, hash[:])
	if err != nil {
		return "", err
	}
	return base64.StdEncoding.EncodeToString(sign), nil
}

// 验证签名
func (s *Signature) Verify(data []byte, base64Signature string) (bool, error) {
	signature, err := base64.StdEncoding.DecodeString(base64Signature)
	if err != nil {
		return false, err
	}

	hash := sha256.Sum256([]byte(strings.ReplaceAll(base64.StdEncoding.EncodeToString(data), "\n", "")))
	if err := rsa.VerifyPKCS1v15(s.PublicKey, crypto.SHA256, hash[:], signature); err != nil {
		return false, err
	}
	return true, nil
}

// 验证签名(响应数据)
func (s *Signature) VerifyResponse(taiPingPublicKey, base64Signature string, data []byte) (bool, error) {
	// 公钥base64解码
	publicKeyBytes, err := base64.StdEncoding.DecodeString(taiPingPublicKey)
	if err != nil {
		return false, err
	}

	// 解析为公钥
	publicKey, err := x509.ParsePKIXPublicKey(publicKeyBytes)
	if err != nil {
		return false, err
	}

	// base64解码签名
	signature, err := base64.StdEncoding.DecodeString(base64Signature)
	if err != nil {
		return false, err
	}

	hash := sha256.Sum256([]byte(strings.ReplaceAll(base64.StdEncoding.EncodeToString(data), "\n", "")))
	if err := rsa.VerifyPKCS1v15(publicKey.(*rsa.PublicKey), crypto.SHA256, hash[:], signature); err != nil {
		return false, err
	}
	return true, nil
}
