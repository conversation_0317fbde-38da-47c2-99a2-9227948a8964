package main

import (
	"bytes"
	"encoding/xml"
	"fmt"
	"io"
	"net/http"
	"os"
	"strings"
	"time"
	"wlhy/single/taiping/rep"
	"wlhy/single/taiping/req"
	"wlhy/toolbox/logger"
)

type TaiPing struct {
	InsuranceUrl string
	InquireUrl   string
	DownloadUrl  string
	PrivateKey   string
	PublicKey    string
	TaiPingKey   string
	Signature    *Signature
}

func NewTaiPing() *TaiPing {
	taiPing := &TaiPing{
		/* InsuranceUrl: "https://noncloud-test1.etaiping.com/react/platform/portal/proposal",
		DownloadUrl:  "http://car.etaiping.com:6018/react/platform/portal/documentDownload",
		PrivateKey:   "MIICdQIBADANBgkqhkiG9w0BAQEFAASCAl8wggJbAgEAAoGBALbwJozXJy4+3dsdliQ2wYw2g1QVtzgCRBnCRXhfqKNiErgSJZdV3vYTxH66U/xuD3bDFJT1NmBxgzRCxLpy/C99W5xXR3P9PvFdvP4B3AVCGOAT5UnLQoxpDvXorRPXAH5tJJ8NV1dJSfJ1dyJub7zAR1LD4Mkz1KNZsqC5QnHfAgMBAAECgYBcIcpoeBslcUfuiJILHwCPCw1NFbq2nH2Ls4mlPBgEYOoohgskkny91erKIDEYGp3zJACDnJOMMt7vPQkzFaHntDhe1/1ZabDXB7Vgh/1+SWvnRCm5kHlZOOC7c2sfPg6t3V/wJ3fY7NlbuVujH67rn1rigwNuLKhtp1uwi7T38QJBAP1wM0tnQEmfUmOWFIwLnkhqLNlbs8Yj8flfiPFwFr8taSXRWh9ERZjDGUXfiPA/rlfaDznnUVu7gnYmzFnChUUCQQC4yYXtdGFgnXWOEw3nUU68600sKlOwDvAr0bSRzHUCfuX3MKo275bffvK6qknHwypEE9YVscq9yNmIpied7NLTAkA/d4Wg8G5tAXUEc2UZ3R76S3yHwis3B3FlXcNRkxxfK+QVEGRdSB+VreX6VYxZfdkPLxB5E7CQJFj7daPfJpNxAkBgiAYKsc1c52TNv2539KWpU1KRS9tEEcmtWnN15tUJZLlFlIqXADpUrsCmeuboEDA4OBSSaLp8wjvBgusXOOw5AkBPZxTjHz4W70sE5UVVMfEx3TF5tI1cWBawlUDuCSxm6lO+vsYwQb2WmRWSiBgxaV3U0j6ZkBb/Ent/72GzKbCO",
		PublicKey:    "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC28CaM1ycuPt3bHZYkNsGMNoNUFbc4AkQZwkV4X6ijYhK4EiWXVd72E8R+ulP8bg92wxSU9TZgcYM0QsS6cvwvfVucV0dz/T7xXbz+AdwFQhjgE+VJy0KMaQ716K0T1wB+bSSfDVdXSUnydXcibm+8wEdSw+DJM9SjWbKguUJx3wIDAQAB",
		TaiPingKey:   "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCIKZ5+kEhYm3LmRZJRBGkiF3nqHl0ZL+XL5J3+QwPr2+FLp/OKKOof7AHunjyDqcP/CZUxkwD2xraC2d1YvOaEuqypcyzaOAZ9ZhXp5VB/2lZYQZlxNK8Rr/7eGhk/CCxiqmZf2sPgtpBGbFrqI/8GdcIcbHoHKacNvYt3HzlCRQIDAQAB", */
		InsuranceUrl: "https://nonautocloud.tpi.cntaiping.com/react/platform/portal/proposal",
		InquireUrl:   "https://nonautocloud.tpi.cntaiping.com/react/platform/portal/documentDownload",
		PrivateKey:   "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAJ/z+1wZLNmDyrhBAgkCssxEAwykqwUaA8WMeEei18C0MjQHCKLDZkBJWY7LWsCU4GLNcrft5ihN1JFvP7Dc0kCozaj2tt863MkjhqH8y2255/ssWXTeRNBsXJ/EGjFIo8ITkAbEun95xnWCnZF9b8IpLpgBH3PrQ8cX8OGZhgjjAgMBAAECgYAXn4H9DR7JlSRIaBlz2Tks39KPeHZlj30FHQTal5tDs5lCg9Nrrm4Yiny2kSP7E0G8ddk+ft0iELSXMhX2+fX3KO8LEZeYIQujDqDB0MBfw/n0UHI9bszBN8tAZhmCVLu6AyZ0I5Mx4jj3JhRsyURTiDDDCekzvZ4RDjwKNB8pAQJBAM38JEqqiX9m79eid2eZ2p8Dw47llBdy5fZbPu/9hQGa5r2PdY25M4n7gLKcajS+cXg0Se0JHMU/q9FrYNXwm4ECQQDGyokgCTil25GqXz+R4exIJzR0/1jNHCGefIUN7pZfDHytnY+ZyIZgwrHDWMGRtyOpU4Lup9G3zt+SdD8jxOZjAkBIAPS6uQRZaaU7bopox6pZXZp1kCEFVV+3j0vf3nIpDnnMF6LTCMNQX38scGrbxxqpFzYkSsQo20n3aZ6vCm2BAkBlnINJD82X4crsVjFtRRj8rKWApEDk3ttEYou0Wl8ciIp2SBJt3fTqIrqwin6vF/ivhZNon/lvxdu7xZW8l9nXAkEAqoaN2qFB8vmg5nKifmik6Nt3XjKvf5KpDgrxtLJUpBrlVErvV3bb/euCKTXAFe/SUBYVxLlLkWXvD16WqeligA==",
		PublicKey:    "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCf8/tcGSzZg8q4QQIJArLMRAMMpKsFGgPFjHhHotfAtDI0Bwiiw2ZASVmOy1rAlOBizXK37eYoTdSRbz+w3NJAqM2o9rbfOtzJI4ah/Mttuef7LFl03kTQbFyfxBoxSKPCE5AGxLp/ecZ1gp2RfW/CKS6YAR9z60PHF/DhmYYI4wIDAQAB",
		TaiPingKey:   "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCEB/Oq3rhvkrax3SUJXqp+hzPryqdfBrcYfiVIISwY3vuuK3dar2soLzQwzQJlYts9LCXX8m5s3nHv/TJOYd7F/l+V8lnZWSi7/J8PWSjsi8OWJSFfF2V/iuRtxxOXBXm2zao8gh2X+dSHVb9NFres4DTLF1zPkvkgJjpdcgdgDwIDAQAB",
	}

	signature, err := NewSignature(taiPing.PrivateKey, taiPing.PublicKey)
	if err != nil {
		logger.Error(err)
		return nil
	}

	taiPing.Signature = signature
	return taiPing
}

type InsuranceReqContent struct {
	OrderNo                 string // 订单号
	TransTime               string // 交易日期
	OperateDate             string // 出单日期,datetime
	StartDate               string // 起保日期,datetime
	EndDate                 string // 终保日期,datetime
	SumInsured              string // 总保额
	SumPremium              string // 总保费
	GoodsDesc               string // 货物描述
	TransportationAmount    string // 运输费用
	TransportationStartDate string // 起运日期
	LoadingAddress          string // 起运地
	UnloadAddress           string // 目的地
	LicenseNumber           string // 车牌号
	CompanyName             string // 公司名称
	ShipperName             string // 货主名称
	ShipperPhone            string // 货主电话
	SocialCreditCode        string // 社会统一信用代码
	MailingAddress          string // 通讯地址
}

type InsuranceReqResult struct {
	TranportationNumber string
	ProposalNo          string
	PolicyNo            string
	PolicyUrl           string
}

func (t *TaiPing) InsuranceReq(content InsuranceReqContent) (*InsuranceReqResult, error) {
	body := req.Insurance{
		Signature: "",
		Request: req.ISRequest{
			Head: req.ISHead{
				Function:     "person",          // 固定值
				OrderNo:      content.OrderNo,   // 订单号
				AgencyCode:   "CFZHWL",          // 固定值
				ProductCode:  "0202201",         // 固定值
				BusinessType: "proposal",        // 固定值
				TransTime:    content.TransTime, // 交易日期,datetime
			},
			Body: req.ISBody{
				Project: req.ISProject{
					ProjectCode:      "0001",              // 固定值
					ProjectName:      "方案1",               // 固定值
					GeographicalArea: "00001",             // 固定值
					OperateDate:      content.OperateDate, // 出单日期,datetime
					StartDate:        content.StartDate,   // 起保日期,datetime
					EndDate:          content.EndDate,     // 终保日期,datetime
					Currency:         "CNY",               // 币值，固定值
					UwCount:          1,                   // 份数，固定值
					SumInsured:       content.SumInsured,  // 总保额，每份方案单位保额*投保份数*投保人数
					SumPremium:       content.SumPremium,  // 总保费，每份方案单位保费*投保份数*投保人数
				},
				CorporateApp: req.ISCorporateApp{
					AppliType:      "2",                               // 固定值
					AppliName:      "赤峰现代智慧物流有限公司",                    // 固定值
					IdentifyType:   "4",                               // 固定值
					IdentifyNumber: "91150402MACRQHHW1K",              // 固定值
					ContactName:    "赤峰现代智慧物流有限公司",                    // 固定值
					ContactPhone:   "18017895856",                     // 固定值
					AppliAddress:   "内蒙古自治区赤峰市红山区桥北街道火花北路155号一楼东侧办事厅", // 固定值
				},
				CorporateInsured: []req.ISCorporateInsured{
					{
						InsuredType:    "2",
						InsuredName:    content.CompanyName,
						IdentifyType:   "4",
						IdentifyNumber: content.SocialCreditCode,
						ContactName:    content.ShipperName,
						ContactPhone:   content.ShipperPhone,
						InsuredAddress: content.MailingAddress,
					},
				},
				ItemDynamics: req.ISItemDynamics{
					RiskCode: "1251",                          // 固定值
					FieldBG:  content.OrderNo,                 // 订单号
					FieldAV:  content.GoodsDesc,               // 货物描述
					FieldAX:  content.TransportationAmount,    // 运费金额
					FieldBE:  content.TransportationStartDate, // 起运日期,date
					FieldBB:  content.LoadingAddress,          // 起运地
					FieldBC:  content.UnloadAddress,           // 目的地
					FieldBF:  content.LicenseNumber,           // 车牌号
					FieldAU:  "02",                            // 运输货物类型，非快递包裹默认值：02
					FieldAS:  "1",                             // 运输工具数量
				},
			},
		},
	}

	bodyRequest := body.Request
	bodyRequestXML, err := xml.Marshal(bodyRequest)
	if err != nil {
		return nil, err
	}
	logger.Info(bodyRequestXML)

	bodyRequestXML = bytes.ReplaceAll(bodyRequestXML, []byte("<request>"), []byte(""))
	bodyRequestXML = bytes.ReplaceAll(bodyRequestXML, []byte("</request>"), []byte(""))
	sign, err := t.Signature.Sign(string(bodyRequestXML))
	if err != nil {
		return nil, err
	}

	_, err = t.Signature.Verify(bodyRequestXML, sign)
	if err != nil {
		return nil, err
	}

	body.Signature = sign

	bodyXML, _ := xml.Marshal(body)

	req, err := http.Post(t.InsuranceUrl, "application/xml", strings.NewReader(xml.Header+string(bodyXML)))
	if err != nil {
		return nil, err
	}
	defer req.Body.Close()

	if req.StatusCode != 200 {
		resp, _ := io.ReadAll(req.Body)
		logger.Info(string(resp))
		return nil, fmt.Errorf("请求失败，状态码:%d", req.StatusCode)
	}

	resp, _ := io.ReadAll(req.Body)

	result := rep.Insurance{}
	if err := xml.Unmarshal(resp, &result); err != nil {
		return nil, err
	}

	// 验签
	responseXML := bytes.ReplaceAll(append([]byte("<response>"), bytes.SplitAfter(resp, []byte("<response>"))[1]...), []byte("</document>"), []byte(""))
	_, err = t.Signature.VerifyResponse(t.TaiPingKey, result.Signature, responseXML)
	if err != nil {
		return nil, err
	}

	if result.Response.Body.SuccessInd == 0 {
		logger.Error(fmt.Sprintf("%s 投保失败，错误信息:%s\n", content.OrderNo, result.Response.Body.ErrorMessage))
		return nil, err
	}

	logger.Info(fmt.Sprintf("%s 投保成功\n投保单号：%s\n保单号：%s\n保单下载地址：%s\n", content.OrderNo, result.Response.Body.ProposalNo, result.Response.Body.PolicyNo, result.Response.Body.PolicyUrl))
	return &InsuranceReqResult{
		TranportationNumber: content.OrderNo,
		ProposalNo:          result.Response.Body.ProposalNo,
		PolicyNo:            result.Response.Body.PolicyNo,
		PolicyUrl:           result.Response.Body.PolicyUrl,
	}, nil
}

func (t *TaiPing) InquireReq(filename, orderNo, documentUrl string) error {
	body := req.Inquire{
		Signature: "",
		Request: req.IQRequest{
			Head: req.IQHead{
				OrderNo:      orderNo,
				AgencyCode:   "CFZHWL",
				ProductCode:  "0202201",
				BusinessType: "documentDownload",
				TransTime:    time.Now().Format("2006-01-02 15:04:05"),
			},
			Body: req.IQBody{
				DocumentNo:   "",
				DocumentType: "",
				DocumentUrl:  documentUrl,
			},
		},
	}

	bodyRequest := body.Request
	bodyRequestXML, err := xml.Marshal(bodyRequest)
	if err != nil {
		return err
	}
	logger.Info(bodyRequest)

	bodyRequestXML = bytes.ReplaceAll(bodyRequestXML, []byte("<request>"), []byte(""))
	bodyRequestXML = bytes.ReplaceAll(bodyRequestXML, []byte("</request>"), []byte(""))
	sign, err := t.Signature.Sign(string(bodyRequestXML))
	if err != nil {
		return err
	}

	_, err = t.Signature.Verify(bodyRequestXML, sign)
	if err != nil {
		return err
	}

	body.Signature = sign

	bodyXML, _ := xml.Marshal(body)

	req, err := http.Post(t.InquireUrl, "application/xml", strings.NewReader(xml.Header+string(bodyXML)))
	if err != nil {
		return err
	}
	defer req.Body.Close()

	if req.StatusCode != 200 {
		resp, _ := io.ReadAll(req.Body)
		logger.Error(string(resp))
		return fmt.Errorf("请求失败，状态码:%d", req.StatusCode)
	}

	if _, err := os.Stat(filename); err == nil {
		os.Remove(filename)
	}
	file, err := os.Create(filename)
	if err != nil {
		return err
	}
	defer file.Close()

	if _, err := io.Copy(file, req.Body); err != nil {
		return err
	}

	return nil
}
