package main

import (
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"time"
	"wlhy/model"
	"wlhy/toolbox"
	"wlhy/toolbox/logger"

	"github.com/aliyun/aliyun-oss-go-sdk/oss"
	"github.com/robfig/cron/v3"
	"github.com/signintech/gopdf"
	"rsc.io/pdf"
)

var ossBucket *oss.Bucket
var isInsuranceRun bool
var isInquireRun bool

func init() {
	// oss
	client, err := oss.New("oss-cn-hangzhou.aliyuncs.com", "LTAI5t7t2tg58yCVGg41WTYZ", "******************************")
	if err != nil {
		logger.Stdout.Error(err.Error())
		os.Exit(-1)
	}

	// 填写存储空间名称，例如examplebucket。
	ossBucket, err = client.Bucket("cfhswlhyhz")
	if err != nil {
		logger.Stdout.Error(err.Error())
		os.Exit(-1)
	}
}

func main() {
	c := cron.New(cron.WithLogger(cron.DefaultLogger), cron.WithSeconds())

	// 投保
	c.AddFunc("0 * * * * *", Insurance)

	// 下载保单
	c.AddFunc("0 */3 * * * *", Inquire)

	c.Start()
	select {}
}

func Insurance() {
	if isInsuranceRun {
		return
	}
	isInsuranceRun = true

	t := NewTaiPing()

	rows, err := model.DB.Table("tms_transport_note AS a").
		Joins("JOIN tms_order_history AS b ON a.order_id = b.id AND a.order_version_no = b.version_no").
		Joins("JOIN tms_shipper AS c ON a.create_company_id = c.company_id").
		Joins("JOIN tms_transport_note_insurance_record AS d ON a.transportation_number = d.transport_note_number").
		Select([]string{"a.id", "a.transportation_number", "b.goods_type", "b.goods_name", "a.freight_amount",
			"b.loading_name", "b.loading_address", "b.unload_name", "b.unload_address", "a.transportation_plate",
			"a.loading_time", "c.company_name", "c.shipper_name", "c.shipper_phone", "c.social_credit_code",
			"c.mailing_address", "a.payee_agent_user_id"}).
		Where("d.insurance_company = ?", 1).
		Where("d.insurance_status = ?", 0).
		Where("a.loading_number > ?", 0).
		Where("c.shipper_type = ? AND c.is_delete = ?", 1, 0).
		Rows()

	if err != nil {
		isInsuranceRun = false
		logger.Stdout.Error(err.Error())
		return
	}

	var serviceFee float64
	for rows.Next() {
		var id string
		var transportationNumber string
		var goodsType string
		var goodsName string
		var freightAmount float64
		var loadingName string
		var loadingAddress string
		var unloadName string
		var unloadAddress string
		var transportationPlate string
		var loadingTime time.Time
		var companyName string
		var shipperName string
		var shipperPhone string
		var socialCreditCode string
		var mailingAddress string
		var payeeAgentUserID string

		if err := rows.Scan(&id, &transportationNumber, &goodsType, &goodsName, &freightAmount, &loadingName, &loadingAddress, &unloadName, &unloadAddress, &transportationPlate, &loadingTime, &companyName, &shipperName, &shipperPhone, &socialCreditCode, &mailingAddress, &payeeAgentUserID); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}

		dt := time.Now().Format("2006-01-02 15:04:05")
		dt1 := time.Now().Add(1 * time.Second)

		if strings.Contains(loadingAddress, loadingName) {
			loadingAddress = loadingName + strings.ReplaceAll(loadingAddress, loadingName, "")
		}
		if strings.Contains(unloadAddress, unloadName) {
			unloadAddress = unloadName + strings.ReplaceAll(unloadAddress, unloadName, "")
		}

		SumPremium := (freightAmount * 100) * (0.0018 * 10000) * (1 + serviceFee) / (100 * 10000)
		content := InsuranceReqContent{
			OrderNo:                 transportationNumber,
			TransTime:               dt,
			OperateDate:             dt,
			StartDate:               dt1.Format("2006-01-02 15:04:05"),
			EndDate:                 dt1.Add(7*24*time.Hour - 1*time.Second).Format("2006-01-02 15:04:05"),
			SumInsured:              fmt.Sprintf("%0.2f", toolbox.RoundToDecimal(freightAmount, 2)),
			SumPremium:              fmt.Sprintf("%0.2f", toolbox.RoundToDecimal(SumPremium, 2)),
			GoodsDesc:               goodsType + " " + goodsName,
			TransportationAmount:    fmt.Sprintf("%0.2f", toolbox.RoundToDecimal(freightAmount, 2)),
			TransportationStartDate: dt1.Format("2006-01-02 15:04:05"),
			LoadingAddress:          loadingAddress,
			UnloadAddress:           unloadAddress,
			LicenseNumber:           transportationPlate,
			CompanyName:             companyName,
			ShipperName:             shipperName,
			ShipperPhone:            shipperPhone,
			SocialCreditCode:        socialCreditCode,
			MailingAddress:          mailingAddress,
		}

		result, err := t.InsuranceReq(content)
		if err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}
		if result == nil {
			continue
		}

		if err := model.DB.Table("tms_transport_note_insurance_record").
			Where("transport_note_number = ?", result.TranportationNumber).
			Updates(map[string]any{
				"insurance_status":            1,
				"policy_number":               result.PolicyNo,
				"insurance_pdf":               result.PolicyUrl,
				"insurance_time":              time.Now().Format("2006-01-02 15:04:05"),
				"driver_id":                   payeeAgentUserID,
				"thirdparty_insurance_amount": toolbox.RoundToDecimal(SumPremium, 2),
			}).Error; err != nil {
			logger.Stdout.Error(err.Error())
		}
	}

	isInsuranceRun = false
}

func Inquire() {
	if isInquireRun {
		return
	}
	isInquireRun = true

	t := NewTaiPing()

	rows, err := model.DB.Table("tms_transport_note_insurance_record").
		Select([]string{"id", "transport_note_number", "insurance_pdf", "insurance_time", "policy_number"}).
		Where("insurance_company = ?", 1).
		Where("insurance_pdf NOT LIKE ?", "https://cfhswlhyhz.oss-cn-hangzhou.aliyuncs.com/insurance/%").
		Rows()
	if err != nil {
		isInquireRun = false
		logger.Stdout.Error(err.Error())
		return
	}

	for rows.Next() {
		var id string
		var transportationNumber string
		var insurancePdf string
		var insuranceTime time.Time
		var policyNumber string
		if err := rows.Scan(&id, &transportationNumber, &insurancePdf, &insuranceTime, &policyNumber); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}

		if time.Since(insuranceTime) > 5*time.Minute {
			UploadToOSS(t, id, transportationNumber, insurancePdf, policyNumber)
		}
	}

	isInquireRun = false
}

func UploadToOSS(taiPing *TaiPing, id, transportationNumber, insurancePdf, policyNumber string) {
	filename := transportationNumber + ".pdf"
	if err := taiPing.InquireReq(filename, policyNumber, insurancePdf); err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	_, err := pdf.Open(filename)
	if err != nil {
		// 删除临时文件
		os.Remove(filename)
		logger.Stdout.Error(err.Error())
		return
	}

	err = ossBucket.PutObjectFromFile("insurance/"+filepath.Base(filename), filename)
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	// 删除临时文件
	os.Remove(filename)

	ossUrl := "https://cfhswlhyhz.oss-cn-hangzhou.aliyuncs.com/insurance/" + filepath.Base(filename)

	if err := model.DB.Table("tms_transport_note_insurance_record").
		Where("id = ?", id).
		Updates(map[string]any{
			"insurance_pdf": ossUrl,
		}).Error; err != nil {
		logger.Stdout.Error(err.Error())
	}
	logger.Stdout.Info(fmt.Sprintf("%s 下载保单并上传成功:%s\n", transportationNumber, ossUrl))
}

func EditPolicyPDF() {
	var err error

	rows, err := model.DB.Debug().Table("tms_transport_note_insurance_record AS a").
		Joins("JOIN tms_transport_note AS b ON a.transport_note_number = b.transportation_number").
		Joins("JOIN tms_order_history AS c ON b.order_id = c.id AND b.order_version_no = c.version_no").
		Select([]string{"a.id", "a.transport_note_number", "a.insurance_pdf", "c.loading_name", "c.loading_address",
			"c.unload_name", "b.unload_address"}).
		// Where("a.shipper_name = ?", "林西宏广商贸有限公司").
		Rows()
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	dir := "pdf"
	os.Mkdir(dir, 0777)
	for rows.Next() {
		var id string
		var transportationNumber string
		var insurancePdf string
		var loadingName string
		var loadingAddress string
		var unloadName string
		var unloadAddress string
		if err := rows.Scan(&id, &transportationNumber, &insurancePdf, &loadingName, &loadingAddress, &unloadName, &unloadAddress); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}

		if strings.Contains(loadingAddress, loadingName) {
			loadingAddress = loadingName + strings.ReplaceAll(loadingAddress, loadingName, "")
		}
		if strings.Contains(unloadAddress, unloadName) {
			unloadAddress = unloadName + strings.ReplaceAll(unloadAddress, unloadName, "")
		}

		// Download a PDF
		if err = downloadFile("example-pdf.pdf", insurancePdf); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}

		pdf := gopdf.GoPdf{}
		pdf.Start(gopdf.Config{PageSize: *gopdf.PageSizeA4})

		pdf.AddPage()

		tpl1 := pdf.ImportPage("example-pdf.pdf", 1, "/MediaBox")
		pdf.UseImportedTemplate(tpl1, 0, 0, 0, 0)
		pdf.SetLineWidth(0)
		pdf.SetFillColor(255, 255, 255) //setup fill color

		addressLength := 0
		addressLength = max(len(loadingAddress), len(unloadAddress))

		if addressLength <= 57 {
			pdf.RectFromUpperLeftWithStyle(113.5, 446, 439.3, 17, "FD")
		} else if addressLength <= 114 {
			pdf.RectFromUpperLeftWithStyle(113.5, 452.5, 439.3, 17, "FD")
		} else if addressLength <= 171 {
			pdf.RectFromUpperLeftWithStyle(113.5, 459, 439.3, 17, "FD")
		} else {
			pdf.RectFromUpperLeftWithStyle(113.5, 465.5, 439.3, 17, "FD")
		}

		pdf.SetFillColor(255, 255, 255)

		pdf.AddPage()
		tpl2 := pdf.ImportPage("example-pdf.pdf", 2, "/MediaBox")
		pdf.UseImportedTemplate(tpl2, 0, 0, 0, 0)
		pdf.AddPage()
		tpl3 := pdf.ImportPage("example-pdf.pdf", 3, "/MediaBox")
		pdf.UseImportedTemplate(tpl3, 0, 0, 0, 0)
		pdf.AddPage()
		tpl4 := pdf.ImportPage("example-pdf.pdf", 4, "/MediaBox")
		pdf.UseImportedTemplate(tpl4, 0, 0, 0, 0)
		pdf.AddPage()
		tpl5 := pdf.ImportPage("example-pdf.pdf", 5, "/MediaBox")
		pdf.UseImportedTemplate(tpl5, 0, 0, 0, 0)
		pdf.AddPage()
		tpl6 := pdf.ImportPage("example-pdf.pdf", 6, "/MediaBox")
		pdf.UseImportedTemplate(tpl6, 0, 0, 0, 0)
		pdf.AddPage()
		tpl7 := pdf.ImportPage("example-pdf.pdf", 7, "/MediaBox")
		pdf.UseImportedTemplate(tpl7, 0, 0, 0, 0)
		pdf.AddPage()
		tpl8 := pdf.ImportPage("example-pdf.pdf", 8, "/MediaBox")
		pdf.UseImportedTemplate(tpl8, 0, 0, 0, 0)

		pdf.WritePdf(dir + "/" + transportationNumber + ".pdf")
	}

	logger.Stdout.Info("EditPolicyPDF done")
}

func downloadFile(filepath string, url string) error {
	// Get the data
	resp, err := http.Get(url)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	// Create the file
	out, err := os.Create(filepath)
	if err != nil {
		return err
	}
	defer out.Close()

	// Write the body to file
	_, err = io.Copy(out, resp.Body)
	return err
}
