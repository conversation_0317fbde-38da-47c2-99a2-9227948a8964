package req

import "encoding/xml"

// 定义 XML 结构体
type Insurance struct {
	XMLName   xml.Name  `xml:"document"`
	Signature string    `xml:"signature"`
	Request   ISRequest `xml:"request"`
}

type ISRequest struct {
	XMLName xml.Name `xml:"request"`
	Head    ISHead   `xml:"head"`
	Body    ISBody   `xml:"body"`
}

type ISHead struct {
	XMLName      xml.Name `xml:"head"`
	Function     string   `xml:"function"`
	OrderNo      string   `xml:"orderNo"`
	AgencyCode   string   `xml:"agencyCode"`
	ProductCode  string   `xml:"productCode"`
	BusinessType string   `xml:"businessType"`
	TransTime    string   `xml:"transTime"`
}

type ISBody struct {
	XMLName          xml.Name           `xml:"body"`
	Project          ISProject          `xml:"project"`
	CorporateApp     ISCorporateApp     `xml:"corporateApp"`
	CorporateInsured []ISCorporateInsured `xml:"corporateInsured"`
	ItemDynamics     ISItemDynamics     `xml:"itemDynamics"`
}

type ISProject struct {
	XMLName          xml.Name `xml:"project"`
	ProjectCode      string   `xml:"projectCode"`
	ProjectName      string   `xml:"projectName"`
	GeographicalArea string   `xml:"geographicalArea"`
	OperateDate      string   `xml:"operateDate"`
	StartDate        string   `xml:"startDate"`
	EndDate          string   `xml:"endDate"`
	Currency         string   `xml:"currency"`
	UwCount          int      `xml:"uwCount"`
	SumInsured       string   `xml:"sumInsured"`
	SumPremium       string   `xml:"sumPremium"`
}

type ISCorporateApp struct {
	XMLName        xml.Name `xml:"corporateApp"`
	AppliType      string   `xml:"appliType"`
	AppliName      string   `xml:"appliName"`
	IdentifyType   string   `xml:"identifyType"`
	IdentifyNumber string   `xml:"identifyNumber"`
	ContactName    string   `xml:"contactName"`
	ContactPhone   string   `xml:"contactPhone"`
	AppliAddress   string   `xml:"appliAddress"`
}

type ISCorporateInsured struct {
	XMLName        xml.Name `xml:"corporateInsured"`
	InsuredType    string   `xml:"insuredType"`
	InsuredName    string   `xml:"insuredName"`
	IdentifyType   string   `xml:"identifyType"`
	IdentifyNumber string   `xml:"identifyNumber"`
	ContactName    string   `xml:"contactName"`
	ContactPhone   string   `xml:"contactPhone"`
	InsuredAddress string   `xml:"insuredAddress"`
}

type ISItemDynamics struct {
	XMLName  xml.Name `xml:"itemDynamics"`
	RiskCode string   `xml:"riskCode"`
	FieldBG  string   `xml:"fieldBG"`
	FieldAV  string   `xml:"fieldAV"`
	FieldAX  string   `xml:"fieldAX"`
	FieldBE  string   `xml:"fieldBE"`
	FieldBB  string   `xml:"fieldBB"`
	FieldBC  string   `xml:"fieldBC"`
	FieldBF  string   `xml:"fieldBF"`
	FieldAU  string   `xml:"fieldAU"`
	FieldAS  string   `xml:"fieldAS"`
}
