package req

import "encoding/xml"

// 定义 XML 结构体
type Inquire struct {
	XMLName   xml.Name  `xml:"document"`
	Signature string    `xml:"signature"`
	Request   IQRequest `xml:"request"`
}

type IQRequest struct {
	XMLName xml.Name `xml:"request"`
	Head    IQHead   `xml:"head"`
	Body    IQBody   `xml:"body"`
}

type IQHead struct {
	XMLName      xml.Name `xml:"head"`
	OrderNo      string   `xml:"orderNo"`
	AgencyCode   string   `xml:"agencyCode"`
	ProductCode  string   `xml:"productCode"`
	BusinessType string   `xml:"businessType"`
	TransTime    string   `xml:"transTime"`
}

type IQBody struct {
	XMLName      xml.Name `xml:"body"`
	DocumentNo   string   `xml:"documentNo"`
	DocumentType string   `xml:"documentType"`
	DocumentUrl  string   `xml:"documentUrl"`
}
