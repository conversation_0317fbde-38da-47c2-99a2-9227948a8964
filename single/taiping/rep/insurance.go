package rep

import "encoding/xml"

// 定义 XML 结构体
type Insurance struct {
	XMLName   xml.Name   `xml:"document"`
	Signature string     `xml:"signature"`
	Response  ISResponse `xml:"response"`
}

type ISResponse struct {
	XMLName xml.Name `xml:"response"`
	Head    ISHead   `xml:"head"`
	Body    ISBody   `xml:"body"`
}

type ISHead struct {
	XMLName      xml.Name `xml:"head"`
	Function     string   `xml:"function"`
	OrderNo      string   `xml:"orderNo"`
	AgencyCode   string   `xml:"agencyCode"`
	ProductCode  string   `xml:"productCode"`
	BusinessType string   `xml:"businessType"`
	TransTime    string   `xml:"transTime"`
}

type ISBody struct {
	XMLName      xml.Name `xml:"body"`
	SuccessInd   int      `xml:"successInd"`
	ErrorCode    string   `xml:"errorCode"`
	ErrorMessage string   `xml:"errorMessage"`
	ProposalNo   string   `xml:"proposalNo"`
	PolicyNo     string   `xml:"policyNo"`
	PolicyUrl    string   `xml:"policyUrl"`
}
