package wlhyverify

import (
	"fmt"
	"strconv"
	"strings"
	"time"
	"wlhy/model"
	"wlhy/single/wlhycity/wlhyorg"
	"wlhy/toolbox/logger"
)

func (w *WlhyVerify) Driver(action string) map[string]wlhyorg.Driver {
	sql := ""
	driverT := &driver{}
	if action == "report" {
		sql = driverT.ReportSQL()
	}
	if sql == "" {
		return nil
	}

	results := []driver{}
	if err := model.DB.Raw(sql).Scan(&results).Error; err != nil {
		logger.Stdout.Error(err.Error())
		return nil
	}

	fmt.Println("总数: " + strconv.Itoa(len(results)))
	drivers := make(map[string]wlhyorg.Driver)
	for _, v := range results {
		validPeriodFrom := time.Now().Format("20060102")
		if !v.ValidPeriodFrom.IsZero() {
			validPeriodFrom = v.ValidPeriodFrom.Format("20060102")
		}

		validPeriodTo := time.Now().Format("20060102")
		if !v.ValidPeriodTo.IsZero() {
			validPeriodTo = v.ValidPeriodTo.Format("20060102")
		}

		drivers[v.DriverID] = wlhyorg.Driver{
			DriverName:               v.DriverName,
			DrivingLicense:           v.DrivingLicense,
			VehicleClass:             "",
			IssuingOrganizations:     strings.ReplaceAll(strings.ReplaceAll(v.IssuingOrganizations, "\n", ""), " ", ""),
			ValidPeriodFrom:          validPeriodFrom,
			ValidPeriodTo:            validPeriodTo,
			QualificationCertificate: v.QualificationCertificate,
			Telephone:                v.Telephone,
			Remark:                   "",
		}
	}
	return drivers
}

type driver struct {
	DriverID                 string
	ID                       string
	DriverName               string
	DrivingLicense           string
	IssuingOrganizations     string
	VehicleClassId           string
	VehicleClass             string
	QualificationCertificate string
	Telephone                string
	ValidPeriodFrom          time.Time
	ValidPeriodTo            time.Time
	GrossMass                string
}

func (d *driver) ReportSQL() string {
	return `
SELECT 
	a.driver_id,
	a.id,
	a.driver_name as driver_name, 
	a.identification_number as driving_license, 
	a.certification_authority as issuing_organizations,
    a.allow_driving_type_id as vehicle_class_id,
	a.allow_driving_type as vehicle_class, 
	a.identification_number as qualification_certificate,
    a.phone as telephone,
	a.date_of_certification as valid_period_from, 
	a.driver_license_exp_date as valid_period_to,
	b.vehicle_total_weight as gross_mass
FROM 
	tms_driver a
    LEFT JOIN tms_vehicle b ON a.driver_id = b.driver_user
	LEFT JOIN tms_network_upload_info c ON a.driver_id = c.association_id AND c.association_type = 1
WHERE a.is_delete = 0
	AND a.audit_status = 2
	AND a.attestation_status = 2
	AND c.is_upload_city = 0
ORDER BY a.create_time DESC
	`
}
