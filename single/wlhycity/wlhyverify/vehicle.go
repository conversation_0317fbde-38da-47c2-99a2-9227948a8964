package wlhyverify

import (
	"fmt"
	"strconv"
	"time"
	"wlhy/model"
	"wlhy/single/wlhycity/wlhyorg"
	"wlhy/toolbox/logger"
)

func (v *WlhyVerify) Vehicle(action string) map[string]wlhyorg.Vehicle {
	sql := ""
	vehicleT := &vehicle{}
	if action == "report" {
		sql = vehicleT.ReportSQL()
	}
	if sql == "" {
		return nil
	}

	results := []vehicle{}
	if err := model.DB.Raw(sql).Scan(&results).Error; err != nil {
		logger.Stdout.Error(err.Error())
		return nil
	}
	fmt.Println("总数: " + strconv.Itoa(len(results)))

	// 获取车型数据
	vehicleTypes := vehicleTypeCode()
	// 获取车辆能源类型数据
	vehicleEnergyTypes := vehicleEnergyCode()
	// 获取车牌颜色数据
	vehiclePlateColorTypes := vehiclePlateColorCode()

	// 校验并组装数据
	vehicles := make(map[string]wlhyorg.Vehicle)
	for _, r := range results {
		registerDate := ""
		issueDate := ""
		if r.RegisterDate.IsZero() {
			registerDate = ""
		} else {
			registerDate = r.RegisterDate.Format("20060102")
		}

		if r.IssueDate.IsZero() {
			issueDate = ""
		} else {
			issueDate = r.IssueDate.Format("20060102")
		}
		vehicles[r.ID] = wlhyorg.Vehicle{
			VehicleNumber:                  r.VehicleNumber,
			VehiclePlateColorCode:          vehiclePlateColorTypes[r.VehiclePlateColorCode],
			VehicleType:                    vehicleTypes[r.VehicleTypeID],
			Owner:                          r.Owner,
			UseCharacter:                   r.UseCharacter,
			VIN:                            r.VIN,
			IssuingOrganizations:           r.IssuingOrganizations,
			RegisterDate:                   registerDate,
			IssueDate:                      issueDate,
			VehicleEnergyType:              vehicleEnergyTypes[r.VehicleEnergyTypeID],
			VehicleTonnage:                 r.VehicleTonnage,
			GrossMass:                      r.GrossMass,
			RoadTransportCertificateNumber: r.RoadTransportCertificateNumber,
			TrailerVehiclePlateNumber:      r.TrailerVehiclePlateNumber,
			Remark:                         "",
		}
	}

	return vehicles
}

type vehicle struct {
	ID                             string
	VehicleNumber                  string
	VehiclePlateColor              string
	VehiclePlateColorCode          string
	VehicleType                    string
	VehicleTypeID                  string
	UseCharacter                   string
	Owner                          string
	VIN                            string
	IssuingOrganizations           string
	IssuingOrganizationsID         string
	RegisterDate                   time.Time
	IssueDate                      time.Time
	VehicleEnergyType              string
	VehicleEnergyTypeID            string
	GrossMass                      string
	RoadTransportCertificateNumber string
	TrailerVehiclePlateNumber      string
	VehicleTonnage                 string
	RoadTransportBusinessLicenseNo string
}

func (v *vehicle) ReportSQL() string {
	return `
SELECT 
	a.id,
    a.vehicle_license_number as vehicle_number,
	a.vehicle_license_color as vehicle_plate_color,
	a.vehicle_license_color_id as vehicle_plate_color_code,
    a.vehicle_type_name as vehicle_type,
	a.vehicle_type_id as vehicle_type_id,
	a.vehicle_use_nature as use_character,
	a.vehicle_owner as owner,
	a.vehicle_identification_code as vin,
    a.certifying_authority as issuing_organizations,
	a.certification_department_id as issuing_organizations_id,
	a.reg_date as register_date,
	a.certifying_date as issue_date,
    a.vehicle_energy_type as vehicle_energy_type,
	a.vehicle_energy_type_id as vehicle_energy_type_id,
	a.vehicle_total_weight as gross_mass,
    a.transport_permit_number as road_transport_certificate_number,
	b.vehicle_license_number as trailer_vehicle_plate_number,
    ( CASE a.vehicle_type_name
    WHEN '重型半挂牵引车' 
		THEN b.nuclear_load_weight
    ELSE a.nuclear_load_weight
    END) AS vehicle_tonnage,
	a.road_transport_business_license_no
FROM
    tms_vehicle a
    LEFT JOIN tms_trailer_info AS b ON a.trailer_info_id = b.id
	LEFT JOIN tms_network_upload_info AS c ON a.id = c.association_id AND c.association_type = 2
WHERE 
	a.is_delete = 0 
	AND a.audit_status = 1
	AND c.is_upload_city = 0
ORDER BY a.create_time DESC
	`
}
