package wlhyverify

import (
	"wlhy/model"
	"wlhy/toolbox/logger"
)

func vehiclePlateColorCode() map[string]string {
	rows, err := model.DB.Table("sys_dict_item").
		Select([]string{"id", "item_value"}).
		Where("dict_id = ?", "1217986119487107073").
		Rows()
	if err != nil {
		logger.Stdout.Error(err.Error())
		return nil
	}

	colorMap := make(map[string]string)
	for rows.Next() {
		var id string
		var itemValue string
		if err := rows.Scan(&id, &itemValue); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}
		colorMap[id] = itemValue
	}
	return colorMap
}

func vehicleTypeCode() map[string]string {
	rows, err := model.DB.Table("sys_dict_item").
		Select([]string{"id", "item_value"}).
		Where("dict_id = ?", "1217979431082176514").
		Rows()
	if err != nil {
		logger.Stdout.Error(err.Error())
		return nil
	}

	typeMap := make(map[string]string)
	for rows.Next() {
		var id string
		var itemValue string
		if err := rows.Scan(&id, &itemValue); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}
		typeMap[id] = itemValue
	}
	return typeMap
}

func vehicleEnergyCode() map[string]string {
	rows, err := model.DB.Table("sys_dict_item").
		Select([]string{"id", "item_value"}).
		Where("dict_id = ?", "1217982195845738497").
		Rows()
	if err != nil {
		logger.Stdout.Error(err.Error())
		return nil
	}

	vehicleEnergyType := make(map[string]string)
	for rows.Next() {
		var id string
		var itemValue string
		if err := rows.Scan(&id, &itemValue); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}
		vehicleEnergyType[id] = itemValue
	}
	return vehicleEnergyType
}

func bankCode() map[string]string {
	rows, err := model.DB.Table("sys_dict_item").
		Select([]string{"id", "item_value"}).
		Where("dict_id = ?", "1217987220508684290").
		Rows()
	if err != nil {
		logger.Stdout.Error(err.Error())
		return nil
	}

	bankType := make(map[string]string)
	for rows.Next() {
		var id string
		var itemValue string
		if err := rows.Scan(&id, &itemValue); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}
		bankType[id] = itemValue
	}
	return bankType
}

func goodsTypeCode() map[string]string {
	rows, err := model.DB.Table("sys_category").
		Select([]string{"id", "code"}).
		Where("is_delete = ?", 0).
		Rows()

	if err != nil {
		logger.Stdout.Error(err.Error())
		return nil
	}

	goodsTypeMap := make(map[string]string)
	for rows.Next() {
		var id string
		var code string
		if err := rows.Scan(&id, &code); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}
		goodsTypeMap[id] = code
	}
	return goodsTypeMap
}
