package wlhyverify

import (
	"fmt"
	"strconv"
	"strings"
	"time"
	"wlhy/model"
	"wlhy/single/wlhycity/wlhyorg"
	"wlhy/toolbox/logger"
)

func (w *WlhyVerify) TransportNote(action string) map[string]wlhyorg.TransportNote {
	sql := ""
	transportNoteT := &transportNote{}

	if action == "report" {
		sql = transportNoteT.ReportSQL()
	}
	if sql == "" {
		return nil
	}

	var results []transportNote
	if err := model.DB.Raw(sql).Scan(&results).Error; err != nil {
		logger.Stdout.Error(err.Error())
		return nil
	}
	fmt.Println("总数: " + strconv.Itoa(len(results)))

	// 获取货物类型数据
	goodsType := goodsTypeCode()

	// 获取车牌颜色数据
	plateColor := vehiclePlateColorCode()

	// 校验并组装数据
	fail := 0
	transportNotes := make(map[string]wlhyorg.TransportNote)
	for _, v := range results {
		// 组装装货地址
		if strings.Contains(v.<PERSON>dd<PERSON>, v.Lo<PERSON>Name) {
			v.LoadingAddress = strings.ReplaceAll(v.LoadingAddress, v.LoadingName, "")
		}
		placeOfLoading := v.LoadingName + v.LoadingAddress

		// 组装卸货地址
		if strings.Contains(v.UnloadAddress, v.UnloadName) {
			v.UnloadAddress = strings.ReplaceAll(v.UnloadAddress, v.UnloadName, "")
		}
		goodsReceiptPlace := v.UnloadName + v.UnloadAddress

		if v.TrailerVehiclePlateColorCode == "" {
			v.TrailerVehiclePlateColorCode = "2"
		}

		//  判断车辆类型，修改核定载
		if v.VehicleTypeName == "重型半挂牵引车" {
			v.NuclearLoadWeight = v.TrailerNuclearLoadWeight
		}

		// 校验货物重量
		if v.NuclearLoadWeight > 0 && v.GoodsItemGrossWeight > v.NuclearLoadWeight {
			v.GoodsItemGrossWeight = v.NuclearLoadWeight
		}

		shortfallFlag := 0
		if v.ShortfallFlag != 0 {
			shortfallFlag = 1
		}

		if strings.Contains(v.VehicleOwner, "公司") {
			v.ActualCarrierName = v.VehicleOwner + "（" + v.ActualCarrierName + "）"
		}
		transportNote := wlhyorg.TransportNote{
			OriginalDocumentNumber:        v.OriginalDocumentNumber,
			ShippingNoteNumber:            v.ShippingNoteNumber,
			SerialNumber:                  "0000",
			VehicleAmount:                 "1",
			TransportTypeCode:             "1",
			SendToProDateTime:             time.Now().Format("20060102150405"),
			Carrier:                       "赤峰现代智慧物流有限公司",
			UnifiedSocialCreditIdentifier: "91150402MACRQHHW1K",
			PermitNumber:                  "150402605841",
			ConsignmentDateTime:           v.ConsignmentDateTime.Format("20060102150405"),
			BusinessTypeCode:              "1002996",
			DespatchActualDateTime:        v.DespatchActualDateTime.Format("20060102150405"),
			GoodsReceiptDateTime:          v.GoodsReceiptDateTime.Format("20060102150405"),
			ConsignorInfo: []wlhyorg.TransportNoteConsignorInfo{
				{
					Consignor:              v.Consignor,
					ConsignorID:            v.ConsignorID,
					PlaceOfLoading:         placeOfLoading,
					CountrySubdivisionCode: v.LoadCountrySubdivisionCode,
				},
			},
			ConsigneeInfo: []wlhyorg.TransportNoteConsigneeInfo{
				{
					Consignee:              v.Consignee,
					GoodsReceiptPlace:      goodsReceiptPlace,
					CountrySubdivisionCode: v.UnloadCountrySubdivisionCode,
				},
			},
			TotalMonetaryAmount: fmt.Sprintf("%.3f", v.TotalMonetaryAmount),
			VehicleInfo: []wlhyorg.TransportNoteVehicleInfo{
				{
					VehicleNumber:                v.VehicleNumber,
					VehiclePlateColorCode:        plateColor[v.VehiclePlateColorCode],
					TrailerVehiclePlateNumber:    v.TrailerVehiclePlateNumber,
					TrailerVehiclePlateColorCode: plateColor[v.TrailerVehiclePlateColorCode],
					DespatchActualDateTime:       v.DespatchActualDateTime.Format("20060102150405"),
					GoodsReceiptDateTime:         v.GoodsReceiptDateTime.Format("20060102150405"),
					Driver: []wlhyorg.TransportNoteDriver{
						{
							DriverName:     v.DriverName,
							DrivingLicense: v.DrivingLicense,
						},
					},
					GoodsInfo: []wlhyorg.TransportNoteGoodsInfo{
						{
							DescriptionOfGoods:          v.DescriptionOfGoods,
							CargoTypeClassificationCode: goodsType[v.CargoTypeClassificationCode],
							GoodsItemGrossWeight:        fmt.Sprintf("%.3f", v.GoodsItemGrossWeight*1000),
							ShortFallWeight:             fmt.Sprintf("%.3f", v.ShortFallWeight*1000),
						},
					},
				},
			},
			ActualCarrierInfo: []wlhyorg.TransportNoteActualCarrierInfo{
				{
					ActualCarrierName:            v.ActualCarrierName,
					ActualCarrierBusinessLicense: v.ActualCarrierBusinessLicense,
					ActualCarrierID:              v.ActualCarrierID,
				},
			},
			InsuranceInformation: []wlhyorg.TransportNoteInsuranceInformation{
				{
					PolicyNumber:         "none",
					InsuranceCompanyCode: "none",
				},
			},
			Remark:           "",
			TransportMileage: fmt.Sprintf("%.2f", v.TransportMileage),
			ShortfallFlag:    fmt.Sprintf("%d", shortfallFlag),
		}

		transportNotes[v.ID] = transportNote
	}

	fmt.Println("失败数:" + strconv.Itoa(fail))
	return transportNotes
}

type transportNote struct {
	OriginalDocumentNumber       string
	ID                           string
	ShippingNoteNumber           string
	SendToProDateTime            string
	ConsignmentDateTime          time.Time
	DespatchActualDateTime       time.Time
	GoodsReceiptDateTime         time.Time
	Consignor                    string
	ConsignorID                  string
	LoadingName                  string
	LoadingAddress               string
	LoadCountrySubdivisionCode   string
	Consignee                    string
	UnloadName                   string
	UnloadAddress                string
	UnloadCountrySubdivisionCode string
	TotalMonetaryAmount          float64
	VehicleNumber                string
	DriverName                   string
	DrivingLicense               string
	DescriptionOfGoods           string
	GoodsItemGrossWeight         float64
	VehiclePlateColorCode        string
	CargoTypeClassificationCode  string
	ActualCarrierID              string
	ActualCarrierName            string
	ActualCarrierBusinessLicense string
	NuclearLoadWeight            float64
	TrailerVehiclePlateNumber    string
	TrailerVehiclePlateColorCode string
	TrailerNuclearLoadWeight     float64
	VehicleTypeName              string
	ShortfallFlag                int
	ShortFallWeight              float64
	TransportMileage             float64
	VehicleOwner                 string
}

func (t *transportNote) ReportSQL() string {
	return `
SELECT
	b.goods_number AS original_document_number,
	a.id,
	a.transportation_number AS shipping_note_number,
	a.note_reporting_time AS send_to_pro_date_time,
	a.create_time AS consignment_date_time,
	a.loading_time AS despatch_actual_date_time,
	a.unload_time AS goods_receipt_date_time,
	( CASE WHEN c.shipper_type = 0 THEN c.shipper_name ELSE ( SELECT shipper_name FROM tms_shipper WHERE is_delete = 0 AND shipper_id = c.company_id ) END ) AS consignor,
	( CASE WHEN c.shipper_type = 0 THEN c.identification_number ELSE ( SELECT social_credit_code FROM tms_shipper WHERE is_delete = 0 AND shipper_id = c.company_id ) END ) AS consignor_id,
	b.loading_name,
	b.loading_address,
	b.loading_area_id AS load_country_subdivision_code,
	b.discharger_name AS consignee,
	b.unload_name,
	b.unload_address,
	b.unload_area_id AS unload_country_subdivision_code,
	a.freight_gross AS total_monetary_amount,
	a.transportation_plate AS vehicle_number,
	d.driver_name AS driver_name,
	d.identification_number AS driving_license,
	IF ( length( trim( b.goods_name ))> 0, b.goods_name, b.goods_type ) AS description_of_goods,
	a.loading_number AS goods_item_gross_weight,
	f.vehicle_license_color_id AS vehicle_plate_color_code,
	b.goods_type_id AS cargo_type_classification_code,
	g.identification_number AS actual_carrier_id,
	CASE	
		WHEN g.type = 0 THEN
			g.driver_name ELSE g.driver_name 
	END AS actual_carrier_name,
	CASE	
		WHEN f.road_transport_business_license_no = "" THEN
			f.transport_permit_number ELSE f.road_transport_business_license_no 
	END AS actual_carrier_business_license,
	f.nuclear_load_weight AS nuclear_load_weight,
	t.vehicle_license_number AS trailer_vehicle_plate_number,
	t.vehicle_license_color_id AS trailer_vehicle_plate_color_code,
	t.nuclear_load_weight AS trailer_nuclear_load_weight,
	f.vehicle_type_name AS vehicle_type_name,

	a.road_loss_settlement_method AS shortfall_flag,
	a.loss_weight_of_goods AS short_fall_weight,
	a.actual_haul_distance AS transport_mileage,
	f.vehicle_owner AS vehicle_owner
FROM
	tms_transport_note a
	LEFT JOIN tms_order b ON a.order_id = b.id
	LEFT JOIN tms_shipper c ON a.create_by = c.shipper_id
	LEFT JOIN tms_driver d ON a.transportation_driver_id = d.driver_id
	LEFT JOIN tms_driver g ON a.payee_id = g.driver_id
	LEFT JOIN tms_vehicle f ON a.transportation_car_id = f.id
	LEFT JOIN tms_trailer_info t ON f.trailer_info_id = t.id 
	JOIN tms_network_upload_info n ON a.id = n.association_id AND n.association_type = 3
WHERE
	a.is_delete = 0 
	AND a.is_up_receipt = 1
    AND a.waybill_status in (4,5,8)
	AND n.is_upload_city = 0
ORDER BY a.create_time ASC
	`
}
