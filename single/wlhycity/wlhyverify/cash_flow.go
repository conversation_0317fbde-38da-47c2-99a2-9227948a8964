package wlhyverify

import (
	"fmt"
	"strconv"
	"strings"
	"time"
	"wlhy/model"
	"wlhy/single/wlhycity/wlhyorg"
	"wlhy/toolbox/logger"
)

func (w *WlhyVerify) CashFlow(action string) map[string]wlhyorg.CashFlow {
	sql := ""
	cashFlowT := &cashFlow{}
	if action == "report" {
		sql = cashFlowT.ReportSQL()
	}
	if sql == "" {
		return nil
	}

	// 获取数据
	results := []cashFlow{}
	if err := model.DB.Raw(sql).Scan(&results).Error; err != nil {
		logger.Stdout.Error(err.Error())
		return nil
	}
	fmt.Println("总数: " + strconv.Itoa(len(results)))

	plateColors := vehiclePlateColorCode()
	bankCodes := bankCode()

	cashFlows := make(map[string]wlhyorg.CashFlow)
	for _, v := range results {
		cashFlow := wlhyorg.CashFlow{
			DocumentNumber:        v.DocumentNumber,
			SendToProDateTime:     time.Now().Format("**************"),
			Carrier:               v.Carrier,
			ActualCarrierID:       v.ActualCarrierID,
			VehicleNumber:         v.VehicleNumber,
			VehiclePlateColorCode: plateColors[v.VehiclePlateColorCode],
			ShippingNoteList: []wlhyorg.CashFlowShippingNote{
				{
					ShippingNoteNumber:  v.ShippingNoteNumber,
					SerialNumber:        "0000",
					TotalMonetaryAmount: fmt.Sprintf("%.3f", v.TotalMonetaryAmount),
				},
			},
			Financiallist: []wlhyorg.CashFlowFinancial{
				{
					PaymentMeansCode: "32",
					Recipient:        v.Recipient,
					ReceiptAccount:   v.ReceiptAccount,
					BankCode:         bankCodes[v.BankCode],
					SequenceCode:     strings.Trim(strings.TrimSpace(v.SequenceCode), "\n"),
					MonetaryAmount:   fmt.Sprintf("%.3f", v.MonetaryAmount),
					DateTime:         v.DateTime.Format("**************"),
				},
			},
			Remark: "",
		}

		cashFlows[v.ID] = cashFlow
	}

	return cashFlows
}

type cashFlow struct {
	ID                    string
	DocumentNumber        string
	Carrier               string
	ActualCarrierID       string
	SequenceCode          string
	ShippingNoteNumber    string
	TotalMonetaryAmount   float64
	MonetaryAmount        float64
	DateTime              time.Time
	VehiclePlateColorCode string
	VehicleNumber         string
	Recipient             string
	ReceiptAccount        string
	BankCode              string
}

func (c *cashFlow) ReportSQL() string {
	return `
SELECT
    b.id,
	b.id AS document_number,
	c.driver_name AS carrier,
	c.identification_number AS actual_carrier_id,
	a.serial_number AS sequence_code,
    b.transportation_number AS shipping_note_number,
	sum(a.transaction_amount) AS total_monetary_amount,
    sum(a.transaction_amount) AS monetary_amount,
    a.consumption_time AS date_time,
	e.vehicle_license_color_id AS vehicle_plate_color_code,
	e.vehicle_license_number AS vehicle_number,
    d.account_owner_name AS recipient,
	d.bank_card_number AS receipt_account,
	d.bank_id AS bank_code
FROM 
	tms_transport_note b
    LEFT JOIN tms_expense_record a ON a.waybill_id=b.id AND a.revenue_and_expenditure_types = 0
    LEFT JOIN tms_driver c ON b.payee_id = c.driver_id
    LEFT JOIN tms_driver f ON b.transportation_driver_id = f.driver_id
    LEFT JOIN tms_bank_card d ON b.payee_id = d.bank_card_user_id
    LEFT JOIN tms_vehicle e ON b.transportation_car_id = e.id
	LEFT JOIN tms_network_upload_info g ON b.id = g.association_id AND g.association_type = 4 and g.sys_org_code = 'A03'
WHERE 
	b.is_delete = 0
	AND b.total_freight_status = 2
    AND b.is_note_reporting = 1
	AND b.waybill_status in (4,5,8)
	AND g.is_upload_city = 0
GROUP BY
	b.id
ORDER BY
	b.create_time ASC
	`
}
