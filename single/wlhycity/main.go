package main

import (
	"wlhy/single/wlhycity/wlhyorg"
	"wlhy/single/wlhycity/wlhyverify"
	"wlhy/toolbox/logger"

	"github.com/spf13/pflag"
)

var category string

func main() {
	pflag.StringVarP(&category, "category", "c", "all", "用于指定需要上报的数据类型，可选值为all，driver，vehicle，transport，cashflow，none")
	pflag.Parse()

	wlhyOrg := &wlhyorg.WlhyOrg{
		PublicKey:    "045AD41B6AE6453890C7579C6E0A093B77AF484CC1C261D7A49B7EB3125CD1D8C80089F070E809EA4315E22B53E98D26D26EACBC1CCCEBFC564969A2BF96602B26",
		Token:        "9b8b9a0d513845bd8a549e171789998da4feff85323a4f2c8cd40be0b64fe09d",
		PublicKeyUrl: "",
		TokenUrl:     "",
		ReportUrl:    "https://cfofms.cfhszy.com:1968/inbound/inbound/upload/batch",
		Account:      "100001",
		Password:     "hszy18191314!",
	}

	verify := &wlhyverify.WlhyVerify{}

	if category == "all" || category == "driver" {
		Driver(verify, wlhyOrg)
	}
	if category == "all" || category == "vehicle" {
		Vehicle(verify, wlhyOrg)
	}
	if category == "all" || category == "transport" {
		Transport(verify, wlhyOrg)
	}
	if category == "all" || category == "cashflow" {
		CashFlow(verify, wlhyOrg)
	}
}

func Driver(verify *wlhyverify.WlhyVerify, wlhyOrg *wlhyorg.WlhyOrg) {
	// 上报数据
	drivers := verify.Driver("report")
	for associationID, driver := range drivers {
		if err := wlhyOrg.SendDriver(associationID, driver); err != nil {
			logger.Stdout.Error(err.Error())
		}
	}
}

func Vehicle(verify *wlhyverify.WlhyVerify, wlhyOrg *wlhyorg.WlhyOrg) {
	// 上报数据
	vehicles := verify.Vehicle("report")
	for associationID, vehicle := range vehicles {
		if err := wlhyOrg.SendVehicle(associationID, vehicle); err != nil {
			logger.Stdout.Error(err.Error())
		}
	}
}

func Transport(verify *wlhyverify.WlhyVerify, wlhyOrg *wlhyorg.WlhyOrg) {
	// 上报数据
	transportNotes := verify.TransportNote("report")
	for associationID, transportNote := range transportNotes {
		if err := wlhyOrg.SendTransportNote(associationID, transportNote); err != nil {
			logger.Stdout.Error(err.Error())
		}
	}
}

func CashFlow(verify *wlhyverify.WlhyVerify, wlhyOrg *wlhyorg.WlhyOrg) {
	// 上报数据
	cashFlows := verify.CashFlow("report")
	for associationID, cashFlow := range cashFlows {
		if err := wlhyOrg.SendCashFlow(associationID, cashFlow); err != nil {
			logger.Stdout.Error(err.Error())
		}
	}
}
