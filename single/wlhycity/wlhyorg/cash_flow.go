package wlhyorg

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/google/uuid"
)

type CashFlow struct {
	DocumentNumber        string                 `json:"documentNumber"`        // 单证号	必填，本资金流水单号。
	SendToProDateTime     string                 `json:"sendToProDateTime"`     // 资金流水单上传时间 必填，本资金流水单上传到省级监测系统的时间。
	Carrier               string                 `json:"carrier"`               // 实际承运人名称 必填，对应运单技术规范中第44项。
	ActualCarrierID       string                 `json:"actualCarrierID"`       // 实际承运人统一社会信用代码或证件号码 必填，对应运单技术规范中第46项。
	VehicleNumber         string                 `json:"vehicleNumber"`         // 车辆牌照号	对应运单技术规范中第26项。
	VehiclePlateColorCode string                 `json:"vehiclePlateColorCode"` // 车牌颜色代码 对应运单技术规范中第27项。
	ShippingNoteList      []CashFlowShippingNote `json:"shippingNoteList"`      // 运单列表 必填。
	Financiallist         []CashFlowFinancial    `json:"financiallist"`         // 财务列表 必填。
	Remark                string                 `json:"remark"`                // 备注 选填。
}

type CashFlowShippingNote struct {
	ShippingNoteNumber  string `json:"shippingNoteNumber"`  // 托运单号 必填，对应运单技术规范中第2项。
	SerialNumber        string `json:"serialNumber"`        // 分段分单号 必填，对应运单技术规范中第3项。
	TotalMonetaryAmount string `json:"totalMonetaryAmount"` // 总金额 该笔运输实际发生费用，含燃油、路桥费和实际支付金额。
}

type CashFlowFinancial struct {
	PaymentMeansCode string `json:"paymentMeansCode"` // 付款方式代码 必填，代码集参见wlhy.mot.gov.cn。
	Recipient        string `json:"recipient"`        // 收款方名称 必填。
	ReceiptAccount   string `json:"receiptAccount"`   // 收款帐户信息 必填，银行卡号或其他收款帐号。
	BankCode         string `json:"bankCode"`         // 收款方银行代码 选填，代码集参见wlhy.mot.gov.cn。
	SequenceCode     string `json:"sequenceCode"`     // 流水号/序列号 必填，银行或第三方支付平台的资金流水单号。
	MonetaryAmount   string `json:"monetaryAmount"`   // 实际支付金额 必填，资金流水金额，货币单位为人民币，保留3位小数，如整数的话，以.000填充。
	DateTime         string `json:"dateTime"`         // 日期时间 资金流水实际发生时间。YYYYMMDDhhmmss
}

func (w *WlhyOrg) SendCashFlow(recordID string, cashFlow CashFlow) error {
	// 生成随机的对称加密秘钥
	aesKey := uuid.NewString()[0:16]

	// 将报文内容json化
	dispatchJson, err := json.Marshal(cashFlow)
	if err != nil {
		return err
	}
	fmt.Println("dispatchJson:", string(dispatchJson))

	// 加密报文表内容
	encryptedCode, err := sm2Encrypt(w.PublicKey, aesKey)
	if err != nil {
		return err
	}
	encryptedContent, err := sm4Encrypt(aesKey, string(dispatchJson))
	if err != nil {
		return err
	}

	cashFlowInfoData := []map[string]string{
		{
			"documentName":           "资金流水单",
			"documentVersionNumber":  "1.1",
			"encryptedCode":          string(encryptedCode),
			"encryptedContent":       string(encryptedContent),
			"ipcType":                "WLHY_ZJ1001",
			"messageReferenceNumber": strings.ReplaceAll(uuid.NewString(), "-", ""),
			"messageSendingDateTime": time.Now().Format("**************"),
			"senderCode":             w.Account,
			"token":                  w.Token,
			"userName":               w.Account,
		},
	}
	// fmt.Println(cashFlowInfoData)

	captalJson, err := json.Marshal(cashFlowInfoData)
	if err != nil {
		return err
	}

	resp, err := http.Post(w.ReportUrl, "application/json", bytes.NewBuffer([]byte(captalJson)))
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	content, _ := io.ReadAll(resp.Body)

	if err := success(AssociationCaptal, recordID, string(content)); err != nil {
		return err
	}

	return nil
}
