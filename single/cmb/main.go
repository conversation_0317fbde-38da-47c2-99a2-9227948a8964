package main

import (
	"context"
	"errors"
	"net/http"
	"os"
	"os/signal"
	"time"
	"wlhy/thirdparty/cmb"

	"github.com/labstack/echo/v4"
	"github.com/labstack/echo/v4/middleware"
)

var hCmb *cmb.CmbBank
var yCmb *cmb.CmbBank

func init() {
	hCmb = cmb.GetTest()
	yCmb = cmb.GetTest()
}

func main() {
	authKey := "4JcjEU2Zm7cl1YnSFCLhbK7ibCW9ajIIkVAi"

	e := echo.New()

	e.Use(middleware.Logger())
	e.GET("/ping", func(c echo.Context) error {
		return c.String(http.StatusOK, "pong")
	})

	g := e.Group("/v1")
	g.Use(middleware.KeyAuthWithConfig(middleware.KeyAuthConfig{
		KeyLookup: "query:key",
		Validator: func(auth string, c echo.Context) (bool, error) {
			return auth == authKey, nil
		},
	}))

	sub := g.Group("/sub")
	sub.POST("/add", subAccountAdd)
	sub.GET("/query", subAccountQuery)
	sub.GET("/update", subAccountUpdate)
	sub.GET("/close", subAccountClose)

	ctx, stop := signal.NotifyContext(context.Background(), os.Interrupt)
	defer stop()

	go func() {
		if err := e.Start(":1323"); err != nil && !errors.Is(err, http.ErrServerClosed) {
			panic(err)
		}
	}()

	<-ctx.Done()

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	if err := e.Shutdown(ctx); err != nil {
		panic(err)
	}
}
