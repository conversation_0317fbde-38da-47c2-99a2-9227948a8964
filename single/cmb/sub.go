package main

import (
	"net/http"

	"github.com/labstack/echo/v4"
)

func subAccountAdd(c echo.Context) error {
	cmb := c.QueryParam("cmb")
	dmanam := c.QueryParam("dmanam")
	dmanbr := c.Query<PERSON>m("dmanbr")

	if cmb == "hszy" {
		if _, err := hCmb.SubAccountAdd(dmanbr, dmanam); err != nil {
			return c.String(http.StatusBadGateway, err.Error())
		}
	} else if cmb == "ybs" {
		if _, err := yCmb.SubAccountAdd(dmanbr, dmanam); err != nil {
			return c.String(http.StatusBadGateway, err.Error())
		}
	}

	return c.String(http.StatusOK, "ok")
}

func subAccountQuery(c echo.Context) error {
	cmb := c.QueryParam("cmb")
	dmanbr := c.Query<PERSON>m("dmanbr")

	if cmb == "hszy" {
		r, err := hCmb.SubAccountQuery(dmanbr)
		if err != nil {
			return c.String(http.StatusBadGateway, err.Error())
		}
		return c.JSON(http.StatusOK, r)
	} else if cmb == "ybs" {
		r, err := yCmb.SubAccountQuery(dmanbr)
		if err != nil {
			return c.String(http.StatusBadGateway, err.Error())
		}
		return c.JSON(http.StatusOK, r)
	}

	return c.String(http.StatusOK, "ok")
}

func subAccountUpdate(c echo.Context) error {
	cmb := c.QueryParam("cmb")
	dmanbr := c.QueryParam("dmanbr")
	dmanam := c.QueryParam("dmanam")

	if cmb == "hszy" {
		if _, err := hCmb.SubAccountUpdate(dmanbr, dmanam); err != nil {
			return c.String(http.StatusBadGateway, err.Error())
		}
	} else if cmb == "ybs" {
		if _, err := yCmb.SubAccountUpdate(dmanbr, dmanam); err != nil {
			return c.String(http.StatusBadGateway, err.Error())
		}
	}

	return c.String(http.StatusOK, "ok")
}

func subAccountClose(c echo.Context) error {
	cmb := c.QueryParam("cmb")
	dmanbr := c.QueryParam("dmanbr")

	if cmb == "hszy" {
		if _, err := hCmb.SubAccountClose(dmanbr); err != nil {
			return c.String(http.StatusBadGateway, err.Error())
		}
	} else if cmb == "ybs" {
		if _, err := yCmb.SubAccountClose(dmanbr); err != nil {
			return c.String(http.StatusBadGateway, err.Error())
		}
	}

	return c.String(http.StatusOK, "ok")
}
