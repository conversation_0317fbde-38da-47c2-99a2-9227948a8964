package main

import (
	"crypto/tls"
	"encoding/json"
	"fmt"
	"os"
	"strconv"
	"time"
	"wlhy/model"
	"wlhy/toolbox/logger"

	"github.com/xuri/excelize/v2"
	"gopkg.in/gomail.v2"
)

type UploadRecord struct {
	Category      int
	AssociationID string
	Result        string
	CreatedAt     time.Time
	VerifyState   int
	UploadState   int
	ErrorMessage  string
}

func statistics(nowTime time.Time) {

	/* yestodayStartTime := nowTime.Add(-24*time.Hour).Format("2006-01-02") + " 00:00:00"
	yestodayEndTime := nowTime.Add(-24*time.Hour).Format("2006-01-02") + " 23:59:59" */

	todayStartTime := nowTime.Format("2006-01-02") + " 00:00:00"
	todayEndTime := nowTime.Format("2006-01-02") + " 23:59:59"

	// 查询昨日数据
	/* var yestodayRecords []UploadRecord
	if err := model.DB.Table("tms_network_upload_info_records AS a").
		Joins("JOIN tms_network_upload_info AS b ON a.category = b.association_type AND a.association_id = b.association_id").
		Where("a.created_at BETWEEN ? AND ?", yestodayStartTime, yestodayEndTime).
		Select("a.category", "a.association_id", "a.result", "a.created_at", "b.upload_report_check_state AS verify_state", "b.upload_report_state AS upload_state", "b.upload_report_check_msg AS error_message").
		Scan(&yestodayRecords).Error; err != nil {
		fmt.Println(err)
	} */

	// 查询今日数据
	var todayRecords []UploadRecord
	if err := model.DB.Table("tms_network_upload_info_records AS a").
		Joins("JOIN tms_network_upload_info AS b ON a.category = b.association_type AND a.association_id = b.association_id").
		Where("a.created_at BETWEEN ? AND ?", todayStartTime, todayEndTime).
		Select("a.category", "a.association_id", "a.result", "a.created_at", "b.upload_report_check_state AS verify_state", "b.upload_report_state AS upload_state", "b.upload_report_check_msg AS error_message").
		Scan(&todayRecords).Error; err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	// statisticsStep(yestodayRecords, "revise")
	statisticsStep(todayRecords, "")
}

func statisticsStep(records []UploadRecord, tag string) {
	// 统计数据
	statisticalData := map[string]int{
		"verifyDriverSuccess":         0,
		"verifyDriverFail":            0,
		"verifyVehicleSuccess":        0,
		"verifyVehicleFail":           0,
		"verifyTransportNoteSuccess:": 0,
		"verifyTransportNoteFail":     0,
		"verifyCashFlowSuccess":       0,
		"verifyCashFlowFail":          0,
	}

	driverFails := make(map[string]map[string]string)
	vehicleFails := make(map[string]map[string]string)
	transportNoteFails := make(map[string]map[string]string)
	capitalFails := make(map[string]map[string]string)

	for _, v := range records {
		if v.Category == 1 {
			if _, ok := driverFails[v.AssociationID]; ok {
				continue
			}
			if v.VerifyState == 1 {
				statisticalData["verifyDriverSuccess"]++
				driverFails[v.AssociationID] = map[string]string{}
			} else if v.VerifyState == 2 {
				statisticalData["verifyDriverFail"]++

				var current map[string]string
				json.Unmarshal([]byte(v.Result), &current)
				current["error"] = v.ErrorMessage
				driverFails[v.AssociationID] = current
			}
		}

		if v.Category == 2 {
			if _, ok := vehicleFails[v.AssociationID]; ok {
				continue
			}
			if v.VerifyState == 1 {
				statisticalData["verifyVehicleSuccess"]++
				vehicleFails[v.AssociationID] = map[string]string{}
			} else if v.VerifyState == 2 {
				statisticalData["verifyVehicleFail"]++

				var current map[string]string
				json.Unmarshal([]byte(v.Result), &current)
				current["error"] = v.ErrorMessage
				vehicleFails[v.AssociationID] = current
			}
		}

		if v.Category == 3 {
			if _, ok := transportNoteFails[v.AssociationID]; ok {
				continue
			}
			if v.VerifyState == 1 {
				statisticalData["verifyTransportNoteSuccess:"]++
				transportNoteFails[v.AssociationID] = map[string]string{}
			} else if v.VerifyState == 2 {
				statisticalData["verifyTransportNoteFail"]++

				var current map[string]string
				json.Unmarshal([]byte(v.Result), &current)
				current["error"] = v.ErrorMessage
				transportNoteFails[v.AssociationID] = current
			}
		}

		if v.Category == 4 {
			if _, ok := capitalFails[v.AssociationID]; ok {
				continue
			}
			if v.VerifyState == 1 {
				capitalFails[v.AssociationID] = map[string]string{}
				statisticalData["verifyCashFlowSuccess"]++
			} else if v.VerifyState == 2 {
				capitalFails[v.AssociationID] = map[string]string{}
				statisticalData["verifyCashFlowFail"]++
			}
		}
	}

	// 清理空数据
	for k, v := range driverFails {
		if len(v) == 0 {
			delete(driverFails, k)
		}
	}
	for k, v := range vehicleFails {
		if len(v) == 0 {
			delete(vehicleFails, k)
		}
	}
	for k, v := range transportNoteFails {
		if len(v) == 0 {
			delete(transportNoteFails, k)
		}
	}
	for k, v := range capitalFails {
		if len(v) == 0 {
			delete(capitalFails, k)
		}
	}

	f := excelize.NewFile()
	defer func() {
		if err := f.Close(); err != nil {
			fmt.Println(err)
		}
	}()

	// Create a new sheet.
	index1, err := f.NewSheet("总计")
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	f.SetActiveSheet(index1)

	f.SetCellValue("总计", "A1", "司机信息单校验总数")
	f.SetCellValue("总计", "B1", statisticalData["verifyDriverSuccess"]+statisticalData["verifyDriverFail"])
	f.SetCellValue("总计", "A2", "司机信息单校验失败数量")
	f.SetCellValue("总计", "B2", statisticalData["verifyDriverFail"])
	f.SetCellValue("总计", "A3", "司机信息单校验成功数量")
	f.SetCellValue("总计", "B3", statisticalData["verifyDriverSuccess"])
	f.SetCellValue("总计", "A4", "车辆信息单校验总数")
	f.SetCellValue("总计", "B4", statisticalData["verifyVehicleSuccess"]+statisticalData["verifyVehicleFail"])
	f.SetCellValue("总计", "A5", "车辆信息单校验失败数量")
	f.SetCellValue("总计", "B5", statisticalData["verifyVehicleFail"])
	f.SetCellValue("总计", "A6", "车辆信息单校验成功数量")
	f.SetCellValue("总计", "B6", statisticalData["verifyVehicleSuccess"])
	f.SetCellValue("总计", "A7", "运单信息单校验总数")
	f.SetCellValue("总计", "B7", statisticalData["verifyTransportNoteSuccess:"]+statisticalData["verifyTransportNoteFail"])
	f.SetCellValue("总计", "A8", "运单信息单校验失败数量")
	f.SetCellValue("总计", "B8", statisticalData["verifyTransportNoteFail"])
	f.SetCellValue("总计", "A9", "运单信息单校验成功数量")
	f.SetCellValue("总计", "B9", statisticalData["verifyTransportNoteSuccess:"])
	f.SetCellValue("总计", "A10", "资金信息单校验总数")
	f.SetCellValue("总计", "B10", statisticalData["verifyCashFlowSuccess"]+statisticalData["verifyCashFlowFail"])
	f.SetCellValue("总计", "A11", "资金信息单校验失败数量")
	f.SetCellValue("总计", "B11", statisticalData["verifyCashFlowFail"])
	f.SetCellValue("总计", "A12", "资金信息单校验成功数量")
	f.SetCellValue("总计", "B12", statisticalData["verifyCashFlowSuccess"])

	sumOfReportData := sumOfReport()
	f.SetCellValue("总计", "A13", "司机信息单累计上报比率")
	f.SetCellValue("总计", "B13", sumOfReportData["driver"])
	f.SetCellValue("总计", "A14", "车辆信息单累计上报比率")
	f.SetCellValue("总计", "B14", sumOfReportData["vehicle"])
	f.SetCellValue("总计", "A15", "运单信息单累计上报比率")
	f.SetCellValue("总计", "B15", sumOfReportData["transportNote"])
	f.SetCellValue("总计", "A16", "资金流水单信息单累计上报比率")
	f.SetCellValue("总计", "B16", sumOfReportData["cashFlow"])

	// Set value of a cell.
	index2, err := f.NewSheet("司机信息")
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	f.SetCellValue("司机信息", "A1", "司机姓名")
	f.SetCellValue("司机信息", "B1", "手机号")
	f.SetCellValue("司机信息", "C1", "错误信息")
	f.SetActiveSheet(index2)

	start := 2
	for _, v := range driverFails {
		f.SetCellValue("司机信息", "A"+strconv.Itoa(start), v["driverName"])
		f.SetCellValue("司机信息", "B"+strconv.Itoa(start), v["phone"])
		f.SetCellValue("司机信息", "C"+strconv.Itoa(start), v["error"])
		start++
	}

	// Create a new sheet.
	index3, err := f.NewSheet("车辆信息")
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	f.SetActiveSheet(index3)

	// Set value of a cell.
	f.SetCellValue("车辆信息", "A1", "车牌号")
	f.SetCellValue("车辆信息", "B1", "错误信息")

	start = 2
	for _, v := range vehicleFails {
		f.SetCellValue("车辆信息", "A"+strconv.Itoa(start), v["vehicleLicenseNumber"])
		f.SetCellValue("车辆信息", "B"+strconv.Itoa(start), v["error"])
		start++
	}

	// Create a new sheet.
	index4, err := f.NewSheet("运单信息")
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	f.SetActiveSheet(index4)

	// Set value of a cell.
	f.SetCellValue("运单信息", "A1", "运单号")
	f.SetCellValue("运单信息", "B1", "车牌")
	f.SetCellValue("运单信息", "C1", "司机姓名")
	f.SetCellValue("运单信息", "D1", "司机号码")
	f.SetCellValue("运单信息", "E1", "错误信息")

	start = 2
	for _, v := range transportNoteFails {
		f.SetCellValue("运单信息", "A"+strconv.Itoa(start), v["transportationNumber"])
		f.SetCellValue("运单信息", "B"+strconv.Itoa(start), v["transportationPlate"])
		f.SetCellValue("运单信息", "C"+strconv.Itoa(start), v["transportationDriver"])
		f.SetCellValue("运单信息", "D"+strconv.Itoa(start), v["transportationPhone"])
		f.SetCellValue("运单信息", "E"+strconv.Itoa(start), v["error"])
		start++
	}

	filename := "监管平台上报异常数据-首次.xlsx"
	if tag == "revise" {
		filename = "监管平台上报异常数据-修正.xlsx"
	}

	f.SetActiveSheet(index1)
	if err := f.SaveAs(filename); err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	d := gomail.NewDialer("smtp.qq.com", 587, "<EMAIL>", "dtusitcqdzrrbcfd")
	d.TLSConfig = &tls.Config{InsecureSkipVerify: true}

	m := gomail.NewMessage()
	m.SetHeader("From", "<EMAIL>")
	m.SetHeader("To", []string{"<EMAIL>", "<EMAIL>", "<EMAIL>"}...)
	if tag == "revise" {
		m.SetHeader("Subject", time.Now().Format("20060102")+"监管平台上报异常数据-修正")
	} else {
		m.SetHeader("Subject", time.Now().Format("20060102")+"监管平台上报异常数据-首次")
	}
	m.Attach(filename)

	// Send the email to Bob, Cora and Dan.
	if err := d.DialAndSend(m); err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	os.Remove(filename)
	fmt.Println("监管平台上报异常数据邮件发送成功")
}

func sumOfReport() map[string]float64 {
	var driverTotal int64
	var vehicleTotal int64
	var transportationNoteTotal int64

	if err := model.DB.Table("tms_driver").Where("is_delete = ?", 0).Count(&driverTotal).Error; err != nil {
		logger.Stdout.Error(err.Error())
		return nil
	}

	if err := model.DB.Table("tms_vehicle").
		Where("vehicle_license_number NOT LIKE ?", "%挂%").
		Where("is_delete = ?", 0).
		Count(&vehicleTotal).Error; err != nil {
		logger.Stdout.Error(err.Error())
		return nil
	}

	if err := model.DB.Table("tms_transport_note").
		Where("waybill_status IN (?)", []int{4, 5, 8}).
		Where("is_delete = ?", 0).
		Count(&transportationNoteTotal).Error; err != nil {
		logger.Stdout.Error(err.Error())
		return nil
	}

	var driverUploaded int64
	var vehicleUploaded int64
	var transportationNoteUploaded int64

	if err := model.DB.Table("tms_network_upload_info").Where("association_type = ?", 1).Where("upload_report_state = ?", 1).Count(&driverUploaded).Error; err != nil {
		logger.Stdout.Error(err.Error())
		return nil
	}

	if err := model.DB.Table("tms_network_upload_info").Where("association_type = ?", 2).Where("upload_report_state = ?", 1).Count(&vehicleUploaded).Error; err != nil {
		logger.Stdout.Error(err.Error())
		return nil
	}

	if err := model.DB.Table("tms_network_upload_info").Where("association_type = ?", 3).Where("upload_report_state = ?", 1).Count(&transportationNoteUploaded).Error; err != nil {
		logger.Stdout.Error(err.Error())
		return nil
	}

	return map[string]float64{
		"driver":        float64(driverUploaded) / float64(driverTotal) * 100,
		"vehicle":       float64(vehicleUploaded) / float64(vehicleTotal) * 100,
		"transportNote": float64(transportationNoteUploaded) / float64(transportationNoteTotal) * 100,
		"cashFlow":      float64(transportationNoteUploaded) / float64(transportationNoteTotal) * 100,
	}
}
