package main

import (
	"fmt"
	"log"
	"os"
	"time"
	"wlhy/single/wlhyreport/wlhyorg"
	"wlhy/single/wlhyreport/wlhyverify"
	"wlhy/toolbox/logger"

	"github.com/spf13/pflag"
)

var tag string
var category string
var statisticsEnable bool

// 执行顺序
// wlhyorg -t verify
// wlhyorg -t report -s=true
func main() {
	pflag.StringVarP(&tag, "tag", "t", "", "verify,report")
	pflag.StringVarP(&category, "category", "c", "all", "用于指定需要上报的数据类型，可选值为all，driver，vehicle，transport，cashflow，none")
	pflag.BoolVarP(&statisticsEnable, "statistics", "s", false, "用于指定是否统计数据，为true则统计，为false则不统计")
	pflag.Parse()

	if tag == "" && !statisticsEnable {
		fmt.Println("请输入tag")
		os.Exit(1)
	}

	// 省监管平台
	wlhyOrg := &wlhyorg.WlhyOrg{
		PublicKey:    "",
		Token:        "",
		PublicKeyUrl: "https://nmg.wlhy.org.cn/wlhy-user/RsaController/getPubKey",
		TokenUrl:     "https://nmg.wlhy.org.cn/wlhy-user/user/exgLogin",
		ReportUrl:    "https://nmg.wlhy.org.cn/wlhy-exchange-kafka/message/sendMsg",
		Account:      "********",
		Password:     "hszy18191314!",
	}
	if err := wlhyOrg.TokenAndPublicKey(); err != nil {
		log.Fatal(err)
	}

	verify := &wlhyverify.WlhyVerify{}

	startTime := time.Now()
	if category == "all" || category == "driver" {
		Driver(verify, wlhyOrg)
	}
	if category == "all" || category == "vehicle" {
		Vehicle(verify, wlhyOrg)
	}
	if category == "all" || category == "transport" {
		Transport(verify, wlhyOrg)
	}
	if category == "all" || category == "cashflow" {
		CashFlow(verify, wlhyOrg)
	}
	endTime := time.Now()

	if tag == "verify" {
		verifyRecords(startTime, endTime)
	}

	if statisticsEnable {
		statistics(time.Now())
	}
}

func Driver(verify *wlhyverify.WlhyVerify, wlhyOrg *wlhyorg.WlhyOrg) {
	// 第一步 本地校验
	if tag == "verify" {
		verify.Driver("verify")
	}

	// 第二步 上报省监管平台
	if tag == "report" {
		drivers := verify.Driver("report")
		for associationID, driver := range drivers {
			if err := wlhyOrg.SendDriver(associationID, driver); err != nil {
				logger.Stdout.Error(err.Error())
			}
		}
	}
}

func Vehicle(verify *wlhyverify.WlhyVerify, wlhyOrg *wlhyorg.WlhyOrg) {
	// 第一步 本地校验
	if tag == "verify" {
		verify.Vehicle("verify")
	}

	// 第二步 上报省监管平台
	if tag == "report" {
		vehicles := verify.Vehicle("report")
		for associationID, vehicle := range vehicles {
			if err := wlhyOrg.SendVehicle(associationID, vehicle); err != nil {
				logger.Stdout.Error(err.Error())
			}
		}
	}
}

func Transport(verify *wlhyverify.WlhyVerify, wlhyOrg *wlhyorg.WlhyOrg) {
	// 第一步 本地校验
	if tag == "verify" {
		verify.TransportNote("verify")
	}

	// 第二步 上报省监管平台
	if tag == "report" {
		transportNotes := verify.TransportNote("report")
		for associationID, transportNote := range transportNotes {
			if err := wlhyOrg.SendTransportNote(associationID, transportNote); err != nil {
				logger.Stdout.Error(err.Error())
			}
		}
	}
}

func CashFlow(verify *wlhyverify.WlhyVerify, wlhyOrg *wlhyorg.WlhyOrg) {
	// 第一步 本地校验
	if tag == "verify" {
		verify.CashFlow("verify")
	}

	// 第二步 上报省监管平台
	if tag == "report" {
		cashFlows := verify.CashFlow("report")
		for associationID, cashFlow := range cashFlows {
			if err := wlhyOrg.SendCashFlow(associationID, cashFlow); err != nil {
				logger.Stdout.Error(err.Error())
			}
		}
	}
}
