package wlhyorg

import (
	"bytes"
	"encoding/json"
	"io"
	"net/http"
	"strings"
	"time"
	"wlhy/toolbox/logger"

	"github.com/google/uuid"
)

type Driver struct {
	DriverName               string `json:"driverName"`               // 姓名 必填。
	DrivingLicense           string `json:"drivingLicense"`           // 身份证号 必填。
	VehicleClass             string `json:"vehicleClass"`             // 准驾车型 使用总质量4.5吨及以下普通货运车辆从事普通货物运输经营的驾驶员必填，根据机动车驾驶证填写。
	IssuingOrganizations     string `json:"issuingOrganizations"`     // 驾驶证发证机关	使用总质量4.5吨及以下普通货运车辆从事普通货物运输经营的驾驶员必填，根据机动车驾驶证填写。
	ValidPeriodFrom          string `json:"validPeriodFrom"`          // 驾驶证有效期自	使用总质量4.5吨及以下普通货运车辆从事普通货物运输经营的驾驶员必填，根据机动车驾驶证填写。YYYYMMDD
	ValidPeriodTo            string `json:"validPeriodTo"`            // 驾驶证有效期至	使用总质量4.5吨及以下普通货运车辆从事普通货物运输经营的驾驶员必填，根据机动车驾驶证填写。YYYYMMDD
	QualificationCertificate string `json:"qualificationCertificate"` // 从业资格证号 必填，驾驶员从业资格证号，使用总质量4.5吨及以下普通货运车辆从事普通货物运输经营的驾驶员，填写“驾驶员身份证前6位+000000000000”。
	Telephone                string `json:"telephone"`                // 手机号码 必填。
	Remark                   string `json:"remark"`                   // 备注 选填。
}

func (w *WlhyOrg) SendDriver(recordID string, driver Driver) error {
	// 生成随机的对称加密秘钥
	aesKey := uuid.NewString()[0:16]

	// 将报文内容json化
	dispatchJson, err := json.Marshal(driver)
	if err != nil {
		return err
	}
	logger.Stdout.Info("驾驶员信息单原始报文:" + string(dispatchJson))

	// 加密报文表内容
	encryptedCode, err := sm2Encrypt(w.PublicKey, aesKey)
	if err != nil {
		return err
	}
	encryptedContent, err := sm4Encrypt(aesKey, string(dispatchJson))
	if err != nil {
		return err
	}

	driverInfoData := map[string]string{
		"documentName":           "驾驶员信息单",
		"documentVersionNumber":  "1.1",
		"encryptedCode":          string(encryptedCode),
		"encryptedContent":       string(encryptedContent),
		"ipcType":                "WLHY_JSY1001",
		"messageReferenceNumber": strings.ReplaceAll(uuid.NewString(), "-", ""),
		"messageSendingDateTime": time.Now().Format("**************"),
		"senderCode":             w.Account,
		"token":                  w.Token,
		"userName":               w.Account,
	}

	driverInfoJson, err := json.Marshal(driverInfoData)
	if err != nil {
		return err
	}

	resp, err := http.Post(w.ReportUrl, "application/json", bytes.NewBuffer([]byte(driverInfoJson)))
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	content, _ := io.ReadAll(resp.Body)

	if err := success(AssociationDriver, recordID, string(dispatchJson), string(content)); err != nil {
		return err
	}

	return nil
}
