package wlhyorg

import (
	"bytes"
	"encoding/json"
	"io"
	"net/http"
	"strings"
	"time"
	"wlhy/toolbox/logger"

	"github.com/google/uuid"
)

type TransportNote struct {
	OriginalDocumentNumber        string                              `json:"originalDocumentNumber"` // 原始单号 必填，上游企业委托运输单号。
	ShippingNoteNumber            string                              `json:"shippingNoteNumber"`     // 运单号 必填，运单号。
	SerialNumber                  string                              // 分段分单号 必填，分段运输和多车运输由四位数字组成，前两位代表一单多车的序号，后两位代表分段序号。若运输形式为一单一车填0000。
	VehicleAmount                 string                              `json:"vehicleAmount"`                 // 运输总车辆数 必填，同一运单号的货物总共使用的运输车辆总数
	TransportTypeCode             string                              `json:"transportTypeCode"`             // 运输组织类型代码	必填，代码集参见wlhy.mot.gov.cn。
	SendToProDateTime             string                              `json:"sendToProDateTime"`             // 运单上传时间	必填，网络货运经营者上传运单到省级监测系统的时间。YYYYMMDDhhmmss
	Carrier                       string                              `json:"carrier"`                       // 网络货运经营者名称 必填。
	UnifiedSocialCreditIdentifier string                              `json:"unifiedSocialCreditIdentifier"` // 统一社会信用代码	必填。
	PermitNumber                  string                              `json:"permitNumber"`                  // 道路运输经营许可证编号 必填，网络货运经营者的道路运输经营许可证编号。
	ConsignmentDateTime           string                              `json:"consignmentDateTime"`           // 运单生成时间 必填，网络货运经营者信息系统正式成交生成运单的日期时间。YYYYMMDDhhmmss
	BusinessTypeCode              string                              `json:"businessTypeCode"`              // 业务类型代码 必填，代码集参见wlhy.mot.gov.cn。
	DespatchActualDateTime        string                              `json:"despatchActualDateTime"`        // 发货日期时间 必填，本单货物的发货时间 YYYYMMDDhhmmss
	GoodsReceiptDateTime          string                              `json:"goodsReceiptDateTime"`          // 收货日期时间 必填，本单货物的收货时间YYYYMMDDhhmmss
	ConsignorInfo                 []TransportNoteConsignorInfo        `json:"consignorInfo"`                 //	托运人信息
	ConsigneeInfo                 []TransportNoteConsigneeInfo        `json:"consigneeInfo"`                 //	收货方信息
	TotalMonetaryAmount           string                              // 运费金额 必填，托运人与网络货运经营者签订运输合同确定的运费金额，货币单位为人民币（元），保留3位小数，如整数的话，以.000填充。如是一笔业务分几辆车运，需将托运人针对这笔业务付给网络货运经营者的运输费用分摊到每辆车上。
	VehicleInfo                   []TransportNoteVehicleInfo          `json:"vehicleInfo"`          //	车辆信息
	ActualCarrierInfo             []TransportNoteActualCarrierInfo    `json:"actualCarrierInfo"`    // 实际承运人信息
	InsuranceInformation          []TransportNoteInsuranceInformation `json:"insuranceInformation"` // 保险信息 必填。
	Remark                        string                              `json:"remark"`               // 备注	选填。
}

type TransportNoteConsignorInfo struct {
	Consignor              string `json:"consignor"`              // 托运人名称	必填。
	ConsignorID            string `json:"consignorID"`            // 托运人统一社会信用代码或个人证件号	必填。
	PlaceOfLoading         string `json:"placeOfLoading"`         // 装货地址 必填，本单货物的装货的地点。
	CountrySubdivisionCode string `json:"countrySubdivisionCode"` // 装货地点的国家行政区划代码或国别代码 必填，代码集参见wlhy.mot.gov.cn。
}

type TransportNoteConsigneeInfo struct {
	Consignee              string `json:"consignee"`              // 收货方名称 必填。
	ConsigneeID            string `json:"consigneeID"`            // 收货方统一社会信用代码或个人证件号码 选填。
	GoodsReceiptPlace      string `json:"goodsReceiptPlace"`      // 收货地址 必填，本单货物的收货的地点
	CountrySubdivisionCode string `json:"countrySubdivisionCode"` // 收货地点的国家行政区划代码或国别代码 必填，代码集参见wlhy.mot.gov.cn。
}

type TransportNoteVehicleInfo struct {
	VehicleNumber                 string                   `json:"vehicleNumber"`                 // 车辆牌照号 必填。
	VehiclePlateColorCode         string                   `json:"vehiclePlateColorCode"`         // 车牌颜色代码 必填，代码集参见wlhy.mot.gov.cn。
	TrailerVehiclePlateNumber     string                   `json:"trailerVehiclePlateNumber"`     // 挂车牌照号 非必填。如26行中的车辆类型为牵引车，则本字段必填。
	TrailerVehiclePlateColorCode  string                   `json:"trailerVehiclePlateColorCode"`  // 挂车牌照颜色代码 非必填。如26行中的车辆类型为牵引车，则本字段必填, 代码集参见wlhy.mot.gov.cn。
	DespatchActualDateTime        string                   `json:"despatchActualDateTime"`        // 发货日期时间 如果为分段运输必填，本车的发货时间 YYYYMMDDhhmmss
	GoodsReceiptDateTime          string                   `json:"goodsReceiptDateTime"`          // 收货日期时间 如果为分段运输必填，本车的收货时间YYYYMMDDhhmmss
	PlaceOfLoading                string                   `json:"placeOfLoading"`                // 装货地址 如果为分段运输必填，本车的装货的地点。
	LoadingCountrySubdivisionCode string                   `json:"loadingCountrySubdivisionCode"` // 装货地址的国家行政区划代码或国别代码 如果为分段运输必填，代码集参见wlhy.mot.gov.cn。
	GoodsReceiptPlace             string                   `json:"goodsReceiptPlace"`             // 收货地址 如果为分段运输必填，本车拉货终点。
	ReceiptCountrySubdivisionCode string                   `json:"receiptCountrySubdivisionCode"` // 收货地址的国家行政区划代码或国别代码 如果为分段运输必填，代码集参见wlhy.mot.gov.cn。
	Driver                        []TransportNoteDriver    `json:"driver"`                        // 驾驶员 必填，如运输过程中有多个驾驶员，可循环。
	GoodsInfo                     []TransportNoteGoodsInfo `json:"goodsInfo"`                     // 货物信息 如一车货有不同货物，则可循环。
}

type TransportNoteDriver struct {
	DriverName     string `json:"driverName"`     // 姓名 必填。
	DrivingLicense string `json:"drivingLicense"` // 身份证号 必填。
}

type TransportNoteGoodsInfo struct {
	DescriptionOfGoods          string `json:"descriptionOfGoods"`          // 货物名称	必填。
	CargoTypeClassificationCode string `json:"cargoTypeClassificationCode"` // 货物类型分类代码	必填，代码集参见wlhy.mot.gov.cn。
	GoodsItemGrossWeight        string `json:"goodsItemGrossWeight"`        // 货物项毛重 必填，重量单位以KGM千克填写数值，保留3位小数，如整数的话，以.000填充。小数点不计入总长。如是轻泡货等货物，请估算重量。如一笔业务分几辆车运，需报送每辆车实际运输的货物重量。
	Cube                        string `json:"cube"`                        // 体积	选填，体积单位以DMQ立方米填写数值，保留4位小数，如整数的话，以.0000填充。小数点不计入总长。
	TotalNumberOfPackages       string `json:"totalNumberOfPackages"`       // 总件数 选填。
}

type TransportNoteActualCarrierInfo struct {
	ActualCarrierName            string `json:"actualCarrierName"`            // 实际承运人名称 必填，与网络货运经营者签订运输合同，实际完成运输的经营者。取得道路运输经营许可证的个体运输业户，直接填写“许可证上的业户名称”；其他情况填写“运输公司名称（合同签订人姓名）”。
	ActualCarrierBusinessLicense string `json:"actualCarrierBusinessLicense"` // 实际承运人道路运输经营许可证号 必填，实际承运人的道路运输经营许可证编号，网络货运经营者整合车辆全部为总质量4.5吨及以下普通货运车辆的，可填“车籍地6位行政区域代码+000000”。
	ActualCarrierID              string `json:"actualCarrierID"`              // 实际承运人统一社会信用代码或证件号码 必填。
}

type TransportNoteInsuranceInformation struct {
	PolicyNumber         string `json:"policyNumber"`         // 保险单号 必填，未投保的，可填“none”。
	InsuranceCompanyCode string `json:"insuranceCompanyCode"` // 保险公司代码 必填，代码集参见wlhy.mot.gov.cn。未投保的，可填“none”。
}

func (w *WlhyOrg) SendTransportNote(recordID string, transportNote TransportNote) error {
	// 生成随机的对称加密秘钥
	aesKey := uuid.NewString()[0:16]

	// 将报文内容json化
	dispatchJson, err := json.Marshal(transportNote)
	if err != nil {
		return err
	}
	logger.Stdout.Info("电子运单原始报文:" + string(dispatchJson))

	// 加密报文表内容
	encryptedCode, err := sm2Encrypt(w.PublicKey, aesKey)
	if err != nil {
		return err
	}
	encryptedContent, err := sm4Encrypt(aesKey, string(dispatchJson))
	if err != nil {
		return err
	}

	transportNoteInfoData := map[string]string{
		"documentName":           "电子运单",
		"documentVersionNumber":  "1.1",
		"encryptedCode":          string(encryptedCode),
		"encryptedContent":       string(encryptedContent),
		"ipcType":                "WLHY_YD1001",
		"messageReferenceNumber": strings.ReplaceAll(uuid.NewString(), "-", ""),
		"messageSendingDateTime": time.Now().Format("**************"),
		"senderCode":             w.Account,
		"token":                  w.Token,
		"userName":               w.Account,
	}

	transportNoteJson, err := json.Marshal(transportNoteInfoData)
	if err != nil {
		return err
	}

	resp, err := http.Post(w.ReportUrl, "application/json", bytes.NewBuffer([]byte(transportNoteJson)))
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	content, _ := io.ReadAll(resp.Body)

	if err := success(AssociationTransportNote, recordID, string(dispatchJson), string(content)); err != nil {
		return err
	}

	return nil
}
