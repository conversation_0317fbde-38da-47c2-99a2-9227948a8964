package wlhyorg

import (
	"bytes"
	"encoding/json"
	"io"
	"net/http"
	"strings"
	"time"
	"wlhy/toolbox/logger"

	"github.com/google/uuid"
)

type Vehicle struct {
	VehicleNumber                  string `json:"vehicleNumber"`                  // 车辆牌照号	必填，对应运单技术规范第24项。
	VehiclePlateColorCode          string `json:"vehiclePlateColorCode"`          // 车牌颜色代码 必填，对应运单技术规范第23项。
	VehicleType                    string `json:"vehicleType"`                    // 车辆类型代码 必填。参考机动车行驶证填写，代码集参见wlhy.mot.gov.cn。
	Owner                          string `json:"owner"`                          // 所有人 总质量4.5吨及以下普通货运车辆必填，按照机动车行驶证填写。
	UseCharacter                   string `json:"useCharacter"`                   // 使用性质 总质量4.5吨及以下普通货运车辆必填，按照机动车行驶证填写。
	VIN                            string `json:"vin"`                            // 车辆识别代号 总质量4.5吨及以下普通货运车辆可填，按照机动车行驶证填写。
	IssuingOrganizations           string `json:"issuingOrganizations"`           // 发证机关 总质量4.5吨及以下普通货运车辆必填，按照机动车行驶证填写。
	RegisterDate                   string `json:"registerDate"`                   // 注册日期 总质量4.5吨及以下普通货运车辆必填，按照机动车行驶证填写。YYYYMMDD
	IssueDate                      string `json:"issueDate"`                      // 发证日期 总质量4.5吨及以下普通货运车辆必填，按照机动车行驶证填写。YYYYMMDD
	VehicleEnergyType              string `json:"vehicleEnergyType"`              // 车辆能源类型 必填，代码集参见wlhy.mot.gov.cn。
	VehicleTonnage                 string `json:"vehicleTonnage"`                 // 核定载质量 必填，参考机动车行驶证填写，默认单位：吨，保留两位小数，如整数的话，以.00填充。小数点不计入总长。
	GrossMass                      string `json:"grossMass"`                      // 吨位 必填，车辆总质量，默认单位：吨，保留两位小数，如整数的话，以.00填充。小数点不计入总长。
	RoadTransportCertificateNumber string `json:"roadTransportCertificateNumber"` // 道路运输证号 必填，总质量4.5吨及以下普通货运车辆的，可填“车籍地6位行政区域代码+000000”。
	TrailerVehiclePlateNumber      string `json:"trailerVehiclePlateNumber"`      // 挂车牌照号 选填。
	Remark                         string `json:"remark"`                         // 备注 选填。
}

func (w *WlhyOrg) SendVehicle(recordID string, vehicle Vehicle) error {
	// 生成随机的对称加密秘钥
	aesKey := uuid.NewString()[0:16]

	// 将报文内容json化
	dispatchJson, err := json.Marshal(vehicle)
	if err != nil {
		return err
	}
	logger.Stdout.Info("车辆信息单原始报文:" + string(dispatchJson))

	// 加密报文表内容
	encryptedCode, err := sm2Encrypt(w.PublicKey, aesKey)
	if err != nil {
		return err
	}
	encryptedContent, err := sm4Encrypt(aesKey, string(dispatchJson))
	if err != nil {
		return err
	}

	vehicleInfo := map[string]string{
		"documentName":           "车辆信息单",
		"documentVersionNumber":  "1.1",
		"encryptedCode":          string(encryptedCode),
		"encryptedContent":       string(encryptedContent),
		"ipcType":                "WLHY_CL1001",
		"messageReferenceNumber": strings.ReplaceAll(uuid.NewString(), "-", ""),
		"messageSendingDateTime": time.Now().Format("**************"),
		"senderCode":             w.Account,
		"token":                  w.Token,
		"userName":               w.Account,
	}

	vehicleInfoJson, err := json.Marshal(vehicleInfo)
	if err != nil {
		return err
	}

	resp, err := http.Post(w.ReportUrl, "application/json", bytes.NewBuffer([]byte(vehicleInfoJson)))
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	content, _ := io.ReadAll(resp.Body)

	if err := success(AssociationVehicle, recordID, string(dispatchJson), string(content)); err != nil {
		return err
	}

	return nil
}
