package wlhyorg

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"time"
	"wlhy/model"
)

type WlhyOrg struct {
	PublicKey    string
	Token        string
	PublicKeyUrl string
	TokenUrl     string
	ReportUrl    string
	Account      string
	Password     string
}

func (w *WlhyOrg) TokenAndPublicKey() error {
	resp, err := http.Get(w.PublicKeyUrl)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	content, _ := io.ReadAll(resp.Body)

	responseData := make(map[string]any)
	if err := json.Unmarshal(content, &responseData); err != nil {
		return err
	}

	originalPublicKey := responseData["data"].(map[string]any)["publicKey"].(string)
	if originalPublicKey == "" {
		return errors.New("publicKey is empty")
	}

	passwdEncrypt, err := sm2Encrypt(originalPublicKey, w.Password)
	if err != nil {
		return err
	}

	accountBody := map[string]any{
		"account":    w.Account,
		"passwd":     passwdEncrypt,
		"publicRsa":  originalPublicKey,
		"systemCode": "",
	}
	accountBodyJson, _ := json.Marshal(accountBody)

	resp, err = http.Post(w.TokenUrl, "application/json", bytes.NewBuffer(accountBodyJson))
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	content, _ = io.ReadAll(resp.Body)
	responseData = make(map[string]any)
	if err := json.Unmarshal(content, &responseData); err != nil {
		return err
	}
	fmt.Println(string(content))

	if responseData["code"].(string) != "0" {
		return errors.New(responseData["message"].(string))
	}

	w.PublicKey = responseData["data"].(map[string]any)["PUBLIC_KEY"].(string)
	w.Token = responseData["data"].(map[string]any)["token"].(string)
	return nil
}

var wlhyOrgError = map[string]string{
	"2001": "token认证失败 用户错误或token失效，请确认用户并重新获取token",
	"3001": "报文参考号重复 重新生成报文参考号",
	"3002": "ipctype错误 错误的业务接口类型",
	"3003": "发送消息队列失败 请联系管理员",
	"3004": "交换报文解析错误 请确认报文格式是否正确",
	"3005": "字段校验失败 请查看返回的具体错误信息",
	"3006": "对称密钥解密失败 请确认公钥是否失效",
	"3007": "业务报文解密错误 请确对称密钥是否正确",
	"3008": "业务报文解析错误 请确认业务报文格式是否正确",
	"9001": "服务器内部错误 联系管理员",
}

const (
	AssociationDriver        = 1
	AssociationVehicle       = 2
	AssociationTransportNote = 3
	AssociationCashFlow      = 4
)

func success(associationType int, associationID string, request string, response string) error {
	var record model.TmsNetworkUploadInfo
	if err := model.DB.
		Where("association_id = ?", associationID).
		Where("association_type = ?", associationType).
		First(&record).Error; err != nil {
		return err
	}

	responseBody := make(map[string]any)
	if err := json.Unmarshal([]byte(response), &responseBody); err != nil {
		return err
	}
	fmt.Printf("%+v\n", responseBody)

	reportState := 0
	reportError := ""
	if responseBody["code"].(string) == "1001" {
		reportState = 1
	} else {
		reportState = 2
		reportError = wlhyOrgError[responseBody["code"].(string)]
	}

	record.UploadBy = "1732305280388886530"
	record.UploadByName = "13661303600"
	record.UploadByTime = time.Now()
	record.UploadReportState = reportState
	record.UploadReportSendJson = request
	record.UploadReportReturnJson = response
	record.UploadReportMsg = reportError
	record.UpdateBy = "1732305280388886530"
	record.UpdateTime = time.Now()
	if reportState == 1 {
		record.UploadReportMsg = "上报成功"
	} else {
		record.UploadReportMsg = "上报失败 " + reportError
	}
	if err := model.DB.Save(&record).Error; err != nil {
		return err
	}

	switch associationType {
	case 1:
		if err := model.DB.Table("tms_driver").
			Where("driver_id = ?", record.AssociationID).
			Updates(map[string]any{
				"is_report_driver_message": reportState,
				"driver_report_err":        reportError,
				"report_time":              time.Now().Format("2006-01-02 15:04:05"),
			}).Error; err != nil {
			fmt.Println(err)
		}
	case 2:
		if err := model.DB.Table("tms_vehicle").
			Where("id = ?", record.AssociationID).
			Updates(map[string]any{
				"is_report_vehicle_message": reportState,
				"report_time":               time.Now().Format("2006-01-02 15:04:05"),
			}).Error; err != nil {
			fmt.Println(err)
		}
	case 3:
		if err := model.DB.Table("tms_transport_note").
			Where("id = ?", record.AssociationID).
			Updates(map[string]any{
				"is_note_reporting":   reportState,
				"note_reporting_time": time.Now().Format("2006-01-02 15:04:05"),
			}).Error; err != nil {
			fmt.Println(err)
		}
	case 4:
		if err := model.DB.Table("tms_expense_record").
			Where("waybill_id = ?", record.AssociationID).
			Updates(map[string]any{
				"is_capital_reporting":   reportState,
				"capital_reporting_err":  reportError,
				"capital_reporting_time": time.Now().Format("2006-01-02 15:04:05"),
			}).Error; err != nil {
			fmt.Println(err)
		}
	}

	return nil
}
