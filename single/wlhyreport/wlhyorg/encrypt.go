package wlhyorg

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
)

func  sm2Encrypt(publicKey, data string) (string, error) {
	bodyJson, err := json.Marshal(map[string]any{
		"publicKey":         publicKey,
		"encryptionContent": data,
	})
	if err != nil {
		return "", err
	}

	resp, err := http.Post("http://127.0.0.1:8991/encryption/sm2encryption", "application/json", bytes.NewBuffer(bodyJson))
	if err != nil {
		return "", err
	}

	rr := make(map[string]any)
	respBody, _ := io.ReadAll(resp.Body)
	if err := json.Unmarshal(respBody, &rr); err != nil {
		return "", err
	}

	if rr["code"].(string) != "0" {
		return "", errors.New(rr["data"].(string))
	}

	return rr["data"].(string), nil
}

func  sm4Encrypt(key, data string) (string, error) {
	bodyJson, err := json.Marshal(map[string]any{
		"key":               key,
		"encryptionContent": data,
	})
	if err != nil {
		return "", err
	}

	resp, err := http.Post("http://127.0.0.1:8991/encryption/sm4encryption", "application/json", bytes.NewBuffer(bodyJson))
	if err != nil {
		return "", err
	}

	rr := make(map[string]any)
	respBody, _ := io.ReadAll(resp.Body)
	if err := json.Unmarshal(respBody, &rr); err != nil {
		return "", err
	}

	fmt.Println(rr)
	if rr["code"].(string) != "0" {
		return "", errors.New(rr["data"].(string))
	}

	return rr["data"].(string), nil
}