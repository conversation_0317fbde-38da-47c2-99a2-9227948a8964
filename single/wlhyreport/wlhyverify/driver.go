package wlhyverify

import (
	"regexp"
	"strconv"
	"strings"
	"time"
	"wlhy/model"
	"wlhy/single/wlhyreport/wlhyorg"
	"wlhy/toolbox/logger"

	"github.com/samber/lo"
)

func (w *WlhyVerify) Driver(action string) map[string]wlhyorg.Driver {
	sql := ""
	driverT := &driver{}
	if action == "verify" {
		sql = driverT.VerifySQL()
	}
	if action == "report" {
		sql = driverT.ReportSQL()
	}
	if sql == "" {
		return nil
	}

	results := []driver{}
	if err := model.DB.Raw(sql).Scan(&results).Error; err != nil {
		logger.Stdout.Error(err.Error())
		return nil
	}

	// 获取已写入上报记录的司机
	driverIDs := []string{}
	for _, r := range results {
		driverIDs = append(driverIDs, r.DriverID)
	}
	reportRecordsMap := make(map[string]model.TmsNetworkUploadInfo)
	driverIDsChunk := lo.Chunk(driverIDs, 10000)
	for _, chunk := range driverIDsChunk {
		reportRecords := []model.TmsNetworkUploadInfo{}
		if err := model.DB.Model(&model.TmsNetworkUploadInfo{}).
			Where("association_id in ? AND association_type = 1", chunk).
			Find(&reportRecords).Error; err != nil {
			logger.Stdout.Error(err.Error())
			return nil
		}
		for _, r := range reportRecords {
			reportRecordsMap[r.AssociationID] = r
		}
	}

	logger.Stdout.Info("驾驶员信息单总数: " + strconv.Itoa(len(results)))
	re := regexp.MustCompile(`^[一-龥]+$`)
	drivers := make(map[string]wlhyorg.Driver)
	for _, v := range results {
		if v.DriverID == "" {
			continue
		}

		isNotVerified := ""
		if v.DriverName == "" {
			isNotVerified += "姓名为空;"
		}
		if v.DrivingLicense == "" {
			isNotVerified += "身份证号为空;"
		}
		if v.QualificationCertificate == "" {
			isNotVerified += "从业资格证号为空;"
		}
		if len(v.QualificationCertificate) != 18 {
			isNotVerified += "从业资格证号位数不正确;"
		}
		if v.Telephone == "" {
			isNotVerified += "电话号码为空;"
		}
		if v.IssuingOrganizations == "" {
			isNotVerified += "发证机关为空;"
		}
		if !re.MatchString(v.IssuingOrganizations) {
			isNotVerified += "发证机关格式错误;"
		}

		if isNotVerified != "" {
			logger.Stdout.Error("ID: " + v.ID + " " + v.DriverName + " " + isNotVerified)
			if uploadInfo, ok := reportRecordsMap[v.DriverID]; ok {
				uploadInfo.UpdateTime = time.Now()
				uploadInfo.UploadByTime = time.Now()
				uploadInfo.UploadReportState = 0
				uploadInfo.UploadReportCheckState = 2
				uploadInfo.UploadReportCheckMsg = isNotVerified

				if err := model.DB.Save(&uploadInfo).Error; err != nil {
					logger.Stdout.Error(err.Error())
					continue
				}
				if err := model.DB.Table("tms_driver").Where("driver_id = ?", v.DriverID).Updates(map[string]any{
					"is_it_verified": 2,
					"verified_err":   isNotVerified + ",校验失败",
					"verified_time":  time.Now().Format("2006-01-02 15:04:05"),
				}).Error; err != nil {
					logger.Stdout.Error(err.Error())
					continue
				}
			} else {
				if err := model.DB.Create(&model.TmsNetworkUploadInfo{
					ID:                     strconv.Itoa(int(time.Now().UnixNano())),
					CreateBy:               "1732305280388886530",
					CreateTime:             time.Now(),
					UpdateBy:               "1732305280388886530",
					UpdateTime:             time.Now(),
					AssociationID:          v.DriverID,
					AssociationType:        1,
					SysOrgCode:             "A03",
					UploadReportState:      0,
					UploadByTime:           time.Now(),
					UploadReportCheckState: 2,
					UploadReportCheckMsg:   isNotVerified,
				}).Error; err != nil {
					logger.Stdout.Error(err.Error())
					continue
				}
				if err := model.DB.Table("tms_driver").Where("driver_id = ?", v.DriverID).Updates(map[string]any{
					"is_it_verified": 2,
					"verified_err":   isNotVerified + ",校验失败",
					"verified_time":  time.Now().Format("2006-01-02 15:04:05"),
				}).Error; err != nil {
					logger.Stdout.Error(err.Error())
					continue
				}
			}
			continue
		}

		if uploadInfo, ok := reportRecordsMap[v.DriverID]; ok {
			uploadInfo.UpdateTime = time.Now()
			uploadInfo.UploadByTime = time.Now()
			uploadInfo.UploadReportState = 0
			uploadInfo.UploadReportCheckState = 1
			uploadInfo.UploadReportCheckMsg = "校验成功"
			if err := model.DB.Save(&uploadInfo).Error; err != nil {
				logger.Stdout.Error(err.Error())
				continue
			}
			if err := model.DB.Table("tms_driver").Where("driver_id = ?", v.DriverID).Updates(map[string]any{
				"is_it_verified": 1,
				"verified_err":   "校验成功",
				"verified_time":  time.Now().Format("2006-01-02 15:04:05"),
			}).Error; err != nil {
				logger.Stdout.Error(err.Error())
				continue
			}
		} else {
			if err := model.DB.Create(&model.TmsNetworkUploadInfo{
				ID:                     strconv.Itoa(int(time.Now().UnixNano())),
				CreateBy:               "1732305280388886530",
				CreateTime:             time.Now(),
				UpdateBy:               "1732305280388886530",
				UpdateTime:             time.Now(),
				AssociationID:          v.DriverID,
				AssociationType:        1,
				SysOrgCode:             "A03",
				UploadReportState:      0,
				UploadByTime:           time.Now(),
				UploadReportCheckState: 1,
				UploadReportCheckMsg:   "校验成功",
			}).Error; err != nil {
				logger.Stdout.Error(err.Error())
				continue
			}
			if err := model.DB.Table("tms_driver").Where("driver_id = ?", v.DriverID).Updates(map[string]any{
				"is_it_verified": 1,
				"verified_err":   "校验成功",
				"verified_time":  time.Now().Format("2006-01-02 15:04:05"),
			}).Error; err != nil {
				logger.Stdout.Error(err.Error())
				continue
			}
		}

		drivers[v.DriverID] = wlhyorg.Driver{
			DriverName:               v.DriverName,
			DrivingLicense:           v.DrivingLicense,
			VehicleClass:             v.VehicleClass,
			IssuingOrganizations:     strings.ReplaceAll(strings.ReplaceAll(v.IssuingOrganizations, "\n", ""), " ", ""),
			ValidPeriodFrom:          "",
			ValidPeriodTo:            "",
			QualificationCertificate: v.QualificationCertificate,
			Telephone:                v.Telephone,
			Remark:                   "",
		}
	}
	return drivers
}

type driver struct {
	DriverID                 string
	ID                       string
	DriverName               string
	DrivingLicense           string
	IssuingOrganizations     string
	VehicleClass             string
	QualificationCertificate string
	Telephone                string
	ValidPeriodFrom          string
	ValidPeriodTo            string
}

func (d *driver) VerifySQL() string {
	return `
SELECT 
	a.driver_id,
	a.id,
	a.driver_name as driver_name, 
	a.identification_number as driving_license, 
	a.certification_authority as issuing_organizations,
	a.allow_driving_type as vehicle_class, 
	a.identification_number as qualification_certificate,
    a.phone as telephone,
	a.date_of_certification as valid_period_from, 
	a.driver_license_exp_date as valid_period_to
FROM 
	tms_driver a
	LEFT JOIN tms_network_upload_info b ON a.driver_id = b.association_id AND b.association_type = 1
WHERE a.is_delete = 0
	AND a.audit_status = 2
	AND a.attestation_status = 2
	AND (b.upload_report_check_state IS NULL OR b.upload_report_check_state IN (0,2))
ORDER BY a.create_time DESC
	`
}

func (d *driver) ReportSQL() string {
	return `
SELECT 
	a.driver_id,
	a.id,
	a.driver_name as driver_name, 
	a.identification_number as driving_license, 
	a.certification_authority as issuing_organizations,
	a.allow_driving_type as vehicle_class, 
	a.identification_number as qualification_certificate,
    a.phone as telephone,
	a.date_of_certification as valid_period_from, 
	a.driver_license_exp_date as valid_period_to
FROM 
	tms_driver a
	LEFT JOIN tms_network_upload_info b ON a.driver_id = b.association_id AND b.association_type = 1
WHERE a.is_delete = 0
	AND a.audit_status = 2
	AND a.attestation_status = 2
	AND b.upload_report_check_state = 1
	AND b.upload_report_state IN (0,2)
ORDER BY a.create_time DESC
	`
}
