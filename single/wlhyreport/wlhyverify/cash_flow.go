package wlhyverify

import (
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"time"
	"wlhy/model"
	"wlhy/single/wlhyreport/wlhyorg"
	"wlhy/toolbox/logger"

	"github.com/samber/lo"
)

func (w *WlhyVerify) CashFlow(action string) map[string]wlhyorg.CashFlow {
	sql := ""
	cashFlowT := &cashFlow{}
	if action == "verify" {
		sql = cashFlowT.VerifySQL()
	}
	if action == "report" {
		sql = cashFlowT.ReportSQL()
	}
	if sql == "" {
		return nil
	}

	// 获取数据
	results := []cashFlow{}
	if err := model.DB.Raw(sql).Scan(&results).Error; err != nil {
		logger.Stdout.Error(err.Error())
		return nil
	}
	logger.Stdout.Info("资金流水单总数: " + strconv.Itoa(len(results)))

	// 获取已写入上报记录的资金流水单
	cashFlowIDs := []string{}
	for _, r := range results {
		cashFlowIDs = append(cashFlowIDs, r.ID)
	}
	reportRecordsMap := make(map[string]model.TmsNetworkUploadInfo)
	cashFlowIDsChunk := lo.Chunk(cashFlowIDs, 10000)
	for _, chunk := range cashFlowIDsChunk {
		reportRecords := []model.TmsNetworkUploadInfo{}
		if err := model.DB.Model(&model.TmsNetworkUploadInfo{}).
			Where("association_id IN ? AND association_type = 4", chunk).
			Find(&reportRecords).Error; err != nil {
			logger.Stdout.Error(err.Error())
			return nil
		}
		for _, r := range reportRecords {
			reportRecordsMap[r.AssociationID] = r
		}
	}

	plateColors := vehiclePlateColorCode()
	bankCodes := bankCode()

	reg := regexp.MustCompile(`^(([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z](([0-9]{5}[DF])|([DF]([A-HJ-NP-Z0-9])[0-9]{4})))|([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳使领]))$`)

	cashFlows := make(map[string]wlhyorg.CashFlow)
	for _, v := range results {
		isNotVerified := ""
		if v.MonetaryAmount > v.TotalMonetaryAmount {
			isNotVerified += "实际支付金额大于总金额；"
		}
		if !reg.MatchString(v.VehicleNumber) {
			isNotVerified += "车牌号格式不正确:" + v.VehicleNumber + ";"
		}
		if v.Carrier != v.Recipient {
			isNotVerified += "承运人与银行卡开户人不一致:" + v.Carrier + " " + v.Recipient + ";"
		}

		if isNotVerified != "" {
			logger.Stdout.Error("ID: " + v.ID + " " + v.ShippingNoteNumber + " " + isNotVerified)
			if uploadInfo, ok := reportRecordsMap[v.ID]; ok {
				uploadInfo.UpdateTime = time.Now()
				uploadInfo.UploadByTime = time.Now()
				uploadInfo.UploadReportState = 0
				uploadInfo.UploadReportCheckState = 2
				uploadInfo.UploadReportCheckMsg = isNotVerified

				if err := model.DB.Save(&uploadInfo).Error; err != nil {
					logger.Stdout.Error(err.Error())
					continue
				}
			} else {
				if err := model.DB.Create(&model.TmsNetworkUploadInfo{
					ID:                     strconv.Itoa(int(time.Now().UnixNano())),
					CreateBy:               "1732305280388886530",
					CreateTime:             time.Now(),
					UpdateBy:               "1732305280388886530",
					UpdateTime:             time.Now(),
					AssociationID:          v.ID,
					AssociationType:        4,
					SysOrgCode:             "A03",
					UploadReportState:      0,
					UploadByTime:           time.Now(),
					UploadReportCheckState: 2,
					UploadReportCheckMsg:   isNotVerified,
				}).Error; err != nil {
					logger.Stdout.Error(err.Error())
					continue
				}
			}
			continue
		}

		if uploadInfo, ok := reportRecordsMap[v.ID]; ok {
			uploadInfo.UpdateTime = time.Now()
			uploadInfo.UploadByTime = time.Now()
			uploadInfo.UploadReportState = 0
			uploadInfo.UploadReportCheckState = 1
			uploadInfo.UploadReportCheckMsg = "校验成功"
			if err := model.DB.Save(&uploadInfo).Error; err != nil {
				logger.Stdout.Error(err.Error())
				continue
			}
		} else {
			if err := model.DB.Create(&model.TmsNetworkUploadInfo{
				ID:                     strconv.Itoa(int(time.Now().UnixNano())),
				CreateBy:               "1732305280388886530",
				CreateTime:             time.Now(),
				UpdateBy:               "1732305280388886530",
				UpdateTime:             time.Now(),
				AssociationID:          v.ID,
				AssociationType:        4,
				SysOrgCode:             "A03",
				UploadReportState:      0,
				UploadByTime:           time.Now(),
				UploadReportCheckState: 1,
				UploadReportCheckMsg:   "校验成功",
			}).Error; err != nil {
				logger.Stdout.Error(err.Error())
				continue
			}
		}

		if strings.Contains(v.VehicleOwner, "公司") {
			v.Carrier = v.VehicleOwner + "（" + v.Carrier + "）"
		}

		captal := wlhyorg.CashFlow{
			DocumentNumber:        v.DocumentNumber,
			SendToProDateTime:     time.Now().Format("**************"),
			Carrier:               v.Carrier,
			ActualCarrierID:       v.ActualCarrierID,
			VehicleNumber:         v.VehicleNumber,
			VehiclePlateColorCode: plateColors[v.VehiclePlateColorCode],
			ShippingNoteList: []wlhyorg.CashFlowShippingNote{
				{
					ShippingNoteNumber:  v.ShippingNoteNumber,
					SerialNumber:        "0000",
					TotalMonetaryAmount: fmt.Sprintf("%.3f", v.TotalMonetaryAmount),
				},
			},
			Financiallist: []wlhyorg.CashFlowFinancial{
				{
					PaymentMeansCode: "32",
					Recipient:        v.Recipient,
					ReceiptAccount:   v.ReceiptAccount,
					BankCode:         bankCodes[v.BankCode],
					SequenceCode:     strings.Trim(strings.TrimSpace(v.SequenceCode), "\n"),
					MonetaryAmount:   fmt.Sprintf("%.3f", v.MonetaryAmount),
					DateTime:         v.DateTime.Format("**************"),
				},
			},
			Remark: "",
		}

		cashFlows[v.ID] = captal
	}

	return cashFlows
}

type cashFlow struct {
	ID                    string
	DocumentNumber        string
	Carrier               string
	ActualCarrierID       string
	SequenceCode          string
	ShippingNoteNumber    string
	TotalMonetaryAmount   float64
	MonetaryAmount        float64
	DateTime              time.Time
	VehiclePlateColorCode string
	VehicleNumber         string
	Recipient             string
	ReceiptAccount        string
	BankCode              string
	VehicleOwner          string
}

func (c *cashFlow) VerifySQL() string {
	return `
SELECT
    b.id,
	b.id AS document_number,
	c.driver_name AS carrier,
	c.identification_number AS actual_carrier_id,
	a.serial_number AS sequence_code,
    b.transportation_number AS shipping_note_number,
	b.freight_gross_shipper AS total_monetary_amount,
    a.transaction_amount AS monetary_amount,
    a.consumption_time AS date_time,
	e.vehicle_license_color_id AS vehicle_plate_color_code,
	e.vehicle_license_number AS vehicle_number,
    d.account_owner_name AS recipient,
	d.bank_card_number AS receipt_account,
	d.bank_id AS bank_code,
	e.vehicle_owner
FROM 
	tms_transport_note b
    LEFT JOIN tms_expense_record a ON a.waybill_id = b.id AND a.revenue_and_expenditure_types = 0
    LEFT JOIN tms_driver c ON b.payee_id = c.driver_id
    LEFT JOIN tms_driver f ON b.transportation_driver_id = f.driver_id
    LEFT JOIN tms_bank_card d ON b.payee_id = d.bank_card_user_id
    LEFT JOIN tms_vehicle e ON b.transportation_car_id = e.id
	LEFT JOIN tms_network_upload_info g ON b.id = g.association_id AND g.association_type = 4 AND g.sys_org_code = 'A03'
WHERE 
	b.waybill_status in (4,5,8)
	AND b.is_abort_report = 0
	AND b.is_note_reporting = 1
	AND b.total_freight_status = 2
	AND b.sys_org_code = 'A03'
    AND (g.upload_report_check_state IS NULL OR g.upload_report_check_state IN (0,2))
GROUP BY 
	b.id
ORDER BY
	b.create_time ASC
	`
}

func (c *cashFlow) ReportSQL() string {
	return `
SELECT
    b.id,
	b.id AS document_number,
	c.driver_name AS carrier,
	c.identification_number AS actual_carrier_id,
	a.serial_number AS sequence_code,
    b.transportation_number AS shipping_note_number,
	b.freight_gross_shipper AS total_monetary_amount,
    a.transaction_amount AS monetary_amount,
    a.consumption_time AS date_time,
	e.vehicle_license_color_id AS vehicle_plate_color_code,
	e.vehicle_license_number AS vehicle_number,
    d.account_owner_name AS recipient,
	d.bank_card_number AS receipt_account,
	d.bank_id AS bank_code,
	e.vehicle_owner
FROM 
	tms_transport_note b
    LEFT JOIN tms_expense_record a ON a.waybill_id=b.id AND a.revenue_and_expenditure_types = 0
    LEFT JOIN tms_driver c ON b.payee_id = c.driver_id
    LEFT JOIN tms_driver f ON b.transportation_driver_id = f.driver_id
    LEFT JOIN tms_bank_card d ON b.payee_id = d.bank_card_user_id
    LEFT JOIN tms_vehicle e ON b.transportation_car_id = e.id
	LEFT JOIN tms_network_upload_info g ON b.id = g.association_id AND g.association_type = 4 and g.sys_org_code = 'A03'
WHERE 
	b.waybill_status in (4,5,8)
	AND b.is_abort_report = 0
	AND b.is_note_reporting = 1
	AND b.total_freight_status = 2
	AND b.sys_org_code = 'A03'
	AND g.upload_report_check_state = 1
	AND g.upload_report_state IN (0,2)
GROUP BY
	b.id
ORDER BY
	b.create_time ASC
	`
}
