package wlhyverify

import (
	"wlhy/model"
	"wlhy/toolbox/logger"
)

// vehiclePlateColorCode 车辆颜色代码
func vehiclePlateColorCode() map[string]string {
	rows, err := model.DB.Table("sys_dict_item").
		Select([]string{"id", "item_value"}).
		Where("dict_id = ?", "1217986119487107073").
		Rows()
	if err != nil {
		logger.Stdout.Error(err.Error())
		return nil
	}

	colorMap := make(map[string]string)
	for rows.Next() {
		var id string
		var itemValue string
		if err := rows.Scan(&id, &itemValue); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}
		colorMap[id] = itemValue
	}
	return colorMap
}

// vehicleTypeCode 车辆类型代码
func vehicleTypeCode() map[string]string {
	rows, err := model.DB.Table("sys_dict_item").
		Select([]string{"id", "item_value"}).
		Where("dict_id = ?", "1217979431082176514").
		Rows()
	if err != nil {
		logger.Stdout.Error(err.Error())
		return nil
	}

	typeMap := make(map[string]string)
	for rows.Next() {
		var id string
		var itemValue string
		if err := rows.Scan(&id, &itemValue); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}
		typeMap[id] = itemValue
	}
	return typeMap
}

// vehicleEnergyCode 车辆能源代码
func vehicleEnergyCode() map[string]string {
	rows, err := model.DB.Table("sys_dict_item").
		Select([]string{"id", "item_value"}).
		Where("dict_id = ?", "1217982195845738497").
		Rows()
	if err != nil {
		logger.Stdout.Error(err.Error())
		return nil
	}

	vehicleEnergyType := make(map[string]string)
	for rows.Next() {
		var id string
		var itemValue string
		if err := rows.Scan(&id, &itemValue); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}
		vehicleEnergyType[id] = itemValue
	}
	return vehicleEnergyType
}

// vehicleIssuingOrganizationsDict 车辆发证机关代码
func vehicleIssuingOrganizationsDict() map[string]string {
	rows, err := model.DB.Table("sys_dict_item").
		Select([]string{"item_text", "item_value"}).
		Where("dict_id = ?", "1777628420845203458").
		Rows()
	if err != nil {
		logger.Stdout.Error(err.Error())
		return nil
	}

	vehicleIssuingOrganizationsDict := make(map[string]string)
	for rows.Next() {
		var itemText string
		var itemValue string
		if err := rows.Scan(&itemText, &itemValue); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}
		vehicleIssuingOrganizationsDict[itemText] = itemValue
	}
	return vehicleIssuingOrganizationsDict
}

// bankCode 银行代码
func bankCode() map[string]string {
	rows, err := model.DB.Table("sys_dict_item").
		Select([]string{"id", "item_value"}).
		Where("dict_id = ?", "1217987220508684290").
		Rows()
	if err != nil {
		logger.Stdout.Error(err.Error())
		return nil
	}

	bankType := make(map[string]string)
	for rows.Next() {
		var id string
		var itemValue string
		if err := rows.Scan(&id, &itemValue); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}
		bankType[id] = itemValue
	}
	return bankType
}

// goodsTypeCode 货物类型代码
func goodsTypeCode() map[string]string {
	rows, err := model.DB.Table("sys_category").
		Select([]string{"id", "code"}).
		Where("is_delete = ?", 0).
		Rows()

	if err != nil {
		logger.Stdout.Error(err.Error())
		return nil
	}

	goodsTypeMap := make(map[string]string)
	for rows.Next() {
		var id string
		var code string
		if err := rows.Scan(&id, &code); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}
		goodsTypeMap[id] = code
	}
	return goodsTypeMap
}

// vehicleReported 已上报车辆
func vehicleReported() map[string]bool {
	rows, err := model.DB.Table("tms_network_upload_info AS a").
		Joins("JOIN tms_vehicle AS b ON a.association_id = b.id").
		Select([]string{"b.vehicle_license_number"}).
		Where("a.upload_report_state = ? AND a.association_type = ?", 1, 2).
		Rows()

	if err != nil {
		logger.Stdout.Error(err.Error())
		return nil
	}

	vehicleReportedMap := make(map[string]bool)
	for rows.Next() {
		var vehicleLicenseNumber string
		if err := rows.Scan(&vehicleLicenseNumber); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}
		vehicleReportedMap[vehicleLicenseNumber] = true
	}
	return vehicleReportedMap
}

// driverReported 已上报驾驶员
func driverReported() map[string]bool {
	rows, err := model.DB.Table("tms_network_upload_info AS a").
		Joins("JOIN tms_driver AS b ON a.association_id = b.driver_id").
		Select([]string{"b.identification_number"}).
		Where("a.upload_report_state = ? AND a.association_type = ?", 1, 1).
		Rows()

	if err != nil {
		logger.Stdout.Error(err.Error())
		return nil
	}

	driverReportedMap := make(map[string]bool)
	for rows.Next() {
		var identificationNumber string
		if err := rows.Scan(&identificationNumber); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}
		driverReportedMap[identificationNumber] = true
	}
	return driverReportedMap
}
