package wlhyverify

import (
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"time"
	"wlhy/model"
	"wlhy/single/wlhyreport/wlhyorg"
	"wlhy/toolbox/logger"

	"github.com/samber/lo"
)

func (v *WlhyVerify) Vehicle(action string) map[string]wlhyorg.Vehicle {
	sql := ""
	vehicleT := &vehicle{}
	if action == "verify" {
		sql = vehicleT.VerifySQL()
	}
	if action == "report" {
		sql = vehicleT.ReportSQL()
	}
	if sql == "" {
		return nil
	}

	results := []vehicle{}
	if err := model.DB.Raw(sql).Scan(&results).Error; err != nil {
		logger.Stdout.Error(err.Error())
		return nil
	}
	logger.Stdout.Info("车辆信息单总数: " + strconv.Itoa(len(results)))

	// 获取已写入上报记录的车辆
	vehicleIDs := []string{}
	for _, r := range results {
		vehicleIDs = append(vehicleIDs, r.ID)
	}
	reportRecordsMap := make(map[string]model.TmsNetworkUploadInfo)
	vehicleIDsChunk := lo.Chunk(vehicleIDs, 10000)
	for _, chunk := range vehicleIDsChunk {
		reportRecords := []model.TmsNetworkUploadInfo{}
		if err := model.DB.Model(&model.TmsNetworkUploadInfo{}).
			Where("association_id in ? AND association_type = 2", chunk).
			Find(&reportRecords).Error; err != nil {
			logger.Stdout.Error(err.Error())
			return nil
		}
		for _, r := range reportRecords {
			reportRecordsMap[r.AssociationID] = r
		}
	}

	// 获取车型数据
	vehicleTypes := vehicleTypeCode()
	// 获取车辆能源类型数据
	vehicleEnergyTypes := vehicleEnergyCode()
	// 获取车牌颜色数据
	vehiclePlateColorTypes := vehiclePlateColorCode()
	// 获取车辆发证机构数据
	issuingOrganizationsDict := vehicleIssuingOrganizationsDict()

	reg := regexp.MustCompile(`^(([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z](([0-9]{5}[DF])|([DF]([A-HJ-NP-Z0-9])[0-9]{4})))|([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳使领]))$`)

	// 校验并组装数据
	vehicles := make(map[string]wlhyorg.Vehicle)
	for _, r := range results {

		grossMassKg, _ := strconv.ParseFloat(r.GrossMassKg, 64)
		vehicleTonnageKg, _ := strconv.ParseFloat(r.VehicleTonnageKg, 64)
		if grossMassKg <= vehicleTonnageKg {
			carloadWeight, _ := strconv.ParseFloat(r.CarloadWeight, 64)
			grossMassKg = vehicleTonnageKg + carloadWeight
		}

		isNotVerified := ""
		if vehicleTypes[r.VehicleTypeID] != "Q11" {
			if r.Owner == "" {
				isNotVerified += "所有人不能为空;"
			}
			if r.UseCharacter == "" {
				isNotVerified += "使用性质不能为空;"
			}
			if r.VIN == "" {
				isNotVerified += "车辆识别代号不能为空;"
			}
			if r.IssueDate.IsZero() {
				isNotVerified += "发证日期不能为空;"
			}
			if r.IssuingOrganizations == "" {
				vehicleLicenseNumberPrefix := string([]rune(r.VehicleNumber)[:2])
				if _, ok := issuingOrganizationsDict[vehicleLicenseNumberPrefix]; ok {
					r.IssuingOrganizations = issuingOrganizationsDict[vehicleLicenseNumberPrefix]
				}
				if r.IssuingOrganizations == "" {
					isNotVerified += "发证机关不能为空;"
				}
			}
			if r.RegisterDate.IsZero() {
				isNotVerified += "注册日期不能为空;"
			}
		} else {
			r.Owner = ""
			r.UseCharacter = ""
			r.VIN = ""
			r.IssueDate = time.Time{}
			r.IssuingOrganizations = ""
			r.RegisterDate = time.Time{}
		}

		if r.VehicleNumber == "" {
			isNotVerified += "车牌号为空;"
		}
		if !reg.MatchString(r.VehicleNumber) {
			isNotVerified += "车牌号格式不正确:" + r.VehicleNumber + ";"
		}
		if r.VehiclePlateColorCode == "" {
			isNotVerified += "车牌颜色代码为空;"
		}
		if r.VehicleTypeID == "" {
			isNotVerified += "车辆类型代码为空;"
		}
		if r.VehicleEnergyTypeID == "" {
			isNotVerified += "车辆能源类型代码为空;"
		}
		if r.VehicleTonnageKg == "" || vehicleTonnageKg <= 0 {
			isNotVerified += "核定载质量为空;"
		}
		if r.GrossMassKg == "" || grossMassKg <= 0 {
			isNotVerified += "吨位为空;"
		}
		if grossMassKg <= vehicleTonnageKg {
			isNotVerified += "吨位小于等于核定载质量;"
		}

		if r.RoadTransportCertificateNumber == "" || len(r.RoadTransportCertificateNumber) != 12 || r.RoadTransportCertificateNumber == "000000000000" {
			r.RoadTransportCertificateNumber = r.RoadTransportBusinessLicenseNo
		}
		if r.RoadTransportCertificateNumber == "" {
			isNotVerified += "道路运输证号为空;"
		}
		if len(r.RoadTransportCertificateNumber) != 12 {
			isNotVerified += "道路运输证号长度不为12位;"
		}
		if r.RoadTransportCertificateNumber == "000000000000" {
			isNotVerified += "道路运输证号全部为0;"
		}

		if vehicleTypes[r.VehicleTypeID] == "Q11" {
			if r.TrailerVehiclePlateNumber == "" {
				isNotVerified += "挂车牌照号为空;"
			}
			if strings.Contains(r.TrailerVehiclePlateNumber, "超") {
				isNotVerified += "挂车牌照号格式错误(超牌);"
			}
			if !reg.MatchString(r.TrailerVehiclePlateNumber) {
				isNotVerified += "挂车牌照号格式不正确:" + r.TrailerVehiclePlateNumber + ";"
			}
		}
		if grossMassKg > 100000 {
			isNotVerified += "吨位大于100;"
		}
		if vehicleTonnageKg > 100000 {
			isNotVerified += "核定载质量大于100;"
		}

		if isNotVerified != "" {
			logger.Stdout.Error("ID: " + r.ID + " " + r.VehicleNumber + " " + isNotVerified)
			if uploadInfo, ok := reportRecordsMap[r.ID]; ok {
				uploadInfo.UpdateTime = time.Now()
				uploadInfo.UploadByTime = time.Now()
				uploadInfo.UploadReportState = 0
				uploadInfo.UploadReportCheckState = 2
				uploadInfo.UploadReportCheckMsg = isNotVerified

				if err := model.DB.Save(&uploadInfo).Error; err != nil {
					logger.Stdout.Error(err.Error())
					continue
				}
			} else {
				if err := model.DB.Create(&model.TmsNetworkUploadInfo{
					ID:                     strconv.Itoa(int(time.Now().UnixNano())),
					CreateBy:               "1732305280388886530",
					CreateTime:             time.Now(),
					UpdateBy:               "1732305280388886530",
					UpdateTime:             time.Now(),
					AssociationID:          r.ID,
					AssociationType:        2,
					SysOrgCode:             "A03",
					UploadReportState:      0,
					UploadByTime:           time.Now(),
					UploadReportCheckState: 2,
					UploadReportCheckMsg:   isNotVerified,
				}).Error; err != nil {
					logger.Stdout.Error(err.Error())
					continue
				}
			}
			if err := model.DB.Table("tms_vehicle").Where("id = ?", r.ID).Updates(map[string]any{
				"is_it_verified": 2,
				"verified_err":   isNotVerified + ",校验失败",
				"verified_time":  time.Now().Format("2006-01-02 15:04:05"),
			}).Error; err != nil {
				logger.Stdout.Error(err.Error())
				continue
			}
			continue
		}

		if uploadInfo, ok := reportRecordsMap[r.ID]; ok {
			uploadInfo.UpdateTime = time.Now()
			uploadInfo.UploadByTime = time.Now()
			uploadInfo.UploadReportState = 0
			uploadInfo.UploadReportCheckState = 1
			uploadInfo.UploadReportCheckMsg = "校验成功"
			if err := model.DB.Save(&uploadInfo).Error; err != nil {
				logger.Stdout.Error(err.Error())
				continue
			}
		} else {
			if err := model.DB.Create(&model.TmsNetworkUploadInfo{
				ID:                     strconv.Itoa(int(time.Now().UnixNano())),
				CreateBy:               "1732305280388886530",
				CreateTime:             time.Now(),
				UpdateBy:               "1732305280388886530",
				UpdateTime:             time.Now(),
				AssociationID:          r.ID,
				AssociationType:        2,
				SysOrgCode:             "A03",
				UploadReportState:      0,
				UploadByTime:           time.Now(),
				UploadReportCheckState: 1,
				UploadReportCheckMsg:   "校验成功",
			}).Error; err != nil {
				logger.Stdout.Error(err.Error())
				continue
			}
		}
		if err := model.DB.Table("tms_vehicle").Where("id = ?", r.ID).Updates(map[string]any{
			"is_it_verified": 1,
			"verified_err":   "校验成功",
			"verified_time":  time.Now().Format("2006-01-02 15:04:05"),
		}).Error; err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}

		registerDate := ""
		issueDate := ""
		if r.RegisterDate.IsZero() {
			registerDate = ""
		} else {
			registerDate = r.RegisterDate.Format("20060102")
		}

		if r.IssueDate.IsZero() {
			issueDate = ""
		} else {
			issueDate = r.IssueDate.Format("20060102")
		}
		vehicles[r.ID] = wlhyorg.Vehicle{
			VehicleNumber:                  r.VehicleNumber,
			VehiclePlateColorCode:          vehiclePlateColorTypes[r.VehiclePlateColorCode],
			VehicleType:                    vehicleTypes[r.VehicleTypeID],
			Owner:                          r.Owner,
			UseCharacter:                   r.UseCharacter,
			VIN:                            r.VIN,
			IssuingOrganizations:           r.IssuingOrganizations,
			RegisterDate:                   registerDate,
			IssueDate:                      issueDate,
			VehicleEnergyType:              vehicleEnergyTypes[r.VehicleEnergyTypeID],
			VehicleTonnage:                 fmt.Sprintf("%.2f", vehicleTonnageKg/1000),
			GrossMass:                      fmt.Sprintf("%.2f", grossMassKg/1000),
			RoadTransportCertificateNumber: r.RoadTransportCertificateNumber,
			TrailerVehiclePlateNumber:      r.TrailerVehiclePlateNumber,
			Remark:                         "",
		}
	}

	return vehicles
}

type vehicle struct {
	ID                             string
	VehicleNumber                  string
	VehiclePlateColor              string
	VehiclePlateColorCode          string
	VehicleType                    string
	VehicleTypeID                  string
	UseCharacter                   string
	Owner                          string
	VIN                            string
	IssuingOrganizations           string
	IssuingOrganizationsID         string
	RegisterDate                   time.Time
	IssueDate                      time.Time
	VehicleEnergyType              string
	VehicleEnergyTypeID            string
	GrossMassKg                    string
	RoadTransportCertificateNumber string
	TrailerVehiclePlateNumber      string
	VehicleTonnageKg               string
	RoadTransportBusinessLicenseNo string
	CarloadWeight                  string // 整备质量
}

// 总质量 = 整备质量+核定载质量

func (v *vehicle) VerifySQL() string {
	return `
SELECT 
	a.id,
    a.vehicle_license_number as vehicle_number,
	a.vehicle_license_color as vehicle_plate_color,
	a.vehicle_license_color_id as vehicle_plate_color_code,
    a.vehicle_type_name as vehicle_type,
	a.vehicle_type_id as vehicle_type_id,
	a.vehicle_use_nature as use_character,
	a.vehicle_owner as owner,
	a.vehicle_identification_code as vin,
    a.certifying_authority as issuing_organizations,
	a.certification_department_id as issuing_organizations_id,
	a.reg_date as register_date,
	a.certifying_date as issue_date,
    a.vehicle_energy_type as vehicle_energy_type,
	a.vehicle_energy_type_id as vehicle_energy_type_id,
	a.vehicle_total_weight_kg as gross_mass_kg,
    a.transport_permit_number as road_transport_certificate_number,
	b.vehicle_license_number as trailer_vehicle_plate_number,
    ( CASE a.vehicle_type_name
    WHEN '重型半挂牵引车' 
		THEN b.nuclear_load_weight_kg
    ELSE a.nuclear_load_weight_kg
    END) AS vehicle_tonnage_kg,
	a.road_transport_business_license_no,
	a.carload_weight
FROM
    tms_vehicle a
    LEFT JOIN tms_trailer_info AS b ON a.trailer_info_id = b.id
	LEFT JOIN tms_network_upload_info AS c ON a.id = c.association_id AND c.association_type = 2
	JOIN tms_transport_note AS d ON a.id = d.transportation_car_id
WHERE 
	a.is_delete = 0 
	AND a.audit_status = 1
	AND a.is_abort_report = 0
	AND (c.upload_report_check_state IS NULL OR c.upload_report_check_state IN (0,2))
GROUP BY a.id
ORDER BY a.create_time DESC
	`
}

func (v *vehicle) ReportSQL() string {
	return `
SELECT 
	a.id,
    a.vehicle_license_number as vehicle_number,
	a.vehicle_license_color as vehicle_plate_color,
	a.vehicle_license_color_id as vehicle_plate_color_code,
    a.vehicle_type_name as vehicle_type,
	a.vehicle_type_id as vehicle_type_id,
	a.vehicle_use_nature as use_character,
	a.vehicle_owner as owner,
	a.vehicle_identification_code as vin,
    a.certifying_authority as issuing_organizations,
	a.certification_department_id as issuing_organizations_id,
	a.reg_date as register_date,
	a.certifying_date as issue_date,
    a.vehicle_energy_type as vehicle_energy_type,
	a.vehicle_energy_type_id as vehicle_energy_type_id,
	a.vehicle_total_weight_kg as gross_mass_kg,
    a.transport_permit_number as road_transport_certificate_number,
	b.vehicle_license_number as trailer_vehicle_plate_number,
    ( CASE a.vehicle_type_name
    WHEN '重型半挂牵引车' 
		THEN b.nuclear_load_weight_kg
    ELSE a.nuclear_load_weight_kg
    END) AS vehicle_tonnage_kg,
	a.road_transport_business_license_no,
	a.carload_weight
FROM
    tms_vehicle a
    LEFT JOIN tms_trailer_info AS b ON a.trailer_info_id = b.id
	LEFT JOIN tms_network_upload_info AS c ON a.id = c.association_id AND c.association_type = 2
WHERE 
	a.is_delete = 0 
	AND a.audit_status = 1
	AND a.is_abort_report = 0
	AND c.upload_report_check_state = 1
	AND c.upload_report_state IN (0,2)
ORDER BY a.create_time DESC
	`
}
