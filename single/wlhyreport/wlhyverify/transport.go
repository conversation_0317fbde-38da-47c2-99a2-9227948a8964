package wlhyverify

import (
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"time"
	"unicode"
	"wlhy/model"
	"wlhy/single/wlhyreport/wlhyorg"
	"wlhy/toolbox/logger"

	"github.com/samber/lo"
)

func (w *WlhyVerify) TransportNote(action string) map[string]wlhyorg.TransportNote {
	sql := ""
	transportNoteT := &transportNote{}
	if action == "verify" {
		sql = transportNoteT.VerifySQL()
	}
	if action == "report" {
		sql = transportNoteT.ReportSQL()
	}
	if sql == "" {
		return nil
	}

	var results []transportNote
	if err := model.DB.Raw(sql).Scan(&results).Error; err != nil {
		logger.Stdout.Error(err.Error())
		return nil
	}
	logger.Stdout.Info("运单总数: " + strconv.Itoa(len(results)))

	// 获取货物类型数据
	goodsType := goodsTypeCode()

	// 获取车牌颜色数据
	plateColor := vehiclePlateColorCode()

	// 获取已上报的司机和车辆
	reportedDrivers := driverReported()
	reportedVehicles := vehicleReported()

	// 获取已写入上报记录的运单
	transportNoteIDs := []string{}
	for _, r := range results {
		transportNoteIDs = append(transportNoteIDs, r.ID)
	}
	reportRecordsMap := make(map[string]model.TmsNetworkUploadInfo)
	transportNoteIDsChunk := lo.Chunk(transportNoteIDs, 10000)
	for _, chunk := range transportNoteIDsChunk {
		reportRecords := []model.TmsNetworkUploadInfo{}
		if err := model.DB.Model(&model.TmsNetworkUploadInfo{}).
			Where("association_id in ? AND association_type = 3", chunk).
			Find(&reportRecords).Error; err != nil {
			logger.Stdout.Error(err.Error())
			return nil
		}
		for _, r := range reportRecords {
			reportRecordsMap[r.AssociationID] = r
		}
	}

	reg := regexp.MustCompile(`^(([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z](([0-9]{5}[DF])|([DF]([A-HJ-NP-Z0-9])[0-9]{4})))|([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳使领]))$`)

	// 校验并组装数据
	fail := 0
	transportNotes := make(map[string]wlhyorg.TransportNote)
	re := regexp.MustCompile(`^[一-龥a-zA-Z0-9\-\(\)（）]+$`)
	for _, v := range results {

		// 组装装货地址
		if strings.Contains(v.LoadingAddress, v.LoadingName) {
			v.LoadingAddress = strings.ReplaceAll(v.LoadingAddress, v.LoadingName, "")
		}
		placeOfLoading := v.LoadingName + v.LoadingAddress

		// 组装卸货地址
		if strings.Contains(v.UnloadAddress, v.UnloadName) {
			v.UnloadAddress = strings.ReplaceAll(v.UnloadAddress, v.UnloadName, "")
		}
		goodsReceiptPlace := v.UnloadName + v.UnloadAddress

		if v.TrailerVehiclePlateColorCode == "" {
			v.TrailerVehiclePlateColorCode = "2"
		}

		isNotVerified := ""
		if action == "report" {
			if _, ok := reportedDrivers[v.DrivingLicense]; !ok {
				isNotVerified += "司机信息未上报;"
			}
			if _, ok := reportedVehicles[v.VehicleNumber]; !ok {
				isNotVerified += "车辆信息未上报;"
			}
		}

		// 校验实际承运人相关信息
		if v.ActualCarrierID == "" || len(v.ActualCarrierID) != 18 {
			isNotVerified += "实际承运人统一社会信用代码或证件号码不符合格式;"
		}
		if v.ActualCarrierBusinessLicense == "" || v.ActualCarrierBusinessLicense == "000000000000" {
			isNotVerified += "实际承运人道路运输经营许可证号为空或全部为0;"
		}
		if len(v.ActualCarrierBusinessLicense) != 12 {
			isNotVerified += "实际承运人道路运输经营许可证号不足12位;"
		}
		if len(v.ActualCarrierBusinessLicense) == 12 {
			for _, v := range v.ActualCarrierBusinessLicense {
				if !unicode.IsDigit(v) {
					isNotVerified += "实际承运人道路运输经营许可证号不符合格式;"
					break
				}
			}
		}
		if v.ActualCarrierName == "" {
			isNotVerified += "实际承运人名称为空;"
		}

		// 校验发货人相关信息
		if v.ConsignorID == "" || len(v.ConsignorID) != 18 {
			isNotVerified += "发货人统一社会信用代码或证件号码不符合格式;"
		}
		if v.Consignor == "" {
			isNotVerified += "发货人名称为空;"
		}

		if placeOfLoading == "" {
			isNotVerified += "发货人地址为空;"
		}
		if !re.MatchString(placeOfLoading) {
			isNotVerified += "发货人地址不符合格式;"
		}
		if v.LoadCountrySubdivisionCode == "" {
			isNotVerified += "发货人地址代码为空;"
		}

		// 校验收货人相关信息
		if goodsReceiptPlace == "" {
			isNotVerified += "收货人地址为空;"
		}
		if !re.MatchString(goodsReceiptPlace) {
			isNotVerified += "收货人地址不符合格式;"
		}
		if v.Consignee == "" {
			isNotVerified += "收货人名称为空;"
		}
		if v.UnloadCountrySubdivisionCode == "" {
			isNotVerified += "收货人地址代码为空;"
		}

		if v.TotalMonetaryAmount == 0 {
			isNotVerified += "总金额为空;"
		}
		if v.TotalMonetaryAmount < 100 {
			isNotVerified += "总金额过低，低于100元;"
		}

		if v.VehicleNumber == "" {
			isNotVerified += "车牌号为空;"
		}
		if !reg.MatchString(v.VehicleNumber) {
			isNotVerified += "车牌号格式不正确:" + v.VehicleNumber + ";"
		}
		if v.VehiclePlateColorCode == "" {
			isNotVerified += "车牌颜色代码为空;"
		}

		if v.VehicleTypeName == "重型半挂牵引车" {
			if v.TrailerVehiclePlateNumber == "" {
				isNotVerified += "挂车牌照号为空;"
			}
			if v.TrailerVehiclePlateColorCode == "" {
				isNotVerified += "挂车牌照颜色代码为空;"
			}
			if strings.Contains(v.TrailerVehiclePlateNumber, "超") {
				isNotVerified += "挂车牌照号格式错误(超牌);"
			}
			if !reg.MatchString(v.TrailerVehiclePlateNumber) {
				isNotVerified += "挂车牌照号格式不正确:" + v.TrailerVehiclePlateNumber + ";"
			}
		}

		if v.DriverName == "" {
			isNotVerified += "司机姓名为空;"
		}
		if v.DrivingLicense == "" {
			isNotVerified += "驾驶证号为空;"
		}
		if v.DescriptionOfGoods == "" {
			isNotVerified += "货物描述为空;"
		}
		if v.CargoTypeClassificationCode == "" {
			isNotVerified += "货物类型代码为空;"
		}

		//  判断车辆类型，修改核定载
		if v.VehicleTypeName == "重型半挂牵引车" {
			v.NuclearLoadWeightKg = v.TrailerNuclearLoadWeightKg
		}

		// 校验车辆核定载质量
		if v.VehicleTypeName == "重型半挂牵引车" {
			if v.NuclearLoadWeightKg < 100 || v.NuclearLoadWeightKg > 200000 {
				isNotVerified += "挂车核定载质量错误;"
			}
		} else {
			if v.NuclearLoadWeightKg < 100 || v.NuclearLoadWeightKg > 200000 {
				isNotVerified += "核定载质量错误;"
			}
		}

		// 校验货物重量
		if v.NuclearLoadWeightKg/1000 > 0 && v.GoodsItemGrossWeight > v.NuclearLoadWeightKg/1000 {
			v.GoodsItemGrossWeight = v.NuclearLoadWeightKg / 1000
		}
		if v.GoodsItemGrossWeight*1000 < 100 || v.GoodsItemGrossWeight*1000 > 200000 {
			isNotVerified += "货物重量错误;"
		}

		if isNotVerified != "" {
			logger.Stdout.Error("ID: " + v.ID + " " + v.ShippingNoteNumber + " " + isNotVerified)

			if uploadInfo, ok := reportRecordsMap[v.ID]; ok {
				uploadInfo.UpdateTime = time.Now()
				uploadInfo.UploadByTime = time.Now()
				uploadInfo.UploadReportState = 0
				uploadInfo.UploadReportCheckState = 2
				uploadInfo.UploadReportCheckMsg = isNotVerified

				if err := model.DB.Save(&uploadInfo).Error; err != nil {
					logger.Stdout.Error(err.Error())
					continue
				}
			} else {
				if err := model.DB.Create(&model.TmsNetworkUploadInfo{
					ID:                     strconv.Itoa(int(time.Now().UnixNano())),
					CreateBy:               "1732305280388886530",
					CreateTime:             time.Now(),
					UpdateBy:               "1732305280388886530",
					UpdateTime:             time.Now(),
					AssociationID:          v.ID,
					AssociationType:        3,
					SysOrgCode:             "A03",
					UploadReportState:      0,
					UploadByTime:           time.Now(),
					UploadReportCheckState: 2,
					UploadReportCheckMsg:   isNotVerified,
				}).Error; err != nil {
					logger.Stdout.Error(err.Error())
					continue
				}
			}
			if err := model.DB.Table("tms_transport_note").Where("id = ?", v.ID).Updates(map[string]any{
				"is_note_reporting":  0,
				"is_verify_status":   2,
				"verify_status_err":  isNotVerified + ",校验失败",
				"verify_status_time": time.Now().Format("2006-01-02 15:04:05"),
			}).Error; err != nil {
				logger.Stdout.Error(err.Error())
				continue
			}

			fail++
			continue
		}

		if uploadInfo, ok := reportRecordsMap[v.ID]; ok {
			uploadInfo.UpdateTime = time.Now()
			uploadInfo.UploadByTime = time.Now()
			uploadInfo.UploadReportState = 0
			uploadInfo.UploadReportCheckState = 1
			uploadInfo.UploadReportCheckMsg = "校验成功"
			if err := model.DB.Save(&uploadInfo).Error; err != nil {
				logger.Stdout.Error(err.Error())
				continue
			}
		} else {
			if err := model.DB.Create(&model.TmsNetworkUploadInfo{
				ID:                     strconv.Itoa(int(time.Now().UnixNano())),
				CreateBy:               "1732305280388886530",
				CreateTime:             time.Now(),
				UpdateBy:               "1732305280388886530",
				UpdateTime:             time.Now(),
				AssociationID:          v.ID,
				AssociationType:        3,
				SysOrgCode:             "A03",
				UploadReportState:      0,
				UploadByTime:           time.Now(),
				UploadReportCheckState: 1,
				UploadReportCheckMsg:   "校验成功",
			}).Error; err != nil {
				logger.Stdout.Error(err.Error())
				continue
			}
		}
		if err := model.DB.Table("tms_transport_note").Where("id = ?", v.ID).Updates(map[string]any{
			"is_note_reporting":  0,
			"is_verify_status":   1,
			"verify_status_err":  "校验成功",
			"verify_status_time": time.Now().Format("2006-01-02 15:04:05"),
		}).Error; err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}

		if strings.Contains(v.VehicleOwner, "公司") {
			v.ActualCarrierName = v.VehicleOwner + "（" + v.ActualCarrierName + "）"
		}
		transportNote := wlhyorg.TransportNote{
			OriginalDocumentNumber:        v.OriginalDocumentNumber,
			ShippingNoteNumber:            v.ShippingNoteNumber,
			SerialNumber:                  "0000",
			VehicleAmount:                 "1",
			TransportTypeCode:             "1",
			SendToProDateTime:             time.Now().Format("20060102150405"),
			Carrier:                       "赤峰现代智慧物流有限公司",
			UnifiedSocialCreditIdentifier: "91150402MACRQHHW1K",
			PermitNumber:                  "150402605841",
			ConsignmentDateTime:           v.ConsignmentDateTime.Format("20060102150405"),
			BusinessTypeCode:              "1002996",
			DespatchActualDateTime:        v.DespatchActualDateTime.Format("20060102150405"),
			GoodsReceiptDateTime:          v.GoodsReceiptDateTime.Format("20060102150405"),
			ConsignorInfo: []wlhyorg.TransportNoteConsignorInfo{
				{
					Consignor:              v.Consignor,
					ConsignorID:            v.ConsignorID,
					PlaceOfLoading:         placeOfLoading,
					CountrySubdivisionCode: v.LoadCountrySubdivisionCode,
				},
			},
			ConsigneeInfo: []wlhyorg.TransportNoteConsigneeInfo{
				{
					Consignee:              v.Consignee,
					GoodsReceiptPlace:      goodsReceiptPlace,
					CountrySubdivisionCode: v.UnloadCountrySubdivisionCode,
				},
			},
			TotalMonetaryAmount: fmt.Sprintf("%.3f", v.TotalMonetaryAmount),
			VehicleInfo: []wlhyorg.TransportNoteVehicleInfo{
				{
					VehicleNumber:                v.VehicleNumber,
					VehiclePlateColorCode:        plateColor[v.VehiclePlateColorCode],
					TrailerVehiclePlateNumber:    v.TrailerVehiclePlateNumber,
					TrailerVehiclePlateColorCode: plateColor[v.TrailerVehiclePlateColorCode],
					Driver: []wlhyorg.TransportNoteDriver{
						{
							DriverName:     v.DriverName,
							DrivingLicense: v.DrivingLicense,
						},
					},
					GoodsInfo: []wlhyorg.TransportNoteGoodsInfo{
						{
							DescriptionOfGoods:          v.DescriptionOfGoods,
							CargoTypeClassificationCode: goodsType[v.CargoTypeClassificationCode],
							GoodsItemGrossWeight:        fmt.Sprintf("%.3f", v.GoodsItemGrossWeight*1000),
						},
					},
				},
			},
			ActualCarrierInfo: []wlhyorg.TransportNoteActualCarrierInfo{
				{
					ActualCarrierName:            v.ActualCarrierName,
					ActualCarrierBusinessLicense: v.ActualCarrierBusinessLicense,
					ActualCarrierID:              v.ActualCarrierID,
				},
			},
			InsuranceInformation: []wlhyorg.TransportNoteInsuranceInformation{
				{
					PolicyNumber:         "none",
					InsuranceCompanyCode: "none",
				},
			},
			Remark: "",
		}

		transportNotes[v.ID] = transportNote
	}

	return transportNotes
}

type transportNote struct {
	OriginalDocumentNumber       string
	ID                           string
	ShippingNoteNumber           string
	SendToProDateTime            string
	ConsignmentDateTime          time.Time
	DespatchActualDateTime       time.Time
	GoodsReceiptDateTime         time.Time
	Consignor                    string
	ConsignorID                  string
	LoadingName                  string
	LoadingAddress               string
	LoadCountrySubdivisionCode   string
	Consignee                    string
	UnloadName                   string
	UnloadAddress                string
	UnloadCountrySubdivisionCode string
	TotalMonetaryAmount          float64
	VehicleNumber                string
	DriverName                   string
	DrivingLicense               string
	DescriptionOfGoods           string
	GoodsItemGrossWeight         float64
	VehiclePlateColorCode        string
	CargoTypeClassificationCode  string
	ActualCarrierID              string
	ActualCarrierName            string
	ActualCarrierBusinessLicense string
	NuclearLoadWeightKg          float64
	TrailerVehiclePlateNumber    string
	TrailerVehiclePlateColorCode string
	TrailerNuclearLoadWeightKg   float64
	VehicleTypeName              string
	VehicleOwner                 string
}

func (t *transportNote) VerifySQL() string {
	return `
SELECT
	b.goods_number AS original_document_number,
	a.id,
	a.transportation_number AS shipping_note_number,
	a.note_reporting_time AS send_to_pro_date_time,
	a.create_time AS consignment_date_time,
	a.loading_time AS despatch_actual_date_time,
	a.unload_time AS goods_receipt_date_time,
	( CASE WHEN c.shipper_type = 0 THEN c.shipper_name ELSE ( SELECT shipper_name FROM tms_shipper WHERE is_delete = 0 AND shipper_id = c.company_id ) END ) AS consignor,
	( CASE WHEN c.shipper_type = 0 THEN c.identification_number ELSE ( SELECT social_credit_code FROM tms_shipper WHERE is_delete = 0 AND shipper_id = c.company_id ) END ) AS consignor_id,
	b.loading_name,
	b.loading_address,
	b.loading_area_id AS load_country_subdivision_code,
	b.discharger_name AS consignee,
	b.unload_name,
	b.unload_address,
	b.unload_area_id AS unload_country_subdivision_code,
	a.freight_gross_shipper AS total_monetary_amount,
	a.transportation_plate AS vehicle_number,
	d.driver_name AS driver_name,
	d.identification_number AS driving_license,
	IF ( length( trim( b.goods_name ))> 0, b.goods_name, b.goods_type ) AS description_of_goods,
	a.loading_number AS goods_item_gross_weight,
	f.vehicle_license_color_id AS vehicle_plate_color_code,
	b.goods_type_id AS cargo_type_classification_code,
	g.identification_number AS actual_carrier_id,
	g.driver_name AS actual_carrier_name,
	CASE	
		WHEN f.road_transport_business_license_no = "" THEN
			f.transport_permit_number ELSE f.road_transport_business_license_no 
	END AS actual_carrier_business_license,
	( CASE f.vehicle_type_name
    WHEN '重型半挂牵引车' 
		THEN t.nuclear_load_weight_kg
    ELSE f.nuclear_load_weight_kg
    END) AS nuclear_load_weight_kg,
	t.vehicle_license_number AS trailer_vehicle_plate_number,
	t.vehicle_license_color_id AS trailer_vehicle_plate_color_code,
	t.nuclear_load_weight_kg AS trailer_nuclear_load_weight_kg,
	f.vehicle_type_name AS vehicle_type_name,
	f.vehicle_owner AS vehicle_owner
FROM
	tms_transport_note a
	LEFT JOIN tms_order_history b ON a.order_id = b.id AND a.order_version_no = b.version_no
	LEFT JOIN tms_shipper c ON a.create_by = c.shipper_id
	LEFT JOIN tms_driver d ON a.transportation_driver_id = d.driver_id
	LEFT JOIN tms_driver g ON a.payee_id = g.driver_id
	LEFT JOIN tms_vehicle f ON a.transportation_car_id = f.id
	LEFT JOIN tms_trailer_info t ON f.trailer_info_id = t.id 
	LEFT JOIN tms_network_upload_info n ON a.id = n.association_id AND n.association_type = 3
WHERE
	a.waybill_status in (4,5,8)
	AND a.is_abort_report = 0
	AND a.sys_org_code = 'A03'
	AND (n.upload_report_check_state IS NULL OR n.upload_report_check_state IN (0,2))
ORDER BY a.create_time ASC
	`
}

func (t *transportNote) ReportSQL() string {
	return `
SELECT
	b.goods_number AS original_document_number,
	a.id,
	a.transportation_number AS shipping_note_number,
	a.note_reporting_time AS send_to_pro_date_time,
	a.create_time AS consignment_date_time,
	a.loading_time AS despatch_actual_date_time,
	a.unload_time AS goods_receipt_date_time,
	( CASE WHEN c.shipper_type = 0 THEN c.shipper_name ELSE ( SELECT shipper_name FROM tms_shipper WHERE is_delete = 0 AND shipper_id = c.company_id ) END ) AS consignor,
	( CASE WHEN c.shipper_type = 0 THEN c.identification_number ELSE ( SELECT social_credit_code FROM tms_shipper WHERE is_delete = 0 AND shipper_id = c.company_id ) END ) AS consignor_id,
	b.loading_name,
	b.loading_address,
	b.loading_area_id AS load_country_subdivision_code,
	b.discharger_name AS consignee,
	b.unload_name,
	b.unload_address,
	b.unload_area_id AS unload_country_subdivision_code,
	a.freight_gross_shipper AS total_monetary_amount,
	a.transportation_plate AS vehicle_number,
	d.driver_name AS driver_name,
	d.identification_number AS driving_license,
	IF ( length( trim( b.goods_name ))> 0, b.goods_name, b.goods_type ) AS description_of_goods,
	a.loading_number AS goods_item_gross_weight,
	f.vehicle_license_color_id AS vehicle_plate_color_code,
	b.goods_type_id AS cargo_type_classification_code,
	g.identification_number AS actual_carrier_id,
	g.driver_name AS actual_carrier_name,
	CASE	
		WHEN f.road_transport_business_license_no = "" THEN
			f.transport_permit_number ELSE f.road_transport_business_license_no 
	END AS actual_carrier_business_license,
	( CASE f.vehicle_type_name
    WHEN '重型半挂牵引车' 
		THEN t.nuclear_load_weight_kg
    ELSE f.nuclear_load_weight_kg
    END) AS nuclear_load_weight_kg,
	t.vehicle_license_number AS trailer_vehicle_plate_number,
	t.vehicle_license_color_id AS trailer_vehicle_plate_color_code,
	t.nuclear_load_weight_kg AS trailer_nuclear_load_weight_kg,
	f.vehicle_type_name AS vehicle_type_name,
	f.vehicle_owner AS vehicle_owner
FROM
	tms_transport_note a
	LEFT JOIN tms_order_history b ON a.order_id = b.id AND a.order_version_no = b.version_no
	LEFT JOIN tms_shipper c ON a.create_by = c.shipper_id
	LEFT JOIN tms_driver d ON a.transportation_driver_id = d.driver_id
	LEFT JOIN tms_driver g ON a.payee_id = g.driver_id
	LEFT JOIN tms_vehicle f ON a.transportation_car_id = f.id
	LEFT JOIN tms_trailer_info t ON f.trailer_info_id = t.id 
	JOIN tms_network_upload_info n ON a.id = n.association_id AND n.association_type = 3
WHERE
	a.waybill_status in (4,5,8)
	AND a.is_abort_report = 0
	AND a.sys_org_code = 'A03'
	AND n.upload_report_check_state = 1
	AND n.upload_report_state IN (0,2)
ORDER BY a.create_time ASC
	`
}
