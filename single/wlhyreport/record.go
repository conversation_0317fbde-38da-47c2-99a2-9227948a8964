package main

import (
	"encoding/json"
	"fmt"
	"time"
	"wlhy/model"
	"wlhy/toolbox/logger"

	"github.com/samber/lo"
)

func verifyRecords(startTime, endTime time.Time) {
	var infos []model.TmsNetworkUploadInfo
	if err := model.DB.Model(&model.TmsNetworkUploadInfo{}).
		Where("update_time BETWEEN ? AND ?", startTime, endTime).
		Find(&infos).Error; err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	// 获取信息单数据
	driverIDs := []string{}
	vehicleIDs := []string{}
	transportNoteIDs := []string{}
	for _, v := range infos {
		if v.AssociationType == 1 {
			driverIDs = append(driverIDs, v.AssociationID)
		}
		if v.AssociationType == 2 {
			vehicleIDs = append(vehicleIDs, v.AssociationID)
		}
		if v.AssociationType == 3 {
			transportNoteIDs = append(transportNoteIDs, v.AssociationID)
		}
	}

	// 获取司机、车辆、运单数据
	drivers := make(map[string]map[string]string)
	driverIDsChunk := lo.Chunk(driverIDs, 10000)
	for _, chunk := range driverIDsChunk {
		rows, _ := model.DB.Table("tms_driver").
			Select([]string{"driver_id", "driver_name", "phone"}).
			Where("driver_id IN (?)", chunk).
			Rows()
		for rows.Next() {
			var driverID string
			var driverName string
			var phone string
			if err := rows.Scan(&driverID, &driverName, &phone); err != nil {
				logger.Stdout.Error(err.Error())
				continue
			}
			drivers[driverID] = map[string]string{"driverName": driverName, "phone": phone}
		}
	}

	vehicles := make(map[string]map[string]string)
	vehicleIDsChunk := lo.Chunk(vehicleIDs, 10000)
	for _, chunk := range vehicleIDsChunk {
		rows, _ := model.DB.Table("tms_vehicle").
			Select([]string{"id", "vehicle_license_number"}).
			Where("id IN (?)", chunk).
			Rows()
		for rows.Next() {
			var id string
			var vehicleLicenseNumber string
			if err := rows.Scan(&id, &vehicleLicenseNumber); err != nil {
				logger.Stdout.Error(err.Error())
				continue
			}
			vehicles[id] = map[string]string{"vehicleLicenseNumber": vehicleLicenseNumber}
		}
	}

	transportNotes := make(map[string]map[string]string)

	transportNoteIDsChunk := lo.Chunk(transportNoteIDs, 10000)
	for _, chunk := range transportNoteIDsChunk {
		rows, _ := model.DB.Table("tms_transport_note").
			Select([]string{"id", "transportation_number", "transportation_plate", "transportation_driver", "transportation_phone"}).
			Where("id IN (?)", chunk).
			Rows()
		for rows.Next() {
			var id string
			var transportationNumber string
			var transportationPlate string
			var transporatationDriver string
			var transportationPhone string
			if err := rows.Scan(&id, &transportationNumber, &transportationPlate, &transporatationDriver, &transportationPhone); err != nil {
				logger.Stdout.Error(err.Error())
				continue
			}
			transportNotes[id] = map[string]string{"transportationNumber": transportationNumber, "transportationPlate": transportationPlate, "transportationDriver": transporatationDriver, "transportationPhone": transportationPhone}
		}
	}

	var savedRecord model.TmsNetworkUploadInfoRecords
	model.DB.Model(&model.TmsNetworkUploadInfoRecords{}).
		Where("created_at BETWEEN ? AND ?", startTime.Format("2006-01-02")+" 00:00:00", endTime.Format("2006-01-02")+" 23:59:59").
		Order("id desc").
		First(&savedRecord)
	batch := 1
	if savedRecord.ID > 0 {
		batch = savedRecord.Batch + 1
	}

	records := []model.TmsNetworkUploadInfoRecords{}
	now := time.Now()
	for _, info := range infos {
		category := 0
		result := []byte{}
		switch info.AssociationType {
		case 1:
			category = 1
			result, _ = json.Marshal(drivers[info.AssociationID])
		case 2:
			category = 2
			result, _ = json.Marshal(vehicles[info.AssociationID])
		case 3:
			category = 3
			result, _ = json.Marshal(transportNotes[info.AssociationID])
		case 4:
			category = 4
			result, _ = json.Marshal(info)
		}

		records = append(records, model.TmsNetworkUploadInfoRecords{
			ID:            0,
			Category:      category,
			AssociationID: info.AssociationID,
			Result:        string(result),
			CreatedAt:     now,
			UpdatedAt:     now,
			Batch:         batch,
		})
	}

	// 拆分数据，每批1000条
	for i := 0; i < len(records); i += 1000 {
		end := i + 1000
		if end > len(records) {
			end = len(records)
		}
		recordsBatch := records[i:end]
		if err := model.DB.Model(&model.TmsNetworkUploadInfoRecords{}).Create(&recordsBatch).Error; err != nil {
			logger.Stdout.Error(err.Error())
		}
	}

	fmt.Println("记录生成成功")
}
