package zjxl

import "fmt"

func RouterPath(token, vclN, qryBtm, qryEtm string) (any, error) {
	url := "https://zhiyunopenapi.95155.com/save/apis/routerPath"

	// 创建 form-data
	formData := map[string]string{
		"token":  token,
		"cid":    cid,
		"srt":    srt,
		"vclN":   vclN,
		"vco":    "2",
		"qryBtm": qryBtm,
		"qryEtm": qryEtm,
	}

	// 处理参数
	processParam(formData)

	// 将参数转换为字符串
	reqBody := convertMapToString(formData)
	fmt.Println(reqBody)

	// 发起请求
	result, err := httpsCall(url, reqBody)
	if err != nil {
		fmt.Println("Error:", err)
		return "", err
	}
	return result, nil
}
