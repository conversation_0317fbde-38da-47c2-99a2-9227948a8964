package zjxl

import (
	"fmt"
	"os"
)

func Token() (string, error) {
	url := "https://openapi.sinoiov.cn/save/apis/login/"

	// 创建 form-data
	formData := map[string]string{
		"user": user,
		"pwd":  pwd,
		"cid":  cid,
		"srt":  srt,
	}

	// ProcessParam processes the parameters and computes a signature.
	processParam(formData)

	// Convert form-data to string
	reqBody := convertMapToString(formData)

	// httpsCall performs an HTTPS call.
	result, err := httpsCall(url, reqBody)
	if err != nil {
		fmt.Println("Error:", err)
		return "", err
	}

	// 写入token到本地文件
	if _, err := os.Stat("token.txt"); os.IsNotExist(err) {
		os.Create("token.txt")
	}
	os.WriteFile("token.txt", []byte(result.(string)), 0644)

	return result.(string), nil
}

func LocalToken() string {
	tokenBytes, err := os.ReadFile("token.txt")
	if err != nil {
		fmt.Println(err)
	}
	return string(tokenBytes)
}
