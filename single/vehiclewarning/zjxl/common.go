package zjxl

import (
	"bytes"
	"crypto/hmac"
	"crypto/sha1"
	"crypto/tls"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"sort"
	"strings"
)

var user = "90c77528-3f8d-42ee-aa3a-24ce6efba5f0"
var pwd = "J814215X0FLq4h986P1Q0475D9395U"
var srt = "96180e9d-2444-4b2e-8755-cb0a07035efe"
var cid = "040689cb-138d-461b-a5bf-161d0b1dedd4"

/*
官网最新白皮书下载连接：http://open.sinoiov.cn/document/dataOpenApi-V9.0.pdf
SDK下载连接：http://open.sinoiov.cn/service/js/openapi-sdk-6.0.jar
正式环境域名地址：https://zhiyunopenapi.95155.com
*/

type Resp struct {
	Status int `json:"status"`
	Result any `json:"result"`
}

// httpsCall performs an HTTPS call.
func httpsCall(url string, reqBody string) (any, error) {
	// Create a new request
	req, err := http.NewRequest("POST", url, bytes.NewBufferString(reqBody))
	if err != nil {
		fmt.Println("Error creating request:", err)
		return "", err
	}

	// Set headers
	req.Header.Set("Content-Length", fmt.Sprint(len(reqBody)))
	req.Header.Set("charset", "UTF-8")
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8")

	// Disable SSL verification (only for testing, not for production)
	tr := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client := &http.Client{Transport: tr}

	// Send the request
	resp, err := client.Do(req)
	if err != nil {
		fmt.Println("Error performing request:", err)
		return "", err
	}
	defer resp.Body.Close()

	// Read the response
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Println("Error reading response:", err)
		return "", err
	}

	result := Resp{}
	if err := json.Unmarshal(responseBody, &result); err != nil {
		return "", err
	}
	fmt.Println(result)
	if result.Status != 1001 {
		return "", errors.New(fmt.Sprintf("Error code: %d", result.Status))
	}
	return result.Result, nil
}

// processParam processes the parameters and computes a signature.
func processParam(param map[string]string) error {
	// Check if the "srt" key exists in the map
	srt, ok := param["srt"]
	if !ok {
		return errors.New("missing 'srt' parameter")
	}

	// Remove the "srt" key from the map
	delete(param, "srt")

	// Create a list of combined key-value strings
	var paramValueList []string
	for key, value := range param {
		paramValueList = append(paramValueList, key+value)
	}

	// Sort the list
	sort.Strings(paramValueList)

	// Concatenate sorted values into a single string
	concatenated := []string{}
	concatenated = append(concatenated, paramValueList...)

	// Compute HMAC-SHA1 signature
	signature, err := hmacSha1(concatenated, []byte(srt))
	if err != nil {
		return err
	}

	// Encode signature to hex string
	sign := encodeHexStr(signature)

	// Add the signature to the map
	param["sign"] = sign

	return nil
}

// hmacSha1 calculates HMAC-SHA1.
func hmacSha1(data []string, key []byte) ([]byte, error) {
	// Create a new HMAC using SHA-1 and the provided key
	mac := hmac.New(sha1.New, key)

	// Write each data element to the HMAC
	for _, str := range data {
		_, err := mac.Write([]byte(str))
		if err != nil {
			return nil, err
		}
	}

	// Calculate the HMAC and return the result
	return mac.Sum(nil), nil
}

// encodeHexStr encodes a byte array to a hexadecimal string.
func encodeHexStr(data []byte) string {
	return strings.ToUpper(hex.EncodeToString(data))
}

// convertMapToString converts a map to a string
func convertMapToString(param map[string]string) string {
	var sb strings.Builder
	if len(param) > 0 {
		for key, value := range param {
			sb.WriteString(key)
			sb.WriteString("=")
			sb.WriteString(value)
			sb.WriteString("&")
		}
		// Remove the trailing '&' by slicing the string
		if sb.Len() > 0 {
			str := sb.String()
			return str[:len(str)-1]
		}
	}
	return ""
}
