package zjxl

import (
	"fmt"
)

func SubscribeWarning(token, vclNs, qryBtm, qryEtm string) (any, error) {
	url := "https://zhiyunopenapi.95155.com/save/apis/rTimeWarnReg"

	// 创建 form-data
	formData := map[string]string{
		"token":  token,
		"cid":    cid,
		"srt":    srt,
		"vclNs":  vclNs,
		"qryBtm": qryBtm,
		"qryEtm": qryEtm,
		"type":   "A",
	}

	// 处理参数
	processParam(formData)

	// 将参数转换为字符串
	reqBody := convertMapToString(formData)
	fmt.Println(reqBody)

	// 发起请求
	result, err := httpsCall(url, reqBody)
	if err != nil {
		fmt.Println("Error:", err)
		return "", err
	}
	return result, nil
}

func UnsubscribeWarning(token, vclN string) (any, error) {
	url := "https://zhiyunopenapi.95155.com/save/apis/rTimeWarnCancel"

	// 创建 form-data
	formData := map[string]string{
		"token": token,
		"cid":   cid,
		"srt":   srt,
		"vclN":  vclN,
	}

	// 处理参数
	processParam(formData)

	// 将参数转换为字符串
	reqBody := convertMapToString(formData)
	fmt.Println(reqBody)

	// 发起请求
	result, err := httpsCall(url, reqBody)
	if err != nil {
		fmt.Println("Error:", err)
		return "", err
	}
	return result, nil
}
