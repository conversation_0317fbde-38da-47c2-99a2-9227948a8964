package main

import (
	"database/sql"
	"fmt"
	"time"

	"wlhy/model"
	"wlhy/single/vehiclewarning/zjxl"
	"wlhy/toolbox/logger"

	"github.com/robfig/cron/v3"
)

func tasks() {
	c := cron.New(cron.WithLogger(cron.DefaultLogger), cron.WithSeconds())

	c.AddFunc("0 0 10 * * *", subscribe)

	c.AddFunc("0 0 23 * * *", unsubscribe)

	c.Start()
}

func subscribe() {
	logger.Stdout.Info("开始订阅")
	token := zjxl.LocalToken()

	max := 10

	rows, err := model.DB.Table("tms_transport_note AS a").
		Joins("JOIN tms_vehicle AS b ON a.transportation_plate = b.vehicle_license_number").
		Joins("JOIN tms_order_history AS c ON a.order_id = c.id AND a.order_version_no = c.version_no").
		Select([]string{"a.transportation_plate", "a.loading_time", "c.haul_distance"}).
		Where("a.waybill_status = ?", 2).
		Where("a.loading_number > ?", 0).
		Where("a.is_vehicle_subscribe = ?", 0).
		Where("a.sys_org_code = ?", "A03").
		Where("(a.unload_number = ? OR a.unload_number IS NULL)", 0).
		Where("b.vehicle_license_color = ?", "黄色").
		Order("a.loading_time DESC").
		Rows()
	if err != nil {
		fmt.Println(err)
	}

	subscribeNum := 0
	for rows.Next() {
		var transportationPlate string
		var loadingTime time.Time
		var haulDistance sql.NullFloat64

		if err := rows.Scan(&transportationPlate, &loadingTime, &haulDistance); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}

		if !haulDistance.Valid || haulDistance.Float64 < 100 {
			continue
		}

		t := time.Now().Add(1 * time.Minute)

		cc, err := zjxl.SubscribeWarning(token, transportationPlate+"_2",
			t.Format("2006-01-02 15:04:05"),
			t.Add(6*time.Hour).Format("2006-01-02 15:04:05"))
		if err != nil {
			logger.Stdout.Error(fmt.Sprintf("%s 订阅失败, %s\n", transportationPlate, err.Error()))
			continue
		}
		logger.Stdout.Info(fmt.Sprintf("%s 订阅成功\n", transportationPlate))
		logger.Stdout.Info(fmt.Sprintf("%s 订阅结果: %+v\n", transportationPlate, cc))
		subscribeNum++
		logger.Stdout.Info(fmt.Sprintf("已订阅 %d 辆车\n", subscribeNum))
		if subscribeNum == max {
			break
		}
	}
	logger.Stdout.Info("订阅完毕\n")
}

func unsubscribe() {
	logger.Stdout.Info("开始退订")
	token := zjxl.LocalToken()

	rows, err := model.DB.Table("tms_transport_note").
		Select([]string{"id", "transportation_plate", "loading_time"}).
		Where("is_vehicle_subscribe = ?", 1).
		Rows()
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	for rows.Next() {
		var id string
		var transportationPlate string
		var loadingTime time.Time

		if err := rows.Scan(&id, &transportationPlate, &loadingTime); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}

		cc, err := zjxl.UnsubscribeWarning(token, transportationPlate)

		if err != nil {
			logger.Stdout.Error(fmt.Sprintf("%s 退订失败, %s\n", transportationPlate, err.Error()))
			continue
		}
		logger.Stdout.Info(fmt.Sprintf("%s 退订成功\n", transportationPlate))
		logger.Stdout.Info(fmt.Sprintf("%s 退订结果: %+v\n", transportationPlate, cc))

		if err := model.DB.Table("tms_transport_note").
			Where("id = ?", id).
			Updates(map[string]any{
				"is_vehicle_subscribe": 0,
			}).Error; err != nil {
			logger.Stdout.Error(err.Error())
		}
	}
	logger.Stdout.Info("退订完毕")
}
