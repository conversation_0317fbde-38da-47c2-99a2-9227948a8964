package main

import (
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"strconv"
	"strings"
	"time"
	"wlhy/model"
	"wlhy/toolbox/logger"

	"maps"

	"github.com/gin-gonic/gin"
)

func web() {
	r := gin.Default()

	r.POST("/areamsg", func(c *gin.Context) {
		// 解析表单参数
		if err := c.Request.ParseForm(); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Error parsing form"})
			return
		}

		// 获取所有表单参数
		formParams := c.Request.Form

		// 输出所有参数
		params := make(map[string][]string)
		maps.Copy(params, formParams)

		jsonData, _ := json.Marshal(params)
		if err := os.WriteFile(time.Now().Format("20060102150405")+".json", jsonData, 0644); err != nil {
			logger.Stdout.Error(fmt.Sprintf("写入文件失败: %s", err.Error()))
		}
		logger.Stdout.Info(fmt.Sprintf("%s\n", string(jsonData)))

		// 判断是订阅结果推送结果还是订阅数据推送
		if _, ok := params["impStatus"]; ok {
			// 订阅结果推送
			vehicleLicenseNumber := params["vno"][0]
			if params["impStatus"][0] == "1" {
				// 订阅成功
				if err := model.DB.Table("tms_transport_note").
					Where("transportation_plate = ?", vehicleLicenseNumber).
					Where("waybill_status = ?", 2).
					Updates(map[string]any{"is_vehicle_subscribe": 1}).Error; err != nil {
					logger.Stdout.Error(fmt.Sprintf("更新订阅状态失败: %s", err.Error()))
				}
			} else {
				// 订阅失败
				if err := model.DB.Table("tms_transport_note").
					Where("transportation_plate = ?", vehicleLicenseNumber).
					Where("waybill_status = ?", 2).
					Updates(map[string]any{"is_vehicle_subscribe": 2}).Error; err != nil {
					logger.Stdout.Error(fmt.Sprintf("更新订阅状态失败: %s", err.Error()))
				}
			}
		} else {
			// 订阅数据推送
			// 查询司机driver_id
			var driverID string
			model.DB.Table("tms_transport_note").
				Select([]string{"transportation_driver_id"}).
				Where("transportation_plate = ?", params["vno"][0]).
				Order("create_time desc").
				Limit(1).
				Scan(&driverID)

			var sendTime time.Time
			t, err := strconv.ParseInt(params["sendTime"][0], 10, 64)
			if err != nil {
				logger.Stdout.Error(fmt.Sprintf("解析时间失败: %s", err.Error()))
				sendTime = time.Now()
			} else {
				sendTime = time.UnixMilli(t)
			}

			insertData := map[string]any{
				"id":               fmt.Sprintf("%d", time.Now().UnixNano()),
				"create_by":        driverID,
				"create_time":      time.Now().Format("2006-01-02 15:04:05"),
				"sys_org_code":     "A03",
				"is_delete":        "0",
				"vno":              params["vno"][0],
				"vco":              params["vco"][0],
				"one_type":         params["oneType"][0],
				"two_type":         params["twoType"][0],
				"three_type":       params["threeType"][0],
				"send_date_time":   sendTime.Format("2006-01-02 15:04:05"),
				"longitude":        strings.Split(params["addr"][0], ",")[0],
				"latitude":         strings.Split(params["addr"][0], ",")[1],
				"msg":              params["msg"][0],
				"danger_longitude": strings.Split(params["addr"][0], ",")[0],
				"danger_latitude":  strings.Split(params["addr"][0], ",")[1],
				"vin":              params["vin"][0],
			}

			if err := model.DB.Table("tms_vehicle_warning").Create(insertData).Error; err != nil {
				logger.Stdout.Error(fmt.Sprintf("写入告警数据失败: %s", err.Error()))
			}
		}

		c.String(http.StatusOK, "success")
	})

	r.Run(":10081")
}
