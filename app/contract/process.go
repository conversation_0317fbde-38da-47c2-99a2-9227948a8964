package contract

import (
	"database/sql"
	"fmt"
	"os"
	"path/filepath"
	"wlhy/model"
	"wlhy/toolbox"
	"wlhy/toolbox/logger"

	"github.com/xuri/excelize/v2"
)

func Process(excelFileName string) {
	// 读取excel
	f, err := excelize.OpenFile(excelFileName)
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	rows, err := f.GetRows("Sheet1")
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	// 提取手机号和车牌号
	phones := []string{}
	dd := []map[string]string{}
	for key, row := range rows {
		if key == 0 {
			continue
		}
		if len(row) < 8 {
			continue
		}

		phone := row[3]
		if phone == "" {
			continue
		}
		phones = append(phones, phone)

		dd = append(dd, map[string]string{
			"vehicle": row[8],
			"phone":   phone,
		})
	}

	driverRows, err := model.DB.Table("tms_driver").
		Select([]string{"driver_id", "phone"}).
		Where("phone IN (?)", phones).
		Where("is_delete = ?", 0).
		Rows()
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	for driverRows.Next() {
		var driverID string
		var phone string
		if err := driverRows.Scan(&driverID, &phone); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}

		for i, v := range dd {
			if v["phone"] == phone {
				dd[i]["driver"] = driverID
			}
		}
	}

	for _, v := range dd {
		downloadInvoiceDataForProcess(v["driver"], v["vehicle"], "datas/", "A03")
	}

	fmt.Println(len(dd))
	fmt.Println("Done")
}

func downloadInvoiceDataForProcess(driverID, plate, dirName, sysOrgCode string) {
	// 检查目录是否存在
	_, err := os.Stat(dirName)
	if err != nil {
		if os.IsExist(err) {
			return
		}
		if os.IsNotExist(err) {
			os.Mkdir(dirName, 0777)
		}
	}

	// 创建目录
	saveDir := filepath.Join(dirName, plate)
	_, err = os.Stat(saveDir)
	if err != nil && os.IsNotExist(err) {
		os.Mkdir(saveDir, 0777)
	}

	var identificationFrontImgUrl sql.NullString
	var driverLicenseImgUrl sql.NullString
	var workCertificateImgUrl sql.NullString
	var drivingPermitImgUrl sql.NullString
	var driverLicenseFrontImgUrl sql.NullString
	var transportPermitImgUrl sql.NullString
	var transportPermitAdditionalImgUrl sql.NullString
	var contractUrl sql.NullString

	// 获取车辆信息
	vehicleRows, err := model.DB.Table("tms_vehicle").
		Select([]string{
			"driving_permit_img_url",
			"driving_front_img_url",
			"transport_permit_img_url",
			"transport_permit_additional_img_url",
		}).
		Where("vehicle_license_number = ?", plate).
		Where("is_delete = ?", 0).
		Limit(1).
		Rows()
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	for vehicleRows.Next() {
		if err := vehicleRows.Scan(
			&drivingPermitImgUrl,
			&driverLicenseFrontImgUrl,
			&transportPermitImgUrl,
			&transportPermitAdditionalImgUrl,
		); err != nil {
			logger.Stdout.Error(err.Error())
			return
		}
	}
	//蒙D41430-1744883863061790722-赤峰市元宝山区-赤峰市红山区-merge
	// 获取驾驶员信息
	driverRows, err := model.DB.Table("tms_driver AS a").
		Joins("LEFT JOIN tms_user_agree AS b ON a.driver_id = b.user_id").
		Select([]string{
			"a.identification_front_img_url",
			"a.driver_license_img_url",
			"a.work_certificate_img_url",
			"b.contract_url",
		}).
		Where("a.driver_id = ?", driverID).
		Where("a.is_delete = ?", 0).
		Where("b.is_delete = ?", 0).
		Where("b.sys_org_code IS NULL OR b.sys_org_code = '' OR b.sys_org_code = ?", sysOrgCode).
		Limit(1).
		Rows()
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	for driverRows.Next() {
		if err := driverRows.Scan(
			&identificationFrontImgUrl,
			&driverLicenseImgUrl,
			&workCertificateImgUrl,
			&contractUrl,
		); err != nil {
			logger.Stdout.Error(err.Error())
			return
		}
	}

	// 下载文件
	if identificationFrontImgUrl.Valid && identificationFrontImgUrl.String != "" {
		toolbox.DownloadFile(filepath.Join(saveDir, "1"+filepath.Ext(identificationFrontImgUrl.String)), identificationFrontImgUrl.String)
	}
	if driverLicenseImgUrl.Valid && driverLicenseImgUrl.String != "" {
		toolbox.DownloadFile(filepath.Join(saveDir, "2"+filepath.Ext(driverLicenseImgUrl.String)), driverLicenseImgUrl.String)
	}
	if workCertificateImgUrl.Valid && workCertificateImgUrl.String != "" {
		toolbox.DownloadFile(filepath.Join(saveDir, "3"+filepath.Ext(workCertificateImgUrl.String)), workCertificateImgUrl.String)
	}
	if drivingPermitImgUrl.Valid && drivingPermitImgUrl.String != "" {
		toolbox.DownloadFile(filepath.Join(saveDir, "4"+filepath.Ext(drivingPermitImgUrl.String)), drivingPermitImgUrl.String)
	}
	if driverLicenseFrontImgUrl.Valid && driverLicenseFrontImgUrl.String != "" {
		toolbox.DownloadFile(filepath.Join(saveDir, "5"+filepath.Ext(driverLicenseFrontImgUrl.String)), driverLicenseFrontImgUrl.String)
	}
	if transportPermitImgUrl.Valid && transportPermitImgUrl.String != "" {
		toolbox.DownloadFile(filepath.Join(saveDir, "6"+filepath.Ext(transportPermitImgUrl.String)), transportPermitImgUrl.String)
	}

	if transportPermitAdditionalImgUrl.Valid && transportPermitAdditionalImgUrl.String != "" {
		toolbox.DownloadFile(filepath.Join(saveDir, "7"+filepath.Ext(transportPermitAdditionalImgUrl.String)), transportPermitAdditionalImgUrl.String)
	}
	if contractUrl.Valid && contractUrl.String != "" {
		toolbox.DownloadFile(filepath.Join(saveDir, "8"+filepath.Ext(contractUrl.String)), contractUrl.String)
	}

	logger.Stdout.Info(fmt.Sprintf("Download %s - %s Done\n", driverID, plate))
}
