<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <style>
        @font-face {
            font-family: customFont;
            /*本地字体文件路径*/
            src: url('https://www.cfhszy.com/images/DroidSansFallback.ttf');
        }

        * {
            margin: 0;
            padding: 0;
            font-family: "customFont";
        }

        body {
            width: 210mm;
            margin: 0;
            padding: 0;
        }

        .page {
            width: 210mm;
            height: 297mm;
            margin: 0 0 1px 0;
            padding: 0;
        }

        .table {
            width: 750px;
            font-size: 12px;
            border-collapse: collapse;
            position: relative;
            top: 30px;
            left: 20px;
        }

        .td1 {
            width: 160px;
            text-align: center;
        }

        .td2 {
            width: 100px;
            text-align: center;
        }

        .td3 {
            text-align: center;
        }

        .td4 {
            text-align: center;
        }

        .td5 {
            width: 80px;
            text-align: center;
        }
    </style>
</head>

<body>
    {{range $key,$list := .AppendixDataChunk}}
    <div class="page">
        <table class="table" border="1">
            <tr>
                <td class="" style="text-align: center;">运单号</td>
                <td class="" style="text-align: center;">货物名称</td>
                <td class="" style="text-align: center;">发货地址</td>
                <td class="" style="text-align: center;">卸货地址</td>
                <td class="" style="text-align: center;">金额</td>
            </tr>

            {{range $list}}
                <tr>
                    <td class="td1">{{.TransportationNumber}}</td>
                    <td class="td2">{{.GoodsName}}</td>
                    <td class="td3">{{.ShippingAddress}}</td>
                    <td class="td4">{{.ReceivingAddress}}</td>
                    <td class="td5">{{.FreightPaid}}元</td>
                </tr>
                {{if .IsLast}}
                <tr>
                    <td class="td1">运费合计</td>
                    <td class="td2"></td>
                    <td class="td3"></td>
                    <td class="td4"></td>
                    <td class="td5">{{$.TotalFreightAmount}}元</td>
                </tr>
                {{end}}
            {{end}}
        </table>
    </div>
    {{end}}
</body>

</html>