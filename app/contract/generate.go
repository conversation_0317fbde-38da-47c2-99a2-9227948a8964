package contract

import (
	"bytes"
	"crypto/md5"
	"database/sql"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"math/rand"
	"mime/multipart"
	"net/http"
	"net/url"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"sync"
	"text/template"
	"time"
	"wlhy/model"
	"wlhy/thirdparty/oss"
	"wlhy/toolbox"
	"wlhy/toolbox/logger"

	"github.com/samber/lo"
	"github.com/xuri/excelize/v2"
)

// AppendixT 运单附件数据结构题
type AppendixT struct {
	TransportationNumber string  // 运单号
	GoodsName            string  // 货物名称
	ShippingAddress      string  // 发货人地址
	ReceivingAddress     string  // 收货人地址
	FreightPaid          float64 // 运费
	IsLast               bool    // 是否最后一条
}

// BatchSign 批量合同结构体
type BatchSign struct {
	LineKey                    string      // 线路key
	TotalFreightAmount         float64     // 总运费
	TransportationDriver       string      // 驾驶员
	TransportationIdentityCard string      // 驾驶员说身份证号
	TransportationPhone        string      // 驾驶员手机号
	TransportationPlate        string      // 车牌号
	Appendix                   []AppendixT // 运单详情附件数据
	AppendixPDF                string      // 运单详情附件路径
	Body                       url.Values  // 君子签签订合同的表单数据
	AplCode                    string      // 君子签合同号
	AplErr                     error       // 君子签错误信息
	ContractCode               string      // 合同号
}

// var batchHtmlTemplatePath = "/home/<USER>/codes/wlhy/app/contract/*.html"

var batchHtmlTemplatePath = "/Users/<USER>/codes/own/wlhy/app/contract/*.html"

// GenerateBatch 生成批量合同
//
// 参数:
//   - startTime 开始时间，格式：2020-01-01 00:00:00
//   - endTime 结束时间，格式：2020-01-01 00:00:00
//   - sysOrgCode 系统组织编码；A03:现代智慧，A07:元宝山
//   - isRetry 是否重试
func GenerateBatch(startTime, endTime, sysOrgCode string, isRetry bool) {
	logName := "app/contract/log-" + sysOrgCode + ".txt"
	r, err := os.OpenFile(logName, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0755)
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	defer r.Close()

	record := make(map[string]any)
	cc, err := os.ReadFile(logName)
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	for _, line := range strings.Split(string(cc), "\n") {
		if line == "" {
			continue
		}
		record[line] = struct{}{}
	}

	startT := time.Now()
	j := &JunZiQian{
		AppKey:      "5163dbe48257703c",
		AppSecret:   "a801378f5163dbe48257703c1b40ad68",
		ServicesUrl: "https://api.junziqian.com",
	}

	// 获取固定时间的运单数据
	rows, err := model.DB.Table("tms_transport_note").
		Select([]string{"transportation_driver_id", "transportation_plate", "transportation_driver"}).
		Where("finished_time BETWEEN ? AND ?", startTime, endTime).
		Where("waybill_status IN (?)", []int{4, 5, 8}).
		Where("is_delete = ?", 0).
		Where("sys_org_code = ?", sysOrgCode).
		Rows()
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	// 提取司机ID
	drivers := make(map[string][]string)
	for rows.Next() {
		var transportationDriverID string
		var transportationPlate string
		var transportationDriver string
		if err := rows.Scan(&transportationDriverID, &transportationPlate, &transportationDriver); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}

		key := fmt.Sprintf("%s|%s", transportationDriverID, transportationDriver)
		drivers[key] = append(drivers[key], transportationPlate)
	}

	// 执行目录
	rootDirName := "datas/"

	// 生成批量合同
	wg := sync.WaitGroup{}
	mu := sync.Mutex{}
	limit := make(chan bool, 20)
	retry := 5
	tmpl := template.Must(template.ParseGlob(batchHtmlTemplatePath))
	for key, val := range drivers {
		limit <- true
		wg.Add(1)
		go func(key string, val []string) {
			defer func() {
				wg.Done()
				<-limit
			}()
			t := strings.Split(key, "|")
			driverID := t[0]
			driverName := t[1]
			plates := lo.Uniq(val)

			dirName := filepath.Join(rootDirName, driverName+"_"+strings.Join(plates, "_"))

			for _, plate := range plates {
				if _, ok := record[plate+"-"+driverID]; ok {
					logger.Stdout.Info(fmt.Sprintf("%s - %s批量合同已生成，跳过本次执行:\n", plate, driverID))
					continue
				}

				if isRetry {
					isReturn := true
					checkSigns := j.BatchContractBodyCheck(plate, driverID, startTime, endTime, dirName, sysOrgCode, tmpl)
					for _, s := range checkSigns {
						mergeFilename := filepath.Join(dirName, fmt.Sprintf("%s-%s-%s-merge.pdf", plate, driverID, s.LineKey))
						// 判断文件不存在，如果不存在修改isReturn的值
						if _, err := os.Stat(mergeFilename); os.IsNotExist(err) {
							isReturn = false
							break
						}
					}
					if isReturn {
						logger.Stdout.Info(fmt.Sprintf("%s - %s批量合同已生成，跳过本次执行:\n", plate, driverID))
						return
					}
				}

				// 下载开票材料
				downloadInvoiceData(driverID, plates, dirName, sysOrgCode)

				// 签订批量合同
				signs := j.ApplySignBatch(plate, driverID, startTime, endTime, dirName, sysOrgCode, tmpl)
				if len(signs) == 0 {
					return
				}
				for _, s := range signs {
					if s.AplErr != nil {
						logger.Stdout.Error(s.AplErr.Error())
						continue
					}

					time.Sleep(time.Second * 3)
					downloadUrl := ""
					for i := 0; i < retry; i++ {
						downloadUrl, err = j.DownloadUrl(s.AplCode)
						if err != nil {
							logger.Stdout.Error(err.Error())
							time.Sleep(time.Second * 3)
							continue
						} else {
							break
						}
					}

					filename := filepath.Join(dirName, fmt.Sprintf("%s%s%s.pdf", plate, driverID, s.LineKey))
					if err := toolbox.DownloadFile(filename, downloadUrl); err != nil {
						logger.Stdout.Error(err.Error())
						continue
					}

					// 使用pdfcpu合并合同和附件
					mergeFilename := filepath.Join(dirName, fmt.Sprintf("%s-%s-%s-merge.pdf", plate, driverID, s.LineKey))
					cmd := exec.Command("pdfcpu", "merge", mergeFilename, filename, s.AppendixPDF)
					if err := cmd.Run(); err != nil {
						logger.Stdout.Info(fmt.Sprintf("Merge PDF Error: %v\n", err))
						continue
					}
					os.Remove(filename)
					os.Remove(s.AppendixPDF)

					// 检查文件是否存在，若存在则删除
					// if _, err := os.Stat(mergeFilename); err == nil {
					// os.Remove(mergeFilename)
					// }

					// 移动合并后的合同文件
					if err := os.Rename(mergeFilename, filepath.Join(dirName, filepath.Base(mergeFilename))); err != nil {
						logger.Stdout.Error(err.Error())
						continue
					}

					logger.Stdout.Info(fmt.Sprintf("%s 合同生成成功：%s\n", time.Now().Format("2006-01-02 15:04:05"), mergeFilename))
				}

				mu.Lock()
				r.WriteString(plate + "-" + driverID + "\n")
				mu.Unlock()
			}

		}(key, val)
	}
	wg.Wait()

	logger.Stdout.Info(fmt.Sprintf("生成批量合同完成，耗时：%s\n", time.Since(startT).String()))

	// 生成统计excel
	type ExcelData struct {
		TransportationDriver               string
		TransportationIdentificationNumber string
		OcrAddress                         string
		TransportationPhone                string
		LoadingAddress                     string
		UnloadAddress                      string
		GoodsName                          string
		VehicleTypeName                    string
		TransportationPlate                string
		ContractCode                       string
		FreightPaid                        float64
		TractionQuality                    float64
	}

	f := excelize.NewFile()
	defer func() {
		if err := f.Close(); err != nil {
			logger.Stdout.Error(err.Error())
		}
	}()

	i, _ := f.NewSheet("Sheet1")
	f.SetActiveSheet(i)

	// 设置表头
	f.SetCellValue("Sheet1", "A1", "姓名")
	f.SetCellValue("Sheet1", "B1", "身份证号")
	f.SetCellValue("Sheet1", "C1", "身份证地址")
	f.SetCellValue("Sheet1", "D1", "电话")
	f.SetCellValue("Sheet1", "E1", "起运地")
	f.SetCellValue("Sheet1", "F1", "到达地")
	f.SetCellValue("Sheet1", "G1", "运输货物")
	f.SetCellValue("Sheet1", "H1", "车辆种类")
	f.SetCellValue("Sheet1", "I1", "车牌号")
	f.SetCellValue("Sheet1", "J1", "关联运单")
	f.SetCellValue("Sheet1", "K1", "金额")
	f.SetCellValue("Sheet1", "L1", "准牵引总质量")

	start := 2

	provinces := []string{"北京市", "天津市", "河北省", "山西省", "内蒙古自治区", "辽宁省", "吉林省", "黑龙江省", "上海市", "江苏省", "浙江省", "安徽省", "福建省", "江西省", "山东省", "河南省", "湖北省", "湖南省", "广东省", "广西壮族自治区", "海南省", "重庆市", "四川省", "贵州省", "云南省", "西藏自治区", "陕西省", "甘肃省", "青海省", "宁夏回族自治区", "新疆维吾尔自治区", "台湾省", "香港特别行政区", "澳门特别行政区"}
	for key, val := range drivers {
		t := strings.Split(key, "|")
		driverID := t[0]
		plates := lo.Uniq(val)

		for _, plate := range plates {
			rows, err := model.DB.Table("tms_transport_note AS a").
				Joins("JOIN tms_order_history AS b ON a.order_id = b.id AND a.order_version_no = b.version_no").
				Joins("JOIN tms_vehicle AS c ON a.transportation_car_id = c.id").
				Joins("JOIN tms_driver AS d on a.transportation_driver_id = d.driver_id").
				Select([]string{
					"a.transportation_driver",
					"a.transportation_identification_number",
					"d.ocr_address",
					"a.transportation_phone",
					"b.loading_name",
					"b.unload_name",
					"b.goods_type",
					"b.goods_name",
					"c.vehicle_type_name",
					"a.transportation_plate",
					"a.transportation_number",
					"a.freight_paid",
					"c.traction_quality",
				}).
				Where("a.transportation_driver_id = ?", driverID).
				Where("a.transportation_plate = ?", plate).
				Where("a.waybill_status IN (?)", []int{4, 5, 8}).
				Where("a.is_delete = ?", 0).
				Where("a.sys_org_code = ?", sysOrgCode).
				Where("a.finished_time BETWEEN ? AND ?", startTime, endTime).
				Rows()
			if err != nil {
				logger.Stdout.Error(err.Error())
				continue
			}

			uniques := make(map[string]ExcelData)
			for rows.Next() {
				var transportationDriver string
				var transportationIdentificationNumber string
				var ocrAddress sql.NullString
				var transportationPhone string
				var loadingName string
				var unloadName string
				var goodsType string
				var goodsName string
				var vehicleTypeName string
				var transportationPlate string
				var transportationNumber string
				var freightPaid float64
				var tractionQuality sql.NullFloat64

				if err := rows.Scan(
					&transportationDriver,
					&transportationIdentificationNumber,
					&ocrAddress,
					&transportationPhone,
					&loadingName,
					&unloadName,
					&goodsType,
					&goodsName,
					&vehicleTypeName,
					&transportationPlate,
					&transportationNumber,
					&freightPaid,
					&tractionQuality,
				); err != nil {
					logger.Stdout.Error(err.Error())
					continue
				}

				for _, province := range provinces {
					if strings.Contains(loadingName, province) {
						loadingName = strings.ReplaceAll(loadingName, province, "")
						break
					}
				}
				for _, province := range provinces {
					if strings.Contains(unloadName, province) {
						unloadName = strings.ReplaceAll(unloadName, province, "")
						break
					}
				}

				if goodsName == "" {
					goodsName = goodsType
				}

				lineKey := loadingName + "-" + unloadName
				if _, ok := uniques[lineKey]; !ok {
					contractCode := generateContractNo(transportationPlate, lineKey, driverID)
					uniques[lineKey] = ExcelData{
						TransportationDriver:               transportationDriver,
						TransportationIdentificationNumber: transportationIdentificationNumber,
						OcrAddress:                         ocrAddress.String,
						TransportationPhone:                transportationPhone,
						LoadingAddress:                     loadingName,
						UnloadAddress:                      unloadName,
						GoodsName:                          goodsName,
						VehicleTypeName:                    vehicleTypeName,
						TransportationPlate:                transportationPlate,
						ContractCode:                       contractCode,
						FreightPaid:                        freightPaid,
						TractionQuality:                    tractionQuality.Float64,
					}
				} else {
					u := uniques[lineKey]
					u.FreightPaid += freightPaid
					uniques[lineKey] = u
				}
			}

			for _, v := range uniques {
				f.SetCellValue("Sheet1", fmt.Sprintf("A%d", start), v.TransportationDriver)
				f.SetCellValue("Sheet1", fmt.Sprintf("B%d", start), v.TransportationIdentificationNumber)
				f.SetCellValue("Sheet1", fmt.Sprintf("C%d", start), v.OcrAddress)
				f.SetCellValue("Sheet1", fmt.Sprintf("D%d", start), v.TransportationPhone)
				f.SetCellValue("Sheet1", fmt.Sprintf("E%d", start), v.LoadingAddress)
				f.SetCellValue("Sheet1", fmt.Sprintf("F%d", start), v.UnloadAddress)
				f.SetCellValue("Sheet1", fmt.Sprintf("G%d", start), v.GoodsName)
				f.SetCellValue("Sheet1", fmt.Sprintf("H%d", start), v.VehicleTypeName)
				f.SetCellValue("Sheet1", fmt.Sprintf("I%d", start), v.TransportationPlate)
				f.SetCellValue("Sheet1", fmt.Sprintf("J%d", start), v.ContractCode)
				f.SetCellValue("Sheet1", fmt.Sprintf("K%d", start), v.FreightPaid)
				f.SetCellValue("Sheet1", fmt.Sprintf("L%d", start), v.TractionQuality)

				start++
			}
		}
	}

	f.SaveAs(filepath.Join(rootDirName, "开票统计数据.xlsx"))
	logger.Stdout.Info(fmt.Sprintf("生成开票统计数据完成，耗时：%s\n", time.Since(startT).String()))

	logger.Stdout.Info("批量合同、开票材料、开票统计数据已完成")
}

// Generate 生成合同
func Generate(transportationNumbers []string) {
	bucket, err := oss.NewOSSByBucket("cfhswlhyhz")
	if err != nil {
		logger.Stdout.Error(err.Error())
		os.Exit(-1)
	}

	j := &JunZiQian{
		AppKey:      "5163dbe48257703c",
		AppSecret:   "a801378f5163dbe48257703c1b40ad68",
		ServicesUrl: "https://api.junziqian.com",
	}

	for _, v := range transportationNumbers {
		for i := 0; i < 2; i++ {
			aplCode, err := j.ApplySign(v, i)
			if err != nil {
				logger.Stdout.Error(err.Error())
				continue
			}

			time.Sleep(time.Second * 3)
			downloadUrl, err := j.DownloadUrl(aplCode)
			if err != nil {
				logger.Stdout.Error(err.Error())
				continue
			}

			if err := createElectronicAgreement(v, downloadUrl, i, bucket); err != nil {
				logger.Stdout.Error(err.Error())
				continue
			}
		}

		logger.Stdout.Info(fmt.Sprintf("%s 合同生成完毕\n", v))
	}

	// 删除临时文件
	os.RemoveAll("pdf")
}

// createElectronicAgreement 写入电子合同数据到合同表
func createElectronicAgreement(transportationNumber, contractUrl string, contractType int, bucket *oss.OSS) error {
	rows, err := model.DB.Table("tms_transport_note").
		Select([]string{"id"}).
		Where("transportation_number = ?", transportationNumber).
		Rows()
	if err != nil {
		return err
	}

	createData := make(map[string]any)
	for rows.Next() {
		var id string
		if err := rows.Scan(&id); err != nil {
			return err
		}

		createData = map[string]any{
			"waybill_id":                id,
			"waybill_number":            transportationNumber,
			"electronic_agreement_type": contractType,
			"is_delete":                 0,
			"electronic_agreement_url":  "",
			"remarks":                   "后补电子合同",
			"sys_org_code":              "A03",
			"is_contract_reporting":     0,
			"is_chk_evidence":           0,
			"order_type":                0,
		}
	}

	localDir := "pdf"
	if _, err := os.Stat(localDir); os.IsNotExist(err) {
		os.Mkdir(localDir, 0755)
	}

	suffix := "Driver"
	if contractType == 0 {
		suffix = "Shipper"
	}
	filename := filepath.Join(localDir, fmt.Sprintf("%s-%s.pdf", suffix, transportationNumber))
	if err := toolbox.DownloadFile(filename, contractUrl); err != nil {
		return err
	}

	err = bucket.PutObjectFromFile("tpmadd/"+filepath.Base(filename), filename)
	if err != nil {
		return err
	}

	createData["electronic_agreement_url"] = fmt.Sprintf("https://cfhswlhyhz.oss-cn-hangzhou.aliyuncs.com/tpmadd/%s", filepath.Base(filename))
	if err := model.DB.Table("tms_electronic_agreement").
		Where("waybill_number = ?", transportationNumber).
		Where("electronic_agreement_type = ?", contractType).
		Updates(map[string]any{
			"is_delete": 1,
		}).Error; err != nil {
		return err
	}
	if err := model.DB.Table("tms_electronic_agreement").Create(createData).Error; err != nil {
		return err
	}

	return nil
}

// JunZiQian 君子签服务
type JunZiQian struct {
	AppKey      string
	AppSecret   string
	ServicesUrl string
	CommonBody  CommonBody
}

// CommonBody 公共参数结构体
type CommonBody struct {
	Ts          string
	AppKey      string
	Sign        string
	Nonce       string
	EncryMethod string
}

// commonBody 生成公共参数
func (j *JunZiQian) commonBody() CommonBody {
	t := time.Now()
	nonce := fmt.Sprintf("%x", md5.Sum([]byte(t.Format("2006-01-02 15:04:05"))))
	sign := fmt.Sprintf("%x", md5.Sum([]byte("nonce"+nonce+"ts"+fmt.Sprintf("%d", t.UnixMilli())+"app_key"+j.AppKey+"app_secret"+j.AppSecret)))

	return CommonBody{
		Ts:          fmt.Sprintf("%d", t.UnixMilli()),
		AppKey:      j.AppKey,
		Sign:        sign,
		Nonce:       nonce,
		EncryMethod: "md5",
	}
}

// ApplySign 申请电子合同
func (j *JunZiQian) ApplySign(transportationNumber string, contractType int) (string, error) {
	requestUrl := j.ServicesUrl + "/v2/sign/applySign"
	commonBody := j.commonBody()
	body := url.Values{
		"ts":           {commonBody.Ts},
		"app_key":      {commonBody.AppKey},
		"sign":         {commonBody.Sign},
		"nonce":        {commonBody.Nonce},
		"encry_method": {commonBody.EncryMethod},
	}

	if contractType == 0 {
		body = j.ShipperContractBody(transportationNumber, body)
	} else {
		body = j.DriverContractBody(transportationNumber, body)
	}

	resp, err := j.Request(requestUrl, body)
	if err != nil {
		return "", err
	}

	response := make(map[string]any)
	if err := json.Unmarshal([]byte(resp), &response); err != nil {
		return "", err
	}
	if !response["success"].(bool) {
		logger.Stdout.Info(resp)
		return "", errors.New(response["msg"].(string))
	}

	return response["data"].(string), nil
}

// DownloadUrl 获取电子合同的下载地址
func (j *JunZiQian) DownloadUrl(aplCode string) (string, error) {
	requestUrl := j.ServicesUrl + "/v2/sign/linkFile"

	commonBody := j.commonBody()
	body := url.Values{
		"ts":           {commonBody.Ts},
		"app_key":      {commonBody.AppKey},
		"sign":         {commonBody.Sign},
		"nonce":        {commonBody.Nonce},
		"encry_method": {commonBody.EncryMethod},
		"aplCode":      {aplCode},
	}
	body.Add("applyNo", aplCode)

	resp, err := j.Request(requestUrl, body)
	if err != nil {
		return "", err
	}

	response := make(map[string]any)
	json.Unmarshal([]byte(resp), &response)
	if response["success"].(bool) {
		downloadUrl := response["data"].(string)
		return downloadUrl, nil
	}
	return "", errors.New(response["msg"].(string))
}

// Request 发送POST请求
func (j *JunZiQian) Request(url string, formData url.Values) (string, error) {
	resp, err := http.Post(url, "application/x-www-form-urlencoded", strings.NewReader(formData.Encode()))
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	content, _ := io.ReadAll(resp.Body)
	return string(content), nil
}

// DriverContractBody 构建承运合同的请求体
func (j *JunZiQian) DriverContractBody(transportationNumber string, body url.Values) url.Values {
	rows, err := model.DB.Table("tms_transport_note AS a").
		Joins("JOIN tms_order_history AS b ON a.order_id = b.id AND a.order_version_no = b.version_no").
		Joins("JOIN tms_driver AS d ON a.payee_id = d.driver_id").
		Select([]string{"d.driver_name AS payee_name", "d.identification_number AS payee_identity_card", "d.phone AS payee_mobile", "a.transportation_number", "b.freighter_name", "b.loading_phone", "a.loading_address", "b.goods_type", "b.goods_name", "b.discharger_name", "b.unload_phone", "a.unload_address", "a.transportation_driver", "a.transportation_phone", "a.transportation_plate", "a.loading_time", "a.unload_time", "a.freight_amount", "a.create_time"}).
		Where("a.transportation_number = ?", transportationNumber).
		Rows()
	if err != nil {
		logger.Stdout.Error(err.Error())
		return nil
	}

	platformCompanyName := "赤峰现代智慧物流有限公司"
	platformCompanyEmail := "<EMAIL>"
	platformCompanyIdentityCard := "91150402MACRQHHW1K"

	for rows.Next() {
		var payeeName string
		var payeeIdentityCard string
		var payeeMobile string
		var transportationNumber string
		var freighterName string
		var loadingPhone string
		var loadingAddress string
		var goodsType string
		var goodsName string
		var dischargerName string
		var unloadPhone string
		var unloadAddress string
		var transportationDriver string
		var transportationPhone string
		var transportationPlate string
		var loadingTime time.Time
		var unloadTime sql.NullTime
		var freightAmount float64
		var createTime time.Time

		if err := rows.Scan(&payeeName, &payeeIdentityCard, &payeeMobile, &transportationNumber, &freighterName, &loadingPhone, &loadingAddress, &goodsType, &goodsName, &dischargerName, &unloadPhone, &unloadAddress, &transportationDriver, &transportationPhone, &transportationPlate, &loadingTime, &unloadTime, &freightAmount, &createTime); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}

		if strings.Contains(transportationNumber, "XYCY") {
			platformCompanyName = "赤峰市元宝山区兴元智慧物流有限公司"
			platformCompanyEmail = "<EMAIL>"
			platformCompanyIdentityCard = "91150403MADNMCLM8L"
		}

		// 合同参数
		signatoriesMap := []map[string]string{
			{
				"fullName":     platformCompanyName,
				"identityType": "12",
				"identityCard": platformCompanyIdentityCard,
				"email":        platformCompanyEmail,
				"chapteJson":   `[{"chaptes":[{"offsetX":0.25,"offsetY":0.84,"cheight":150}],"page":-1}]`,
				"serverCaAuto": "1",
				"signLevel":    "1",
			},
			{
				"fullName":     payeeName,
				"identityType": "1",
				"identityCard": payeeIdentityCard,
				"mobile":       payeeMobile,
				"chapteJson":   `[{"chaptes":[{"offsetX":0.68,"offsetY":0.84,"cheight":130}],"page":-1}]`,
				"serverCaAuto": "1",
				"signLevel":    "3",
			},
		}
		signatories, _ := json.Marshal(signatoriesMap)

		unloadTimeT := ""
		if unloadTime.Valid {
			unloadTimeT = unloadTime.Time.Format("2006-01-02")
		} else {
			unloadTimeT = time.Now().Format("2006-01-02")
		}
		templateParamsMap := map[string]string{
			"合同号":     fmt.Sprintf("SJHT%s%05d", createTime.Format("20060102151617")[2:], rand.Intn(999999)),
			"网络货运经营者": platformCompanyName,
			"实际承运人":   payeeName,
			"运单编号":    transportationNumber,
			"发货人":     freighterName,
			"发货人联系电话": loadingPhone,
			"发货地址":    loadingAddress,
			"货物名称":    goodsType + " " + goodsName,
			"收货人":     dischargerName,
			"收货人联系电话": unloadPhone,
			"卸货地址":    unloadAddress,
			"驾驶员":     transportationDriver + " " + transportationPhone,
			"驾驶员联系电话": transportationPhone,
			"实际承运车牌号": transportationPlate,
			"预定装货时间":  loadingTime.Format("2006-01-02"),
			"预定卸货时间":  unloadTimeT,
			"最晚付运费时间": "最晚卸货后7天内付运费",
			"运费":      fmt.Sprintf("%0.2f", freightAmount),
			"甲方":      platformCompanyName,
			"乙方":      payeeName,
			"签订日期":    createTime.Format("2006-01-02"),
		}
		templateParams, _ := json.Marshal(templateParamsMap)

		body.Add("contractName", "driver"+transportationNumber)
		body.Add("signatories", string(signatories))
		body.Add("serverCa", "1")
		body.Add("dealType", "1")
		body.Add("templateNo", "6815E65F67E543E5ADF38FE6C6E20FE0")
		body.Add("templateParams", string(templateParams))
		body.Add("fileType", "4")
		body.Add("positionType", "0")
		body.Add("noBorderSign", "1")
	}

	return body
}

// ShipperContractBody 构建托运合同的请求体
func (j *JunZiQian) ShipperContractBody(transportationNumber string, body url.Values) url.Values {
	rows, err := model.DB.Table("tms_transport_note AS a").
		Joins("JOIN tms_order_history AS b ON a.order_id = b.id AND a.order_version_no = b.version_no").
		Joins("JOIN tms_shipper AS c ON a.create_company_id = c.company_id").
		Select([]string{"c.company_name AS shipper_name", "c.shipper_phone", "c.social_credit_code AS shipper_social_credit_code", "a.transportation_number", "b.freighter_name", "b.loading_phone", "a.loading_address", "b.goods_type", "b.goods_name", "b.discharger_name", "b.unload_phone", "a.unload_address", "a.transportation_driver", "a.transportation_phone", "a.transportation_plate", "a.loading_time", "a.unload_time", "a.freight_gross_shipper", "a.create_time"}).
		Where("a.transportation_number = ?", transportationNumber).
		Where("c.shipper_type = 1 AND c.is_delete = 0").
		Rows()
	if err != nil {
		logger.Stdout.Error(err.Error())
		return nil
	}

	platformCompanyName := "赤峰现代智慧物流有限公司"
	platformCompanyEmail := "<EMAIL>"
	platformCompanyIdentityCard := "91150402MACRQHHW1K"

	for rows.Next() {
		var shipperName string
		var shipperPhone string
		var shipperSocialCreditCode string
		var transportationNumber string
		var freighterName string
		var loadingPhone string
		var loadingAddress string
		var goodsType string
		var goodsName string
		var dischargerName string
		var unloadPhone string
		var unloadAddress string
		var transportationDriver string
		var transportationPhone string
		var transportationPlate string
		var loadingTime time.Time
		var unloadTime sql.NullTime
		var freightGrossShipper float64
		var createTime time.Time

		if err := rows.Scan(&shipperName, &shipperPhone, &shipperSocialCreditCode, &transportationNumber, &freighterName, &loadingPhone, &loadingAddress, &goodsType, &goodsName, &dischargerName, &unloadPhone, &unloadAddress, &transportationDriver, &transportationPhone, &transportationPlate, &loadingTime, &unloadTime, &freightGrossShipper, &createTime); err != nil {
			logger.Stdout.Error(err.Error())
			return nil
		}

		if strings.Contains(transportationNumber, "XYCY") {
			platformCompanyName = "赤峰市元宝山区兴元智慧物流有限公司"
			platformCompanyEmail = "<EMAIL>"
			platformCompanyIdentityCard = "91150403MADNMCLM8L"
		}

		// 合同参数
		signatoriesMap := []map[string]string{
			{
				"fullName":     shipperName,
				"identityType": "12",
				"identityCard": shipperSocialCreditCode,
				"email":        shipperPhone + "@qq.com",
				"chapteJson":   `[{"chaptes":[{"offsetX":0.25,"offsetY":0.87,"cheight":150}],"page":-1}]`,
				"serverCaAuto": "1",
				"signLevel":    "1",
			},
			{
				"fullName":     platformCompanyName,
				"identityType": "12",
				"identityCard": platformCompanyIdentityCard,
				"email":        platformCompanyEmail,
				"chapteJson":   `[{"chaptes":[{"offsetX":0.68,"offsetY":0.87,"cheight":150}],"page":-1}]`,
				"serverCaAuto": "1",
				"signLevel":    "1",
			},
		}
		signatories, _ := json.Marshal(signatoriesMap)

		unloadTimeT := ""
		if unloadTime.Valid {
			unloadTimeT = unloadTime.Time.Format("2006-01-02")
		} else {
			unloadTimeT = time.Now().Format("2006-01-02")
		}
		templateParamsMap := map[string]string{
			"合同号":     fmt.Sprintf("HZHT%s%05d", createTime.Format("20060102151617")[2:], rand.Intn(999999)),
			"托运方":     shipperName,
			"网络货运经营者": platformCompanyName,
			"异议时限":    "24",
			"运单编号":    transportationNumber,
			"发货人":     freighterName,
			"发货人联系电话": loadingPhone,
			"发货地址":    loadingAddress,
			"货物名称":    goodsType + " " + goodsName,
			"收货人":     dischargerName,
			"收货人联系电话": unloadPhone,
			"卸货地址":    unloadAddress,
			"驾驶员":     transportationDriver + " " + transportationPhone,
			"驾驶员联系电话": transportationPhone,
			"实际承运车牌号": transportationPlate,
			"预定装货时间":  loadingTime.Format("2006-01-02"),
			"预定卸货时间":  unloadTimeT,
			"运费":      fmt.Sprintf("%0.2f", freightGrossShipper),
			"甲方":      shipperName,
			"乙方":      platformCompanyName,
			"签订日期":    createTime.Format("2006-01-02"),
		}
		templateParams, _ := json.Marshal(templateParamsMap)

		body.Add("contractName", "shipper"+transportationNumber)
		body.Add("signatories", string(signatories))
		body.Add("serverCa", "1")
		body.Add("dealType", "1")
		body.Add("templateNo", "877516768DD04ED1B2B7F9DC0D456147")
		body.Add("templateParams", string(templateParams))
		body.Add("fileType", "4")
		body.Add("positionType", "0")
		body.Add("noBorderSign", "1")
	}

	return body
}

// Ping 检测君子签服务是否可用
func (j *JunZiQian) Ping() {
	requestUrl := j.ServicesUrl + "/v2/ping"
	commonBody := j.commonBody()
	body := url.Values{
		"ts":           {commonBody.Ts},
		"app_key":      {commonBody.AppKey},
		"sign":         {commonBody.Sign},
		"nonce":        {commonBody.Nonce},
		"encry_method": {commonBody.EncryMethod},
	}

	_, err := j.Request(requestUrl, body)
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	logger.Stdout.Info("Ping Done")
}

// ApplySignBatch 生成批量合同
func (j *JunZiQian) ApplySignBatch(plate, driverID, firstDate, lastDate, dirName, sysOrgCode string, tmpl *template.Template) map[string]BatchSign {
	requestUrl := j.ServicesUrl + "/v2/sign/applySign"

	signs := j.BatchContractBody(plate, driverID, firstDate, lastDate, dirName, sysOrgCode, tmpl)
	if len(signs) == 0 {
		return nil
	}

	for k, s := range signs {
		resp, err := j.RequestFormData(requestUrl, s.Body)
		if err != nil {
			s.AplErr = err
			continue
		}

		response := make(map[string]any)
		if err := json.Unmarshal([]byte(resp), &response); err != nil {
			s.AplErr = err
			continue
		}
		if !response["success"].(bool) {
			s.AplErr = err
			continue
		}
		s.AplCode = response["data"].(string)
		s.AplErr = nil

		signs[k] = s
	}

	return signs
}

// RequestFormData 发送form-data POST请求
func (j *JunZiQian) RequestFormData(url string, formData url.Values) (string, error) {
	pdfFilename := formData.Get("attachFiles")
	formData.Del("attachFiles")

	// 创建一个缓冲区，用于存储multipart/form-data的数据
	var requestBody bytes.Buffer
	writer := multipart.NewWriter(&requestBody)

	// 将url.Values中的字段添加到multipart.Writer
	for key, values := range formData {
		for _, value := range values {
			// 使用 CreateFormField 创建一个表单字段
			fieldWriter, err := writer.CreateFormField(key)
			if err != nil {
				logger.Stdout.Error(err.Error())
				return err.Error(), err
			}
			_, err = io.Copy(fieldWriter, bytes.NewBufferString(value))
			if err != nil {
				logger.Stdout.Error(err.Error())
				return err.Error(), err
			}
		}
	}

	// 创建一个表单文件字段
	fileWriter, err := writer.CreateFormFile("attachFiles", filepath.Base(pdfFilename))
	if err != nil {
		logger.Stdout.Error(err.Error())
		return err.Error(), err
	}

	// 打开要上传的文件
	file, err := os.Open(pdfFilename)
	if err != nil {
		logger.Stdout.Error(err.Error())
		return err.Error(), err
	}
	defer file.Close()

	// 将文件内容复制到表单文件字段中
	_, err = io.Copy(fileWriter, file)
	if err != nil {
		logger.Stdout.Error(err.Error())
		return err.Error(), err
	}

	// 关闭multipart writer以设置结束边界
	writer.Close()

	// 创建一个新的POST请求
	req, err := http.NewRequest("POST", url, &requestBody)
	if err != nil {
		logger.Stdout.Error(err.Error())
		return err.Error(), err
	}

	// 设置Content-Type头为multipart/form-data，并包含boundary
	req.Header.Set("Content-Type", writer.FormDataContentType())

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		logger.Stdout.Error(err.Error())
		return "", err
	}
	defer resp.Body.Close()

	content, _ := io.ReadAll(resp.Body)
	return string(content), nil
}

// BatchContractBody 构建批量合同的请求体并生成pdf附件
func (j *JunZiQian) BatchContractBody(plate, driverID, firstDate, lastDate, dirName, sysOrgCode string, tmpl *template.Template) map[string]BatchSign {
	rows, err := model.DB.Table("tms_transport_note AS a").
		Joins("JOIN tms_order_history AS b ON a.order_id = b.id AND a.order_version_no = b.version_no").
		Select([]string{
			"a.transportation_number",
			"b.freighter_name",
			"b.loading_phone",
			"b.loading_name",
			"b.goods_type",
			"b.goods_name",
			"b.discharger_name",
			"b.unload_phone",
			"b.unload_name",
			"a.transportation_driver",
			"a.transportation_phone",
			"a.transportation_identification_number",
			"a.transportation_plate",
			"a.freight_paid",
		}).
		Where("a.transportation_driver_id = ?", driverID).
		Where("a.transportation_plate = ?", plate).
		Where("a.waybill_status IN (?)", []int{4, 5, 8}).
		Where("a.is_delete = ?", 0).
		Where("a.sys_org_code = ?", sysOrgCode).
		Where("a.finished_time BETWEEN ? AND ?", firstDate, lastDate).
		Rows()
	if err != nil {
		logger.Stdout.Error(err.Error())
		return nil
	}

	platformCompanyName := "赤峰现代智慧物流有限公司"
	platformCompanyEmail := "<EMAIL>"
	platformCompanyIdentityCard := "91150402MACRQHHW1K"
	if sysOrgCode == "A07" {
		platformCompanyName = "赤峰市元宝山区兴元智慧物流有限公司"
		platformCompanyEmail = "<EMAIL>"
		platformCompanyIdentityCard = "91150403MADNMCLM8L"
	}

	provinces := []string{"北京市", "天津市", "河北省", "山西省", "内蒙古自治区", "辽宁省", "吉林省", "黑龙江省", "上海市", "江苏省", "浙江省", "安徽省", "福建省", "江西省", "山东省", "河南省", "湖北省", "湖南省", "广东省", "广西壮族自治区", "海南省", "重庆市", "四川省", "贵州省", "云南省", "西藏自治区", "陕西省", "甘肃省", "青海省", "宁夏回族自治区", "新疆维吾尔自治区", "台湾省", "香港特别行政区", "澳门特别行政区"}

	signs := make(map[string]BatchSign)
	for rows.Next() {
		var transportationNumber string
		var freighterName string
		var loadingPhone string
		var loadingName string
		var goodsType string
		var goodsName string
		var dischargerName string
		var unloadPhone string
		var unloadName string
		var transportationDriver string
		var transportationPhone string
		var transportationIdentificationNumber string
		var transportationPlate string
		var freightPaid float64

		if err := rows.Scan(&transportationNumber, &freighterName, &loadingPhone, &loadingName, &goodsType, &goodsName, &dischargerName, &unloadPhone, &unloadName, &transportationDriver, &transportationPhone, &transportationIdentificationNumber, &transportationPlate, &freightPaid); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}

		for _, province := range provinces {
			if strings.Contains(loadingName, province) {
				loadingName = strings.ReplaceAll(loadingName, province, "")
				break
			}
		}
		for _, province := range provinces {
			if strings.Contains(unloadName, province) {
				unloadName = strings.ReplaceAll(unloadName, province, "")
				break
			}
		}

		if goodsName == "" {
			goodsName = goodsType
		}
		appendix := AppendixT{
			TransportationNumber: transportationNumber,
			GoodsName:            goodsName,
			ShippingAddress:      loadingName,
			ReceivingAddress:     unloadName,
			FreightPaid:          freightPaid,
		}

		key := loadingName + "-" + unloadName
		if _, ok := signs[key]; !ok {
			signs[key] = BatchSign{
				LineKey:                    key,
				TotalFreightAmount:         freightPaid,
				TransportationDriver:       transportationDriver,
				TransportationIdentityCard: transportationIdentificationNumber,
				TransportationPhone:        transportationPhone,
				TransportationPlate:        transportationPlate,
				Appendix:                   []AppendixT{},
			}
			s := signs[key]
			s.Appendix = append(s.Appendix, appendix)
			signs[key] = s
		} else {
			s := signs[key]
			s.TotalFreightAmount += freightPaid
			s.Appendix = append(s.Appendix, appendix)
			signs[key] = s
		}
	}

	for k, s := range signs {
		commonBody := j.commonBody()
		body := url.Values{
			"ts":           {commonBody.Ts},
			"app_key":      {commonBody.AppKey},
			"sign":         {commonBody.Sign},
			"nonce":        {commonBody.Nonce},
			"encry_method": {commonBody.EncryMethod},
		}

		htmlFilename := filepath.Join(dirName, fmt.Sprintf("%s%s%s.html", plate, driverID, s.LineKey))
		pdfFilename := filepath.Join(dirName, fmt.Sprintf("%s%s%s-appendix.pdf", plate, driverID, s.LineKey))
		f, _ := os.Create(htmlFilename)

		// 标记最后一个元素
		if len(s.Appendix) > 0 {
			s.Appendix[len(s.Appendix)-1].IsLast = true
		}
		// 拆分数据
		appendixDataChunk := lo.Chunk(s.Appendix, 58)

		htmlData := struct {
			AppendixDataChunk  [][]AppendixT
			TotalFreightAmount string
		}{
			AppendixDataChunk:  appendixDataChunk,
			TotalFreightAmount: fmt.Sprintf("%0.2f", s.TotalFreightAmount),
		}
		if err := tmpl.ExecuteTemplate(f, "appendix.html", htmlData); err != nil {
			logger.Stdout.Error(fmt.Sprintf("生成附件html失败: %v\n", err))
			continue
		}

		// 生成pdf
		cmd := exec.Command("wkhtmltopdf", "--no-outline", "--margin-top", "0", "--margin-right", "0", "--margin-bottom", "0", "--margin-left", "0", "--disable-smart-shrinking", "--load-media-error-handling", "abort", htmlFilename, pdfFilename)
		if err := cmd.Run(); err != nil {
			logger.Stdout.Error(fmt.Sprintf("生成附件pdf失败: %v\n", err))
			continue
		}
		s.AppendixPDF = pdfFilename

		// 删除生成的html
		os.Remove(htmlFilename)

		// 合同参数
		signatoriesMap := []map[string]string{
			{
				"fullName":     platformCompanyName,
				"identityType": "12",
				"identityCard": platformCompanyIdentityCard,
				"email":        platformCompanyEmail,
				"chapteJson":   `[{"chaptes":[{"offsetX":0.25,"offsetY":0.47,"cheight":150}],"page":-1}]`,
				"serverCaAuto": "1",
				"signLevel":    "1",
			},
			{
				"fullName":     s.TransportationDriver,
				"identityType": "1",
				"identityCard": s.TransportationIdentityCard,
				"mobile":       s.TransportationPhone,
				"chapteJson":   `[{"chaptes":[{"offsetX":0.68,"offsetY":0.47,"cheight":130}],"page":-1}]`,
				"serverCaAuto": "1",
				"signLevel":    "3",
			},
		}
		signatories, _ := json.Marshal(signatoriesMap)

		s.ContractCode = generateContractNo(plate, s.LineKey, driverID)
		templateParamsMap := map[string]string{
			"合同号":     s.ContractCode,
			"网络货运经营者": platformCompanyName,
			"实际承运人":   s.TransportationDriver,
			"驾驶员":     s.TransportationDriver + " " + s.TransportationPhone,
			"驾驶员联系电话": s.TransportationPhone,
			"实际承运车牌号": s.TransportationPlate,
			"运费":      fmt.Sprintf("%0.2f 元", s.TotalFreightAmount),
			"甲方":      platformCompanyName,
			"乙方":      s.TransportationDriver,
			"签订日期":    lastDate,
		}
		templateParams, _ := json.Marshal(templateParamsMap)

		body.Add("contractName", "driverbatch"+plate+driverID)
		body.Add("signatories", string(signatories))
		body.Add("serverCa", "1")
		body.Add("dealType", "1")
		body.Add("templateNo", "7B21CCF9F5CF4ECBA222F62E20AEF2D3")
		body.Add("templateParams", string(templateParams))
		body.Add("fileType", "4")
		body.Add("positionType", "0")
		body.Add("noBorderSign", "1")
		body.Add("attachFiles", pdfFilename)

		s.Body = body

		signs[k] = s
	}

	return signs
}

func (j *JunZiQian) BatchContractBodyCheck(plate, driverID, firstDate, lastDate, dirName, sysOrgCode string, tmpl *template.Template) map[string]BatchSign {
	rows, err := model.DB.Table("tms_transport_note AS a").
		Joins("JOIN tms_order_history AS b ON a.order_id = b.id AND a.order_version_no = b.version_no").
		Select([]string{
			"a.transportation_number",
			"b.freighter_name",
			"b.loading_phone",
			"b.loading_name",
			"b.goods_type",
			"b.goods_name",
			"b.discharger_name",
			"b.unload_phone",
			"b.unload_name",
			"a.transportation_driver",
			"a.transportation_phone",
			"a.transportation_identification_number",
			"a.transportation_plate",
			"a.freight_paid",
		}).
		Where("a.transportation_driver_id = ?", driverID).
		Where("a.transportation_plate = ?", plate).
		Where("a.waybill_status IN (?)", []int{4, 5, 8}).
		Where("a.is_delete = ?", 0).
		Where("a.sys_org_code = ?", sysOrgCode).
		Where("a.finished_time BETWEEN ? AND ?", firstDate, lastDate).
		Rows()
	if err != nil {
		logger.Stdout.Error(err.Error())
		return nil
	}

	provinces := []string{"北京市", "天津市", "河北省", "山西省", "内蒙古自治区", "辽宁省", "吉林省", "黑龙江省", "上海市", "江苏省", "浙江省", "安徽省", "福建省", "江西省", "山东省", "河南省", "湖北省", "湖南省", "广东省", "广西壮族自治区", "海南省", "重庆市", "四川省", "贵州省", "云南省", "西藏自治区", "陕西省", "甘肃省", "青海省", "宁夏回族自治区", "新疆维吾尔自治区", "台湾省", "香港特别行政区", "澳门特别行政区"}

	signs := make(map[string]BatchSign)
	for rows.Next() {
		var transportationNumber string
		var freighterName string
		var loadingPhone string
		var loadingName string
		var goodsType string
		var goodsName string
		var dischargerName string
		var unloadPhone string
		var unloadName string
		var transportationDriver string
		var transportationPhone string
		var transportationIdentificationNumber string
		var transportationPlate string
		var freightPaid float64

		if err := rows.Scan(&transportationNumber, &freighterName, &loadingPhone, &loadingName, &goodsType, &goodsName, &dischargerName, &unloadPhone, &unloadName, &transportationDriver, &transportationPhone, &transportationIdentificationNumber, &transportationPlate, &freightPaid); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}

		for _, province := range provinces {
			if strings.Contains(loadingName, province) {
				loadingName = strings.ReplaceAll(loadingName, province, "")
				break
			}
		}
		for _, province := range provinces {
			if strings.Contains(unloadName, province) {
				unloadName = strings.ReplaceAll(unloadName, province, "")
				break
			}
		}

		if goodsName == "" {
			goodsName = goodsType
		}
		appendix := AppendixT{
			TransportationNumber: transportationNumber,
			GoodsName:            goodsName,
			ShippingAddress:      loadingName,
			ReceivingAddress:     unloadName,
			FreightPaid:          freightPaid,
		}

		key := loadingName + "-" + unloadName
		if _, ok := signs[key]; !ok {
			signs[key] = BatchSign{
				LineKey:                    key,
				TotalFreightAmount:         freightPaid,
				TransportationDriver:       transportationDriver,
				TransportationIdentityCard: transportationIdentificationNumber,
				TransportationPhone:        transportationPhone,
				TransportationPlate:        transportationPlate,
				Appendix:                   []AppendixT{},
			}
			s := signs[key]
			s.Appendix = append(s.Appendix, appendix)
			signs[key] = s
		} else {
			s := signs[key]
			s.TotalFreightAmount += freightPaid
			s.Appendix = append(s.Appendix, appendix)
			signs[key] = s
		}
	}

	return signs
}

// generateContractNo 生成合同编号
// 合同编号规则
// PLHT+plate中的ASCII字符+s.LineKey的MD5值前6位+driverID
func generateContractNo(plate, lineKey, driverID string) string {
	var asciiPlate []rune
	for _, r := range plate {
		// 只保留ASCII字符
		if r >= 32 && r <= 126 { // ASCII可打印字符范围
			asciiPlate = append(asciiPlate, r)
		}
	}

	// 计算MD5值
	hash := md5.New()
	hash.Write([]byte(lineKey))
	hashBytes := hash.Sum(nil)

	// 转换为16进制字符串并取前6位
	md5String := strings.ToUpper(hex.EncodeToString(hashBytes))

	return fmt.Sprintf("PLHT%s%s%s", driverID, string(asciiPlate), md5String[:6])
}

func downloadInvoiceData(driverID string, plates []string, dirName, sysOrgCode string) {
	// 检查目录是否存在
	_, err := os.Stat(dirName)
	if err != nil {
		if os.IsExist(err) {
			return
		}
		if os.IsNotExist(err) {
			os.MkdirAll(dirName, 0777)
		}
	}

	var identificationFrontImgUrl sql.NullString
	var driverLicenseImgUrl sql.NullString
	var workCertificateImgUrl sql.NullString
	var contractUrl sql.NullString

	// 获取驾驶员信息
	driverRows, err := model.DB.Table("tms_driver AS a").
		Joins("LEFT JOIN tms_user_agree AS b ON a.driver_id = b.user_id").
		Select([]string{
			"a.identification_front_img_url",
			"a.driver_license_img_url",
			"a.work_certificate_img_url",
			"b.contract_url",
		}).
		Where("a.driver_id = ?", driverID).
		Where("a.is_delete = ?", 0).
		Where("b.is_delete = ?", 0).
		Where("b.sys_org_code IS NULL OR b.sys_org_code = '' OR b.sys_org_code = ?", sysOrgCode).
		Limit(1).
		Rows()
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	for driverRows.Next() {
		if err := driverRows.Scan(
			&identificationFrontImgUrl,
			&driverLicenseImgUrl,
			&workCertificateImgUrl,
			&contractUrl,
		); err != nil {
			logger.Stdout.Error(err.Error())
			return
		}
	}

	if identificationFrontImgUrl.Valid && identificationFrontImgUrl.String != "" {
		toolbox.DownloadFile(filepath.Join(dirName, "1.身份证"+filepath.Ext(identificationFrontImgUrl.String)), identificationFrontImgUrl.String)
	}
	if driverLicenseImgUrl.Valid && driverLicenseImgUrl.String != "" {
		toolbox.DownloadFile(filepath.Join(dirName, "2.驾驶证"+filepath.Ext(driverLicenseImgUrl.String)), driverLicenseImgUrl.String)
	}
	if workCertificateImgUrl.Valid && workCertificateImgUrl.String != "" {
		toolbox.DownloadFile(filepath.Join(dirName, "3.从业资格证"+filepath.Ext(workCertificateImgUrl.String)), workCertificateImgUrl.String)
	}
	if contractUrl.Valid && contractUrl.String != "" {
		toolbox.DownloadFile(filepath.Join(dirName, "4.税务代理委托书"+filepath.Ext(contractUrl.String)), contractUrl.String)
	}

	for _, plate := range plates {
		var drivingPermitImgUrl sql.NullString
		var driverLicenseFrontImgUrl sql.NullString
		var transportPermitImgUrl sql.NullString
		var transportPermitAdditionalImgUrl sql.NullString
		var vehicleAffiliatedStatementPdfUrl sql.NullString
		var vehicleOwner string

		// 获取车辆信息
		vehicleRows, err := model.DB.Table("tms_vehicle").
			Select([]string{
				"driving_permit_img_url",
				"driving_front_img_url",
				"transport_permit_img_url",
				"transport_permit_additional_img_url",
				"vehicle_affiliated_statement_pdf_url",
				"vehicle_owner",
			}).
			Where("vehicle_license_number = ?", plate).
			Where("is_delete = ?", 0).
			Limit(1).
			Rows()
		if err != nil {
			logger.Stdout.Error(err.Error())
			return
		}
		for vehicleRows.Next() {
			if err := vehicleRows.Scan(
				&drivingPermitImgUrl,
				&driverLicenseFrontImgUrl,
				&transportPermitImgUrl,
				&transportPermitAdditionalImgUrl,
				&vehicleAffiliatedStatementPdfUrl,
				&vehicleOwner,
			); err != nil {
				logger.Stdout.Error(err.Error())
				return
			}
		}
		//蒙D41430-1744883863061790722-赤峰市元宝山区-赤峰市红山区-merge

		// 下载文件

		if drivingPermitImgUrl.Valid && drivingPermitImgUrl.String != "" {
			toolbox.DownloadFile(filepath.Join(dirName, plate+"_"+"4.行驶证主页"+filepath.Ext(drivingPermitImgUrl.String)), drivingPermitImgUrl.String)
		}
		if driverLicenseFrontImgUrl.Valid && driverLicenseFrontImgUrl.String != "" {
			toolbox.DownloadFile(filepath.Join(dirName, plate+"_"+"5.行驶证副页"+filepath.Ext(driverLicenseFrontImgUrl.String)), driverLicenseFrontImgUrl.String)
		}
		if transportPermitImgUrl.Valid && transportPermitImgUrl.String != "" {
			toolbox.DownloadFile(filepath.Join(dirName, plate+"_"+"6.道路运输证"+filepath.Ext(transportPermitImgUrl.String)), transportPermitImgUrl.String)
		}

		if transportPermitAdditionalImgUrl.Valid && transportPermitAdditionalImgUrl.String != "" {
			toolbox.DownloadFile(filepath.Join(dirName, plate+"_"+"7.截图"+filepath.Ext(transportPermitAdditionalImgUrl.String)), transportPermitAdditionalImgUrl.String)
		}
	}

	logger.Stdout.Info(fmt.Sprintf("Download %s Done\n", driverID))
}
