package table

import (
	"fmt"
	"os"
	"regexp"
	"strconv"
	"strings"
	"wlhy/model"
	"wlhy/thirdparty/cmb"

	"github.com/samber/lo"
	"github.com/xuri/excelize/v2"
)

const (
	TotalAmount                = "total_Amount"
	TotalAmountTransit         = "total_Amount_transit"
	TransportAmount            = "transport_Amount"
	ImmediatelyAmount          = "immediately_Amount"
	ImmediatelyCostAmount      = "immediately_cost_Amount"
	ImmediatelyExceptionAmount = "immediately_exception_Amount"
	ServiceAmount              = "service_Amount"
	InsuranceAmount            = "insurance_Amount"
	CommissionAmount           = "commission_Amount"
	OilAmount                  = "oil_Amount"
	CostProfit                 = "cost_profit"
)

var planItms = map[string]string{
	"total_Amount":                 "资金池流水",
	"total_Amount_transit":         "中转账户流水",
	"transport_Amount":             "到付运费流水",
	"immediately_Amount":           "立即付流水",
	"immediately_cost_Amount":      "立即付服务费流水",
	"immediately_exception_Amount": "立即付坏账回收流水",
	"service_Amount":               "服务费流水",
	"insurance_Amount":             "保险费流水",
	"commission_Amount":            "手续费流水",
	"oil_Amount":                   "司机油费流水",
	"cost_profit":                  "油费成本账户流水",
}

// 到付+立即付 两条支付计划 10 + 2 + 4 油卡+2 保险+2
// 到付+预付 两条支付计划 16条流水 油卡+2 保险+2
// 到付 一条支付计划 10条流水 油卡+2 保险+2
// 油卡 2条流水
// 保险 2条流水

// 正常流程下 最少要有10条流水记录

// CheckPayments 检查银行支付流水
func CheckPayments(exceptionRecordExcel string) error {
	f, err := excelize.OpenFile(exceptionRecordExcel)
	if err != nil {
		return err
	}

	excelRows, err := f.GetRows("Sheet1")
	if err != nil {
		return err
	}

	records := make(map[string]map[string]int)
	transportationNumbers := []string{}
	for k, v := range excelRows {
		if k == 0 {
			continue
		}

		transportationNumber := strings.TrimSpace(v[0])
		transportationNumbers = append(transportationNumbers, transportationNumber)

		remark := strings.TrimSpace(v[10])

		if _, ok := records[transportationNumber]; !ok {
			records[transportationNumber] = map[string]int{
				TotalAmount:                0,
				TransportAmount:            0,
				ImmediatelyAmount:          0,
				ImmediatelyCostAmount:      0,
				ImmediatelyExceptionAmount: 0,
				ServiceAmount:              0,
				InsuranceAmount:            0,
				CommissionAmount:           0,
				OilAmount:                  0,
				CostProfit:                 0,
			}
		}

		if strings.Contains(remark, "付款9999") {
			records[transportationNumber][TotalAmount]++
		} else if strings.Contains(remark, "付款6666") {
			records[transportationNumber][TotalAmountTransit]++
		} else if strings.Contains(remark, "付款司机") {
			records[transportationNumber][TransportAmount]++
		} else if strings.Contains(remark, "手续费") {
			records[transportationNumber][CommissionAmount]++
		} else if strings.Contains(remark, "服务费") {
			records[transportationNumber][ServiceAmount]++
		} else if strings.Contains(remark, "油卡成本") {
			records[transportationNumber][CostProfit]++
		} else if strings.Contains(remark, "立即付司机") {

		}

	}

	transportationNumbers = lo.Uniq(transportationNumbers)
	var plans []model.TmsTransportPayPlan
	model.DB.
		Select([]string{"waybill_No", "`type`", "total_Amount", "transport_Amount", "immediately_Amount", "immediately_cost_Amount", "immediately_exception_Amount", "service_Amount", "insurance_Amount", "commission_Amount", "oil_Amount", "cost_profit"}).
		Where("waybill_No IN (?)", transportationNumbers).
		Where("is_delete = ?", 0).
		Find(&plans)

	type MissingT struct {
		WaybillNo string
		Reason    string
		Amount    float64
		Count     int
	}
	missing := []MissingT{}
	for _, plan := range plans {
		if plan.Type != 1 {
			continue
		}
		if plan.TotalAmount > 0 && records[plan.WaybillNo][TotalAmount] < 2 {
			missing = append(missing, MissingT{
				WaybillNo: plan.WaybillNo,
				Reason:    planItms[TotalAmount],
				Amount:    plan.TotalAmount,
				Count:     records[plan.WaybillNo][TotalAmount],
			})
		}
		if plan.TotalAmount > 0 && records[plan.WaybillNo][TotalAmountTransit] < 2 {
			missing = append(missing, MissingT{
				WaybillNo: plan.WaybillNo,
				Reason:    planItms[TotalAmountTransit],
				Amount:    plan.TotalAmount,
				Count:     records[plan.WaybillNo][TotalAmountTransit],
			})
		}
		if plan.TransportAmount > 0 && records[plan.WaybillNo][TransportAmount] < 2 {
			missing = append(missing, MissingT{
				WaybillNo: plan.WaybillNo,
				Reason:    planItms[TransportAmount],
				Amount:    plan.TransportAmount,
				Count:     records[plan.WaybillNo][TransportAmount],
			})
		}
		if plan.ServiceAmount > 0 && records[plan.WaybillNo][ServiceAmount] < 2 {
			missing = append(missing, MissingT{
				WaybillNo: plan.WaybillNo,
				Reason:    planItms[ServiceAmount],
				Amount:    plan.ServiceAmount,
				Count:     records[plan.WaybillNo][ServiceAmount],
			})
		}
		if plan.CommissionAmount > 0 && records[plan.WaybillNo][CommissionAmount] < 2 {
			missing = append(missing, MissingT{
				WaybillNo: plan.WaybillNo,
				Reason:    planItms[CommissionAmount],
				Amount:    plan.CommissionAmount,
				Count:     records[plan.WaybillNo][CommissionAmount],
			})
		}
	}

	// 写入缺失流水记录
	f.NewSheet("缺失流水")
	f.SetCellValue("缺失流水", "A1", "运单号")
	f.SetCellValue("缺失流水", "B1", "缺失流水类型")
	f.SetCellValue("缺失流水", "C1", "金额")
	f.SetCellValue("缺失流水", "D1", "流水数量")
	cellIndex := 2
	for _, v := range missing {
		f.SetCellValue("缺失流水", "A"+strconv.Itoa(cellIndex), v.WaybillNo)
		f.SetCellValue("缺失流水", "B"+strconv.Itoa(cellIndex), v.Reason)
		f.SetCellValue("缺失流水", "C"+strconv.Itoa(cellIndex), v.Amount)
		f.SetCellValue("缺失流水", "D"+strconv.Itoa(cellIndex), v.Count)
		cellIndex++
	}

	// 模拟补充流水记录
	cellIndex = 0
	f.NewSheet("模拟补充流水")
	for k, v := range excelRows {
		f.SetCellValue("模拟补充流水", "A"+strconv.Itoa(k+1), v[0])
		f.SetCellValue("模拟补充流水", "B"+strconv.Itoa(k+1), v[1])
		f.SetCellValue("模拟补充流水", "C"+strconv.Itoa(k+1), v[2])
		f.SetCellValue("模拟补充流水", "D"+strconv.Itoa(k+1), v[3])
		f.SetCellValue("模拟补充流水", "E"+strconv.Itoa(k+1), v[4])
		f.SetCellValue("模拟补充流水", "F"+strconv.Itoa(k+1), v[5])
		f.SetCellValue("模拟补充流水", "G"+strconv.Itoa(k+1), v[6])
		f.SetCellValue("模拟补充流水", "H"+strconv.Itoa(k+1), v[7])
		f.SetCellValue("模拟补充流水", "I"+strconv.Itoa(k+1), v[8])
		f.SetCellValue("模拟补充流水", "J"+strconv.Itoa(k+1), v[9])
		f.SetCellValue("模拟补充流水", "K"+strconv.Itoa(k+1), v[10])
		cellIndex = k + 1
	}
	for _, v := range missing {
		switch v.Count {
		case 0:
			for i := range 2 {
				f.SetCellValue("模拟补充流水", "A"+strconv.Itoa(cellIndex), v.WaybillNo)
				f.SetCellValue("模拟补充流水", "B"+strconv.Itoa(cellIndex), "")
				f.SetCellValue("模拟补充流水", "C"+strconv.Itoa(cellIndex), "")
				f.SetCellValue("模拟补充流水", "D"+strconv.Itoa(cellIndex), "")
				if i == 0 {
					f.SetCellValue("模拟补充流水", "E"+strconv.Itoa(cellIndex), v.Amount)
				} else {
					f.SetCellValue("模拟补充流水", "E"+strconv.Itoa(cellIndex), -v.Amount)
				}
				f.SetCellValue("模拟补充流水", "F"+strconv.Itoa(cellIndex), "")
				f.SetCellValue("模拟补充流水", "G"+strconv.Itoa(cellIndex), "")
				f.SetCellValue("模拟补充流水", "H"+strconv.Itoa(cellIndex), "")
				f.SetCellValue("模拟补充流水", "I"+strconv.Itoa(cellIndex), "")
				f.SetCellValue("模拟补充流水", "J"+strconv.Itoa(cellIndex), "")
				f.SetCellValue("模拟补充流水", "K"+strconv.Itoa(cellIndex), v.Reason)
				cellIndex++
			}
		case 1:
			f.SetCellValue("模拟补充流水", "A"+strconv.Itoa(cellIndex), v.WaybillNo)
			f.SetCellValue("模拟补充流水", "B"+strconv.Itoa(cellIndex), "")
			f.SetCellValue("模拟补充流水", "C"+strconv.Itoa(cellIndex), "")
			f.SetCellValue("模拟补充流水", "D"+strconv.Itoa(cellIndex), "")
			f.SetCellValue("模拟补充流水", "E"+strconv.Itoa(cellIndex), v.Amount)
			f.SetCellValue("模拟补充流水", "F"+strconv.Itoa(cellIndex), "")
			f.SetCellValue("模拟补充流水", "G"+strconv.Itoa(cellIndex), "")
			f.SetCellValue("模拟补充流水", "H"+strconv.Itoa(cellIndex), "")
			f.SetCellValue("模拟补充流水", "I"+strconv.Itoa(cellIndex), "")
			f.SetCellValue("模拟补充流水", "J"+strconv.Itoa(cellIndex), "")
			f.SetCellValue("模拟补充流水", "K"+strconv.Itoa(cellIndex), v.Reason)
			cellIndex++
		}
	}

	if err := f.SaveAs("./流水对账.xlsx"); err != nil {
		return err
	}

	return nil
}

// PaymentRecords 获取银行异常支付流水
func PaymentRecords() error {
	c := cmb.GetHszy()

	ctnkey := ""
	type RecordT struct {
		Accnbr string `json:"accnbr"`
		Autflg string `json:"autflg"`
		Ccynbr string `json:"ccynbr"`
		Dmanam string `json:"dmanam"`
		Dmanbr string `json:"dmanbr"`
		Rpyacc string `json:"rpyacc"`
		Rpynam string `json:"rpynam"`
		Trxamt string `json:"trxamt"`
		Trxdat string `json:"trxdat"`
		Trxdir string `json:"trxdir"`
		Trxnbr string `json:"trxnbr"`
		Trxtim string `json:"trxtim"`
		Trxtxt string `json:"trxtxt"`
	}

	re := regexp.MustCompile(`CYBM\d{16}`)
	mm := make(map[string][]RecordT)
	for {
		rr, err := c.SubAccountQueryRecord("", ctnkey, "********", "********")
		if err != nil {
			fmt.Println(err)
			os.Exit(1)
		}
		fmt.Println(rr.Ntdmthlsy)
		if len(rr.Ntdmthlsy) == 0 {
			break
		}
		ctnkey = rr.Ntdmthlsy[0].Ctnkey
		if ctnkey == "" {
			break
		}

		for _, v := range rr.Ntdmthlsz {
			tt := re.FindAllString(v.Trxtxt, -1)
			if len(tt) < 1 {
				continue
			}
			waybillNo := tt[0]

			mm[waybillNo] = append(mm[waybillNo], v)
		}
	}

	f := excelize.NewFile()
	defer f.Close()

	f.SetCellValue("Sheet1", "A1", "运单号")
	f.SetCellValue("Sheet1", "B1", "账号")
	f.SetCellValue("Sheet1", "C1", "记账子单元编号")
	f.SetCellValue("Sheet1", "D1", "记账子单元名称")
	f.SetCellValue("Sheet1", "E1", "交易金额")
	f.SetCellValue("Sheet1", "F1", "交易日期")
	f.SetCellValue("Sheet1", "G1", "交易方向")
	f.SetCellValue("Sheet1", "H1", "收方/付方账号")
	f.SetCellValue("Sheet1", "I1", "收方/付方名称")
	f.SetCellValue("Sheet1", "J1", "交易流水号")
	f.SetCellValue("Sheet1", "K1", "交易说明")

	cellIndex := 2
	for k, v := range mm {

		// 到付+立即付 两条支付计划 10 + 2 + 4 油卡+2 保险+2
		// 到付+预付 两条支付计划 16条流水 油卡+2 保险+2
		// 到付 一条支付计划 10条流水 油卡+2 保险+2
		// 油卡 2条流水
		// 保险 2条流水

		// 正常流程下 最少要有10条流水记录

		if len(v) >= 10 {
			continue
		}

		isPrePayContinue := false
		for _, vv := range v {
			if strings.Contains(vv.Trxtxt, "预付款") {
				if len(v) >= 6 {
					isPrePayContinue = true
				}
			}
		}
		if isPrePayContinue {
			continue
		}

		for _, vv := range v {
			f.SetCellValue("Sheet1", "A"+strconv.Itoa(cellIndex), k)
			f.SetCellValue("Sheet1", "B"+strconv.Itoa(cellIndex), vv.Accnbr)
			f.SetCellValue("Sheet1", "C"+strconv.Itoa(cellIndex), vv.Dmanbr)
			f.SetCellValue("Sheet1", "D"+strconv.Itoa(cellIndex), vv.Dmanam)
			f.SetCellValue("Sheet1", "E"+strconv.Itoa(cellIndex), vv.Trxamt)
			f.SetCellValue("Sheet1", "F"+strconv.Itoa(cellIndex), vv.Trxdat+" "+vv.Trxtim)
			f.SetCellValue("Sheet1", "G"+strconv.Itoa(cellIndex), vv.Trxdir)
			f.SetCellValue("Sheet1", "H"+strconv.Itoa(cellIndex), vv.Rpyacc)
			f.SetCellValue("Sheet1", "I"+strconv.Itoa(cellIndex), vv.Rpynam)
			f.SetCellValue("Sheet1", "J"+strconv.Itoa(cellIndex), vv.Trxnbr)
			f.SetCellValue("Sheet1", "K"+strconv.Itoa(cellIndex), vv.Trxtxt)
			cellIndex++
		}
	}

	if err := f.SaveAs("异常流水记录.xlsx"); err != nil {
		return err
	}
	return nil
}
