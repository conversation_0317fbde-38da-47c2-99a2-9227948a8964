package table

import (
	"encoding/json"
	"fmt"
	"strings"
	"wlhy/model"
	"wlhy/thirdparty/jinron"
	"wlhy/toolbox/logger"

	"github.com/xuri/excelize/v2"
)

func Etc() {
	type NoteT struct {
		TransportationNumber string `gorm:"column:transportation_number"`
		CompanyName          string `gorm:"column:company_name"`
		EtcResult            string `gorm:"column:etc_result"`
	}

	var notes []NoteT
	if err := model.DB.Debug().Table("tms_transport_note AS a").
		Joins("JOIN tms_shipper AS b ON a.create_company_id = b.company_id").
		Select([]string{"a.transportation_number", "b.company_name", "a.etc_result"}).
		Where("a.is_etc_invoice IN (?)", []int{1, 8}).
		Where("a.etc_channel = ?", 2).
		Where("a.etc_result IS NOT NULL AND a.etc_result != ''").
		Where("b.shipper_type = ? AND b.is_delete = ?", 1, 0).
		Scan(&notes).Error; err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	type InvoiceT struct {
		TransportationNumber string
		CompanyName          string
	}
	invoiceMap := make(map[string]InvoiceT)
	for _, note := range notes {
		if !json.Valid([]byte(note.EtcResult)) {
			continue
		}

		var result jinron.QueryInvoiceResp
		if err := json.Unmarshal([]byte(note.EtcResult), &result); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}

		for _, v := range result.Data.Result {
			invoiceMap[v.InvoiceNum] = InvoiceT{
				TransportationNumber: note.TransportationNumber,
				CompanyName:          note.CompanyName,
			}
		}
	}

	f, err := excelize.OpenFile("etc.xlsx")
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	rows, err := f.GetRows("Sheet1")
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	for key, row := range rows {
		if key == 0 {
			continue
		}
		if len(row) < 5 {
			continue
		}

		invoiceNumber := strings.TrimSpace(row[4])

		f.SetCellValue("Sheet1", fmt.Sprintf("V%d", key+1), invoiceMap[invoiceNumber].TransportationNumber)
		f.SetCellValue("Sheet1", fmt.Sprintf("W%d", key+1), invoiceMap[invoiceNumber].CompanyName)
	}

	f.SaveAs("etc-with-company.xlsx")
}
