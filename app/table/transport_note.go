package table

import (
	"database/sql"
	"strconv"
	"strings"
	"time"
	"wlhy/model"
	"wlhy/toolbox"
	"wlhy/toolbox/logger"

	"github.com/xuri/excelize/v2"
)

// TransportNote 生成运单数据excel
func TransportNote(companyNames []string, startTime, endTime string) {
	if endTime == "" {
		endTime = time.Now().Format("2006-01-02 15:04:05")
	}
	db := model.DB.Table("tms_transport_note AS a").
		Joins("JOIN tms_order_history AS b ON a.order_id = b.id AND a.order_version_no = b.version_no").
		Joins("JOIN tms_shipper AS c ON b.create_company_id = c.shipper_id").
		Joins("JOIN tms_vehicle AS d ON a.transportation_plate = d.vehicle_license_number").
		Joins("LEFT JOIN tms_trailer_info AS e ON d.trailer_info_id = e.id").
		Select([]string{
			"a.id",
			"b.goods_number",
			"a.transportation_number",
			"a.waybill_status",
			"b.radio_value",
			"a.univalent_should",
			"b.owner_unit_price",
			"a.freight_gross",
			"a.freight_amount",
			"a.service_charge",
			"b.goods_type",
			"b.goods_name",
			"b.line_name",
			"b.loading_name",
			"b.loading_address",
			"a.loading_time",
			"a.loading_number",
			"b.unload_name",
			"b.unload_address",
			"a.unload_time",
			"a.unload_number",
			"a.transportation_driver",
			"a.transportation_phone",
			"a.transportation_plate",
			"c.company_name",
			"b.creater_name",
			"b.creater_phone",
			"b.create_time",
			"b.is_road_loss",
			"b.road_loss_mode",
			"b.goods_value",
			"a.road_loss_settlement_method",
			"a.road_loss_state",
			"a.loss_weight_of_goods",
			"deduction_of_loss_expenses",
			"a.freight_accounting_status",
			"b.remarks",
			"a.service_fee_paid",
			"a.pay_time",
			"a.oil_card_online",
			"a.actualmileage",
			"a.freight_paid",
			"a.oil_card_amount",
			"a.etc_fee",
			"d.vehicle_type_name",
			"d.nuclear_load_weight",
			"e.nuclear_load_weight AS trailer_nuclear_load_weight",
			"a.actual_haul_distance",
		}).
		Where("a.sys_org_code = ?", "A03").
		Where("a.is_region_out = ?", 0).
		Where("a.is_delete = ?", 0).
		Where("a.waybill_status IN (?)", []int{1, 2, 3, 4, 5, 8, 9}).
		Where("c.shipper_type = ? AND c.is_delete = ?", 1, 0)

	if companyNames != nil {
		db.Where("c.company_name IN (?)", companyNames)
	}
	if startTime != "" {
		db.Where("a.finished_time >= ?", startTime)
	}
	db.Where("a.finished_time <= ?", endTime)

	type firstCreateTimeT struct {
		CreateTime time.Time `gorm:"column:create_time"`
	}
	var firstCreateTime firstCreateTimeT
	orderDb := db
	orderDb.Order("a.create_time").Scan(&firstCreateTime)

	rows, err := db.Order("a.finished_time DESC").Rows()

	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	// 获取支付计划数据
	planRows, err := model.DB.Table("tms_transport_pay_plan").
		Select([]string{"waybill_No", "paytype", "transport_Amount", "insurance_Amount", "commission_Amount"}).
		Where("create_time BETWEEN ? AND ?", startTime, endTime).
		Where("is_delete = ?", 0).
		Rows()
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	type planT struct {
		PayPlanType      int
		TransportAmount  float64
		InsuranceAmount  float64
		CommissionAmount float64
	}
	plans := make(map[string]planT)
	for planRows.Next() {
		var waybillNo string
		var payType int
		var transportAmount float64
		var insuranceAmount float64
		var commissionAmount float64

		if err := planRows.Scan(&waybillNo, &payType, &transportAmount, &insuranceAmount, &commissionAmount); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}

		plans[waybillNo] = planT{
			PayPlanType:      payType,
			TransportAmount:  transportAmount,
			InsuranceAmount:  insuranceAmount,
			CommissionAmount: commissionAmount,
		}
	}

	// 获取运单开票数据
	invoiceOpen := make(map[string]time.Time)
	invoiceRows, err := model.DB.Table("tms_invoice_list").
		Select([]string{"transportation_ids", "manager_time"}).
		Where("invoice_state = ?", 2).
		Where("is_delete = ?", 0).
		Where("create_time >= ?", startTime).
		Rows()
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	for invoiceRows.Next() {
		var transportationIds string
		var managerTime sql.NullTime
		if err := invoiceRows.Scan(&transportationIds, &managerTime); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}
		if transportationIds != "" {
			notes := strings.Split(transportationIds, ",")
			for _, v := range notes {
				if v == " " || v == "," || v == "" {
					continue
				}
				if managerTime.Valid {
					invoiceOpen[v] = managerTime.Time
				}
			}
		}
	}

	// 获取保险数据
	insuranceData := make(map[string]map[string]any)
	insuranceRows, err := model.DB.Table("tms_transport_note_insurance_record AS a").
		Joins("JOIN tms_transport_note AS b ON a.transport_note_number = b.transportation_number").
		Joins("JOIN tms_order_history AS c ON b.order_id = c.id AND b.order_version_no = c.version_no").
		Select([]string{"a.transport_note_number", "a.insurance_time", "a.thirdparty_insurance_amount", "a.insurance_company", "b.freight_amount", "c.goods_type", "a.shipper_name"}).
		Where("a.insurance_status = ?", 1).
		Where("a.is_delete = ?", 0).
		Where("a.create_time BETWEEN ? AND ?", firstCreateTime.CreateTime.Format("2006-01-02 15:04:05"), endTime).
		Rows()
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	for insuranceRows.Next() {
		var transportNoteNumber string
		var insuranceTime time.Time
		var thirdpartyInsuranceAmount sql.NullFloat64
		var insuranceCompany int
		var freightAmount float64
		var insuranceGoodsType string
		var shipperName string
		if err := insuranceRows.Scan(&transportNoteNumber, &insuranceTime, &thirdpartyInsuranceAmount, &insuranceCompany, &freightAmount, &insuranceGoodsType, &shipperName); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}

		if (!thirdpartyInsuranceAmount.Valid || thirdpartyInsuranceAmount.Float64 == 0) && insuranceCompany == 1 {
			sumPremium := (freightAmount * 100) * (0.0018 * 10000) / (100 * 10000)
			thirdpartyInsuranceAmount.Float64 = sumPremium
		}

		if (!thirdpartyInsuranceAmount.Valid || thirdpartyInsuranceAmount.Float64 == 0) && insuranceCompany == 2 {
			// 保费
			var premium float64
			if insuranceGoodsType == "煤炭" {
				premium = 1.00
			} else {
				tmpPremium := toolbox.RoundToDecimal(freightAmount*0.0016, 2)
				if tmpPremium < 3 {
					premium = 3.00
				} else {
					premium = tmpPremium
				}
			}

			// 特殊规则
			// 内蒙古天壹成信环保科技有限公司 石英砂
			if shipperName == "内蒙古天壹成信环保科技有限公司" && insuranceGoodsType == "石英砂" {
				premium = 3.00
			}

			thirdpartyInsuranceAmount.Float64 = premium
		}

		insuranceCompanyDisplay := "太平"
		if insuranceCompany == 2 {
			insuranceCompanyDisplay = "东海"
		}
		insuranceData[transportNoteNumber] = map[string]any{
			"insurance_time":              insuranceTime.Format("2006-01-02 15:04:05"),
			"thirdparty_insurance_amount": thirdpartyInsuranceAmount.Float64,
			"insurance_company":           insuranceCompanyDisplay,
		}
	}

	f := excelize.NewFile()
	defer func() {
		if err := f.Close(); err != nil {
			logger.Stdout.Error(err.Error())
		}
	}()

	// Create a new sheet.
	index, err := f.NewSheet("Sheet1")
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	// Set value of a cell.
	f.SetCellValue("Sheet1", "A1", "货单号")
	f.SetCellValue("Sheet1", "B1", "运单号")
	f.SetCellValue("Sheet1", "C1", "运单状态")
	f.SetCellValue("Sheet1", "D1", "结算方式")
	f.SetCellValue("Sheet1", "E1", "司机单价")
	f.SetCellValue("Sheet1", "F1", "货主单价")
	f.SetCellValue("Sheet1", "G1", "应付总运费")
	f.SetCellValue("Sheet1", "H1", "运费金额")
	f.SetCellValue("Sheet1", "I1", "服务费金额")
	f.SetCellValue("Sheet1", "J1", "货物大类型")
	f.SetCellValue("Sheet1", "K1", "货物小类型")
	f.SetCellValue("Sheet1", "L1", "线路名称")
	f.SetCellValue("Sheet1", "M1", "装货地址")
	f.SetCellValue("Sheet1", "N1", "装货时间")
	f.SetCellValue("Sheet1", "O1", "装货重量")
	f.SetCellValue("Sheet1", "P1", "卸货地址")
	f.SetCellValue("Sheet1", "Q1", "卸货时间")
	f.SetCellValue("Sheet1", "R1", "卸货重量")
	f.SetCellValue("Sheet1", "S1", "运输司机姓名")
	f.SetCellValue("Sheet1", "T1", "运输司机电话")
	f.SetCellValue("Sheet1", "U1", "运输车牌号")
	f.SetCellValue("Sheet1", "V1", "公司名称")
	f.SetCellValue("Sheet1", "W1", "货单创建人")
	f.SetCellValue("Sheet1", "X1", "货单创建人电话")
	f.SetCellValue("Sheet1", "Y1", "创建日期")
	f.SetCellValue("Sheet1", "Z1", "是否开启路耗")
	f.SetCellValue("Sheet1", "AA1", "路耗模式")
	f.SetCellValue("Sheet1", "AB1", "货物价值")
	f.SetCellValue("Sheet1", "AC1", "路耗结算方式")
	f.SetCellValue("Sheet1", "AD1", "路耗状态")
	f.SetCellValue("Sheet1", "AE1", "亏损重量")
	f.SetCellValue("Sheet1", "AF1", "亏损费用")
	f.SetCellValue("Sheet1", "AG1", "运费核算状态")
	f.SetCellValue("Sheet1", "AH1", "备注")
	f.SetCellValue("Sheet1", "AI1", "已支付服务费")
	f.SetCellValue("Sheet1", "AJ1", "支付时间")
	f.SetCellValue("Sheet1", "AK1", "加油宝费用")
	f.SetCellValue("Sheet1", "AL1", "实际里程")
	f.SetCellValue("Sheet1", "AM1", "已支付运费")
	f.SetCellValue("Sheet1", "AN1", "电子油卡金额")
	f.SetCellValue("Sheet1", "AO1", "预付金额")
	f.SetCellValue("Sheet1", "AP1", "保险费用")
	f.SetCellValue("Sheet1", "AQ1", "是否开票")
	f.SetCellValue("Sheet1", "AR1", "开票时间")
	f.SetCellValue("Sheet1", "AS1", "保险公司")
	f.SetCellValue("Sheet1", "AT1", "投保时间")
	f.SetCellValue("Sheet1", "AU1", "投保金额")
	f.SetCellValue("Sheet1", "AV1", "手续费")
	f.SetCellValue("Sheet1", "AW1", "ETC金额")
	f.SetCellValue("Sheet1", "AX1", "车辆类型")
	f.SetCellValue("Sheet1", "AY1", "核定载质量")
	f.SetCellValue("Sheet1", "AZ1", "是否超载")
	f.SetCellValue("Sheet1", "BA1", "实际运输距离")

	// Set active sheet of the workbook.
	f.SetActiveSheet(index)

	start := 2
	for rows.Next() {
		var id string
		var goodsNumber sql.NullString
		var transportationNumber sql.NullString
		var waybillStatus sql.NullInt64
		var radioValue sql.NullInt64
		var univalentShould sql.NullFloat64
		var ownerUnitPrice sql.NullFloat64
		var freightGross sql.NullFloat64
		var freightAmount sql.NullFloat64
		var serviceCharge sql.NullFloat64
		var goodsType sql.NullString
		var goodsName sql.NullString
		var lineName sql.NullString
		var loadingName sql.NullString
		var loadingAddress sql.NullString
		var loadingTime sql.NullTime
		var loadingNumber sql.NullFloat64
		var unloadName sql.NullString
		var unloadAddress sql.NullString
		var unloadTime sql.NullTime
		var unloadNumber sql.NullString
		var transportationDriver sql.NullString
		var transportationPhone sql.NullString
		var transportationPlate sql.NullString
		var companyName sql.NullString
		var createrName sql.NullString
		var createrPhone sql.NullString
		var createTime sql.NullTime
		var isRoadLoss sql.NullInt64
		var roadLossMode sql.NullInt64
		var goodsValue sql.NullFloat64
		var roadLossSettlementMethod sql.NullInt64
		var roadLossState sql.NullInt64
		var lossWeightOfGoods sql.NullFloat64
		var deductionOfLossExpenses sql.NullFloat64
		var freightAccountingStatus sql.NullInt64
		var remarks sql.NullString
		var serviceFeePaid sql.NullFloat64
		var payTime sql.NullTime
		var oilCardOnline sql.NullFloat64
		var actualmileage sql.NullFloat64
		var freightPaid sql.NullFloat64
		var oilCardAmount sql.NullFloat64
		var etcFee sql.NullFloat64
		var vehicleTypeName string
		var trailerNuclearLoadWeight sql.NullFloat64
		var nuclearLoadWeight sql.NullFloat64
		var actualHaulDistance sql.NullFloat64

		if err := rows.Scan(
			&id,
			&goodsNumber,
			&transportationNumber,
			&waybillStatus,
			&radioValue,
			&univalentShould,
			&ownerUnitPrice,
			&freightGross,
			&freightAmount,
			&serviceCharge,
			&goodsType,
			&goodsName,
			&lineName,
			&loadingName,
			&loadingAddress,
			&loadingTime,
			&loadingNumber,
			&unloadName,
			&unloadAddress,
			&unloadTime,
			&unloadNumber,
			&transportationDriver,
			&transportationPhone,
			&transportationPlate,
			&companyName,
			&createrName,
			&createrPhone,
			&createTime,
			&isRoadLoss,
			&roadLossMode,
			&goodsValue,
			&roadLossSettlementMethod,
			&roadLossState,
			&lossWeightOfGoods,
			&deductionOfLossExpenses,
			&freightAccountingStatus,
			&remarks,
			&serviceFeePaid,
			&payTime,
			&oilCardOnline,
			&actualmileage,
			&freightPaid,
			&oilCardAmount,
			&etcFee,
			&vehicleTypeName,
			&trailerNuclearLoadWeight,
			&nuclearLoadWeight,
			&actualHaulDistance,
		); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}

		startS := strconv.Itoa(start)

		transportationStatus := ""
		switch waybillStatus.Int64 {
		case 0:
			transportationStatus = "待接单"
		case 1:
			transportationStatus = "待确认"
		case 2:
			transportationStatus = "运输中"
		case 3:
			transportationStatus = "待支付"
		case 4:
			transportationStatus = "待评价"
		case 5:
			transportationStatus = "已评价"
		case 6:
			transportationStatus = "已取消"
		case 7:
			transportationStatus = "已关闭 "
		case 8:
			transportationStatus = "已完成"
		case 9:
			transportationStatus = "待验收"
		}

		radioValueS := "按车次结算"
		if radioValue.Int64 == 2 {
			radioValueS = "按重量结算"
		}

		f.SetCellValue("Sheet1", "A"+startS, goodsNumber.String)
		f.SetCellValue("Sheet1", "B"+startS, transportationNumber.String)
		f.SetCellValue("Sheet1", "C"+startS, transportationStatus)
		f.SetCellValue("Sheet1", "D"+startS, radioValueS)
		f.SetCellValue("Sheet1", "E"+startS, univalentShould.Float64)
		f.SetCellValue("Sheet1", "F"+startS, ownerUnitPrice.Float64)
		f.SetCellValue("Sheet1", "G"+startS, freightGross.Float64)
		f.SetCellValue("Sheet1", "H"+startS, freightAmount.Float64)
		f.SetCellValue("Sheet1", "I"+startS, serviceCharge.Float64)
		f.SetCellValue("Sheet1", "J"+startS, goodsType.String)
		f.SetCellValue("Sheet1", "K"+startS, goodsName.String)
		f.SetCellValue("Sheet1", "L"+startS, lineName.String)

		if strings.Contains(loadingAddress.String, loadingName.String) {
			loadingAddress.String = strings.ReplaceAll(loadingAddress.String, loadingName.String, "")
		}
		f.SetCellValue("Sheet1", "M"+startS, loadingName.String+loadingAddress.String)
		f.SetCellValue("Sheet1", "N"+startS, loadingTime.Time.Format("2006-01-02 15:04:05"))
		f.SetCellValue("Sheet1", "O"+startS, loadingNumber.Float64)

		if strings.Contains(unloadAddress.String, unloadName.String) {
			unloadAddress.String = strings.ReplaceAll(unloadAddress.String, unloadName.String, "")
		}
		f.SetCellValue("Sheet1", "P"+startS, unloadName.String+unloadAddress.String)
		f.SetCellValue("Sheet1", "Q"+startS, unloadTime.Time.Format("2006-01-02 15:04:05"))
		f.SetCellValue("Sheet1", "R"+startS, unloadNumber.String)
		f.SetCellValue("Sheet1", "S"+startS, transportationDriver.String)
		f.SetCellValue("Sheet1", "T"+startS, transportationPhone.String)
		f.SetCellValue("Sheet1", "U"+startS, transportationPlate.String)
		f.SetCellValue("Sheet1", "V"+startS, companyName.String)
		f.SetCellValue("Sheet1", "W"+startS, createrName.String)
		f.SetCellValue("Sheet1", "X"+startS, createrPhone.String)
		f.SetCellValue("Sheet1", "Y"+startS, createTime.Time.Format("2006-01-02 15:04:05"))

		if isRoadLoss.Int64 == 0 {
			f.SetCellValue("Sheet1", "Z"+startS, "否")
		} else {
			f.SetCellValue("Sheet1", "Z"+startS, "是")
		}

		switch roadLossMode.Int64 {
		case 0:
			f.SetCellValue("Sheet1", "AA"+startS, "无")
		case 1:
			f.SetCellValue("Sheet1", "AA"+startS, "按比例")
		case 2:
			f.SetCellValue("Sheet1", "AA"+startS, "按数量")
		}

		f.SetCellValue("Sheet1", "W"+startS, goodsValue.Float64)

		switch roadLossSettlementMethod.Int64 {
		case 1:
			f.SetCellValue("Sheet1", "AC"+startS, "取装货重量")
		case 2:
			f.SetCellValue("Sheet1", "AC"+startS, "取卸货重量")
		case 3:
			f.SetCellValue("Sheet1", "AC"+startS, "装卸货取大")
		case 4:
			f.SetCellValue("Sheet1", "AC"+startS, "装卸货取小")
		}

		switch roadLossState.Int64 {
		case 0:
			f.SetCellValue("Sheet1", "AD"+startS, "不亏吨(运输路耗=允许路耗)")
		case 1:
			f.SetCellValue("Sheet1", "AD"+startS, "亏吨但不超过允许亏吨数(运输路耗<允许路耗)")
		case 2:
			f.SetCellValue("Sheet1", "AD"+startS, "亏吨并且超过允许亏吨数（运输路耗>允许路耗）")
		case 3:
			f.SetCellValue("Sheet1", "AD"+startS, "涨吨(运输路耗<0)）")
		}

		f.SetCellValue("Sheet1", "AE"+startS, lossWeightOfGoods.Float64)
		f.SetCellValue("Sheet1", "AF"+startS, deductionOfLossExpenses.Float64)

		switch freightAccountingStatus.Int64 {
		case 0:
			f.SetCellValue("Sheet1", "AG"+startS, "无需核算")
		case 1:
			f.SetCellValue("Sheet1", "AG"+startS, "待核算")
		case 2:
			f.SetCellValue("Sheet1", "AG"+startS, "已核算")
		}

		f.SetCellValue("Sheet1", "AH"+startS, remarks.String)
		f.SetCellValue("Sheet1", "AI"+startS, serviceFeePaid.Float64)
		f.SetCellValue("Sheet1", "AJ"+startS, payTime.Time.Format("2006-01-02 15:04:05"))
		f.SetCellValue("Sheet1", "AK"+startS, oilCardOnline.Float64)
		f.SetCellValue("Sheet1", "AL"+startS, actualmileage.Float64)
		f.SetCellValue("Sheet1", "AM"+startS, freightPaid.Float64)
		f.SetCellValue("Sheet1", "AN"+startS, oilCardAmount.Float64)

		if plans[transportationNumber.String].PayPlanType == 2 {
			f.SetCellValue("Sheet1", "AO"+startS, plans[transportationNumber.String].PayPlanType)
		} else {
			f.SetCellValue("Sheet1", "AO"+startS, 0)
		}

		if _, ok := plans[transportationNumber.String]; ok {
			f.SetCellValue("Sheet1", "AP"+startS, plans[transportationNumber.String].InsuranceAmount)
		} else {
			f.SetCellValue("Sheet1", "AP"+startS, 0)
		}

		if _, ok := invoiceOpen[id]; ok {
			f.SetCellValue("Sheet1", "AQ"+startS, "已开票")
			f.SetCellValue("Sheet1", "AR"+startS, invoiceOpen[id].Format("2006-01-02 15:04:05"))
		} else {
			f.SetCellValue("Sheet1", "AQ"+startS, "未开票")
			f.SetCellValue("Sheet1", "AR"+startS, "")
		}

		if _, ok := insuranceData[transportationNumber.String]; ok {
			f.SetCellValue("Sheet1", "AS"+startS, insuranceData[transportationNumber.String]["insurance_company"])
			f.SetCellValue("Sheet1", "AT"+startS, insuranceData[transportationNumber.String]["insurance_time"])
			f.SetCellValue("Sheet1", "AU"+startS, insuranceData[transportationNumber.String]["thirdparty_insurance_amount"])
		} else {
			f.SetCellValue("Sheet1", "AS"+startS, "")
			f.SetCellValue("Sheet1", "AT"+startS, "")
			f.SetCellValue("Sheet1", "AU"+startS, 0)
		}

		if _, ok := plans[transportationNumber.String]; ok {
			f.SetCellValue("Sheet1", "AV"+startS, plans[transportationNumber.String].CommissionAmount)
		} else {
			f.SetCellValue("Sheet1", "AV"+startS, 0)
		}

		if etcFee.Valid && etcFee.Float64 > 0 {
			f.SetCellValue("Sheet1", "AW"+startS, etcFee.Float64/100)
		} else {
			f.SetCellValue("Sheet1", "AW"+startS, 0)
		}

		f.SetCellValue("Sheet1", "AX"+startS, vehicleTypeName)
		currentNuclearLoadWeight := nuclearLoadWeight.Float64
		if strings.Contains(vehicleTypeName, "重型半挂牵引车") {
			currentNuclearLoadWeight = nuclearLoadWeight.Float64
			f.SetCellValue("Sheet1", "AY"+startS, trailerNuclearLoadWeight.Float64)
		} else {
			currentNuclearLoadWeight = trailerNuclearLoadWeight.Float64
			f.SetCellValue("Sheet1", "AY"+startS, nuclearLoadWeight.Float64)
		}
		if loadingNumber.Float64 > currentNuclearLoadWeight {
			f.SetCellValue("Sheet1", "AZ"+startS, "是")
		} else {
			f.SetCellValue("Sheet1", "AZ"+startS, "否")
		}
		f.SetCellValue("Sheet1", "BA"+startS, actualHaulDistance.Float64)

		start++
	}

	if err := f.SaveAs("运单数据.xlsx"); err != nil {
		logger.Stdout.Error(err.Error())
	}

	logger.Stdout.Info("运单数据生成成功")
}
