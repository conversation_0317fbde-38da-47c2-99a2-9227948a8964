package table

import (
	"fmt"
	"math"
	"strconv"
	"strings"
	"time"
	"wlhy/model"
	"wlhy/toolbox/logger"

	"github.com/samber/lo"
	"github.com/xuri/excelize/v2"
)

// FinanceBankRecords 处理银行记录
func FinanceBankRecords(excelName string, filename string) error {
	f, err := excelize.OpenFile(excelName)
	if err != nil {
		logger.Stdout.Error(err.<PERSON>rror())
		return err
	}
	defer f.Close()

	rows, err := f.GetRows("Sheet0")
	if err != nil {
		logger.Stdout.Error(err.Error())
		return err
	}

	companies := make(map[string]bool)
	results, err := model.DB.Table("tms_shipper").Select([]string{"company_name"}).Where("shipper_type = ? AND is_delete = ?", 1, 0).Rows()
	if err != nil {
		logger.Stdout.Error(err.<PERSON>rror())
		return err
	}
	for results.Next() {
		var companyName string
		if err := results.Scan(&companyName); err != nil {
			panic(err)
		}
		if companyName == "赤峰烛煦辰泽热力有限责任公司" {
			companyName = "赤峰烛煦辰泽热力有限公司"
		}
		if companyName == "克什克腾旗新城热力有限责任公司林业棚户区供热分公司" {
			companyName = "林业棚户区供热分公司"
		}
		if companyName == "内蒙古磐石建材商贸有限公司乌兰察布市察哈尔右翼后旗建材分公司" {
			companyName = "磐石建材商贸有限公司建材分公司"
		}

		companies[companyName] = true
	}

	m := make(map[string][][]string)
	for _, row := range rows {
		if row[3] == "赤峰烛煦辰泽热力有限责任公司" {
			row[3] = "赤峰烛煦辰泽热力有限公司"
		}
		if row[3] == "克什克腾旗新城热力有限责任公司林业棚户区供热分公司" {
			row[3] = "林业棚户区供热分公司"
		}
		if row[3] == "内蒙古磐石建材商贸有限公司乌兰察布市察哈尔右翼后旗建材分公司" {
			row[3] = "磐石建材商贸有限公司建材分公司"
		}

		companyName := row[3]
		if _, ok := companies[companyName]; !ok {
			continue
		}

		key := row[3] + row[0]
		if _, ok := m[key]; !ok {
			m[key] = [][]string{row}
		} else {
			m[key] = append(m[key], row)
		}
	}

	filter := []string{"付款6666", "付款司机", "服务费", "保险费", "手续费"}
	details := make(map[string]map[string]float64)
	for _, row := range rows {
		transportationNumber := row[10]
		for _, v := range filter {
			transportationNumber = strings.ReplaceAll(transportationNumber, v, "")
		}

		if _, ok := details[transportationNumber]; !ok {
			details[transportationNumber] = map[string]float64{
				"driver":    0,
				"profit":    0,
				"insurance": 0,
			}
		}

		amount := strings.ReplaceAll(strings.TrimSpace(row[4]), ",", "")
		account := strings.TrimSpace(row[9])
		remark := strings.TrimSpace(row[10])
		if strings.Contains(remark, "付款司机") && account == "**********" {
			driverAmount, _ := strconv.ParseFloat(amount, 64)
			details[transportationNumber]["driver"] = math.Abs(driverAmount)
		}
		if strings.Contains(remark, "服务费") && account == "**********" {
			profitAmount, _ := strconv.ParseFloat(amount, 64)
			details[transportationNumber]["profit"] = math.Abs(profitAmount)
		}
		if strings.Contains(remark, "手续费") && account == "**********" {
			handlingFees, _ := strconv.ParseFloat(amount, 64)
			details[transportationNumber]["profit"] += math.Abs(handlingFees)
		}
		if strings.Contains(remark, "保险费") && account == "**********" {
			insuranceAmount, _ := strconv.ParseFloat(amount, 64)
			details[transportationNumber]["insurance"] = math.Abs(insuranceAmount)
		}
	}

	// 排序
	for k, v := range m {
		j := 0
		for i := 0; i < len(v); i++ {
			if strings.Contains(v[i][4], "+") {
				m[k][j], m[k][i] = m[k][i], m[k][j]
				j++
			}
		}
	}

	s1, _ := f.NewStyle(&excelize.Style{
		Border: []excelize.Border{
			{Type: "left", Color: "#000000", Style: 1},
			{Type: "right", Color: "#000000", Style: 1},
			{Type: "top", Color: "#000000", Style: 1},
			{Type: "bottom", Color: "#000000", Style: 1},
		},
		Font: &excelize.Font{
			Size: 10,
		},
		Alignment: &excelize.Alignment{
			Horizontal: "left",
			Vertical:   "center",
			WrapText:   true,
		},
	})

	for k, v := range m {
		i, err := f.NewSheet(k)
		if err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}
		f.SetActiveSheet(i)

		f.SetCellValue(k, "A1", "交易日期")
		f.SetCellValue(k, "B1", "交易时间")
		f.SetCellValue(k, "C1", "记账子单元编号")
		f.SetCellValue(k, "D1", "记账子单元户名")
		f.SetCellValue(k, "E1", "交易金额")
		f.SetCellValue(k, "F1", "余额")
		f.SetCellValue(k, "G1", "币种")
		f.SetCellValue(k, "H1", "记账方式")
		f.SetCellValue(k, "I1", "收(付)方名称")
		f.SetCellValue(k, "J1", "收(付)方账号")
		f.SetCellValue(k, "K1", "摘要")
		f.SetCellValue(k, "L1", "收(付)方开户行")
		f.SetCellValue(k, "M1", "收(付)方开户行行号")
		f.SetCellValue(k, "N1", "收(付)方开户地")
		f.SetCellValue(k, "O1", "交易流水号")

		var total float64
		var driverTotal float64
		var profitTotal float64
		var insuranceTotal float64
		max := 0
		for i, row := range v {
			f.SetCellValue(k, "A"+fmt.Sprintf("%d", i+2), row[0])
			f.SetCellValue(k, "B"+fmt.Sprintf("%d", i+2), row[1])
			f.SetCellValue(k, "C"+fmt.Sprintf("%d", i+2), row[2])
			f.SetCellValue(k, "D"+fmt.Sprintf("%d", i+2), row[3])
			f.SetCellValue(k, "E"+fmt.Sprintf("%d", i+2), row[4])
			f.SetCellValue(k, "F"+fmt.Sprintf("%d", i+2), row[5])
			f.SetCellValue(k, "G"+fmt.Sprintf("%d", i+2), row[6])
			f.SetCellValue(k, "H"+fmt.Sprintf("%d", i+2), row[7])
			f.SetCellValue(k, "I"+fmt.Sprintf("%d", i+2), row[8])
			f.SetCellValue(k, "J"+fmt.Sprintf("%d", i+2), row[9])
			f.SetCellValue(k, "K"+fmt.Sprintf("%d", i+2), row[10])
			f.SetCellValue(k, "L"+fmt.Sprintf("%d", i+2), row[11])
			f.SetCellValue(k, "M"+fmt.Sprintf("%d", i+2), row[12])
			f.SetCellValue(k, "N"+fmt.Sprintf("%d", i+2), row[13])
			f.SetCellValue(k, "O"+fmt.Sprintf("%d", i+2), row[14])
			max = i + 2
			if strings.Contains(row[4], "-") {
				money, _ := strconv.ParseFloat(strings.ReplaceAll(strings.ReplaceAll(row[4], "-", ""), ",", ""), 64)
				total += money
			}

			transportationNumber := row[10]
			for _, v := range filter {
				transportationNumber = strings.ReplaceAll(transportationNumber, v, "")
			}
			driverTotal += details[transportationNumber]["driver"]
			profitTotal += details[transportationNumber]["profit"]
			insuranceTotal += details[transportationNumber]["insurance"]
		}

		f.SetCellValue(k, "E"+fmt.Sprintf("%d", max+1), fmt.Sprintf("-%0.2f", total))

		max += 2
		f.SetCellValue(k, "D"+fmt.Sprintf("%d", max), "司机运费")
		f.SetCellValue(k, "E"+fmt.Sprintf("%d", max), fmt.Sprintf("-%0.2f", driverTotal))

		max++
		f.SetCellValue(k, "D"+fmt.Sprintf("%d", max), "平台运费")
		f.SetCellValue(k, "E"+fmt.Sprintf("%d", max), fmt.Sprintf("-%0.2f", profitTotal))

		max++
		f.SetCellValue(k, "D"+fmt.Sprintf("%d", max), "保险费")
		f.SetCellValue(k, "E"+fmt.Sprintf("%d", max), fmt.Sprintf("-%0.2f", insuranceTotal))

		// 设置样式
		f.SetCellStyle(k, "A1", "O"+strconv.Itoa(max+1), s1)
	}

	if filename == "" {
		filename = excelName
	}

	if err := f.SaveAs(filename); err != nil {
		logger.Stdout.Error(err.Error())
		return err
	}
	return nil
}

// 生成财务凭证导入excel
func FinanceImportExcel(excelName string, serialNumber int, prefix string) (int, error) {
	f, err := excelize.OpenFile(excelName)
	if err != nil {
		logger.Stdout.Error(err.Error())
		return 0, err
	}
	defer f.Close()

	rows, err := f.GetRows("Sheet0")
	if err != nil {
		logger.Stdout.Error(err.Error())
		return 0, err
	}

	companies := make(map[string]bool)
	results, err := model.DB.Table("tms_shipper").Select([]string{"company_name"}).Where("shipper_type = ? AND is_delete = ?", 1, 0).Rows()
	if err != nil {
		logger.Stdout.Error(err.Error())
		return 0, err
	}
	for results.Next() {
		var companyName string
		if err := results.Scan(&companyName); err != nil {
			panic(err)
		}
		if companyName == "克什克腾旗新城热力有限责任公司林业棚户区供热分公司" {
			companyName = "林业棚户区供热分公司"
		}
		if companyName == "内蒙古磐石建材商贸有限公司乌兰察布市察哈尔右翼后旗建材分公司" {
			companyName = "磐石建材商贸有限公司建材分公司"
		}

		companies[companyName] = true
	}

	currentCompanies := []string{}
	m := make(map[string][][]string)
	for _, row := range rows {
		if row[3] == "克什克腾旗新城热力有限责任公司林业棚户区供热分公司" {
			row[3] = "林业棚户区供热分公司"
		}
		if row[3] == "内蒙古磐石建材商贸有限公司乌兰察布市察哈尔右翼后旗建材分公司" {
			row[3] = "磐石建材商贸有限公司建材分公司"
		}

		companyName := row[3]
		if _, ok := companies[companyName]; !ok {
			continue
		}
		currentCompanies = append(currentCompanies, companyName)

		key := row[3] + row[0]
		if _, ok := m[key]; !ok {
			m[key] = [][]string{row}
		} else {
			m[key] = append(m[key], row)
		}
	}
	currentCompanies = lo.Uniq(currentCompanies)

	filter := []string{"付款6666", "付款司机", "服务费", "保险费", "手续费"}
	details := make(map[string]map[string]float64)
	for _, row := range rows {
		transportationNumber := row[10]
		for _, v := range filter {
			transportationNumber = strings.ReplaceAll(transportationNumber, v, "")
		}

		if _, ok := details[transportationNumber]; !ok {
			details[transportationNumber] = map[string]float64{
				"driver":    0,
				"profit":    0,
				"insurance": 0,
			}
		}

		amount := strings.ReplaceAll(strings.TrimSpace(row[4]), ",", "")
		account := strings.TrimSpace(row[9])
		remark := strings.TrimSpace(row[10])
		if strings.Contains(remark, "付款司机") && account == "**********" {
			driverAmount, _ := strconv.ParseFloat(amount, 64)
			details[transportationNumber]["driver"] = math.Abs(driverAmount)
		}
		if strings.Contains(remark, "服务费") && account == "**********" {
			profitAmount, _ := strconv.ParseFloat(amount, 64)
			details[transportationNumber]["profit"] = math.Abs(profitAmount)
		}
		if strings.Contains(remark, "手续费") && account == "**********" {
			handlingFees, _ := strconv.ParseFloat(amount, 64)
			details[transportationNumber]["profit"] += math.Abs(handlingFees)
		}
		if strings.Contains(remark, "保险费") && account == "**********" {
			insuranceAmount, _ := strconv.ParseFloat(amount, 64)
			details[transportationNumber]["insurance"] = math.Abs(insuranceAmount)
		}
	}

	// 排序
	for k, v := range m {
		j := 0
		for i := 0; i < len(v); i++ {
			if strings.Contains(v[i][4], "+") {
				m[k][j], m[k][i] = m[k][i], m[k][j]
				j++
			}
		}
	}

	documents := []map[string]map[string]float64{}
	for k, v := range m {
		pp := make(map[string]map[string]float64)

		datetime := make(map[string]bool)
		for _, row := range v {
			datetime[row[0]] = true
		}

		// var total float64
		var receivingTotal float64
		var driverTotal float64
		var profitTotal float64
		var insuranceTotal float64

		for t := range datetime {
			for _, row := range v {
				if t == row[0] {
					if row[11] != "" {
						money, _ := strconv.ParseFloat(row[4], 64)
						receivingTotal += money
					}

					transportationNumber := row[10]
					for _, v := range filter {
						transportationNumber = strings.ReplaceAll(transportationNumber, v, "")
					}
					driverTotal += details[transportationNumber]["driver"]
					profitTotal += details[transportationNumber]["profit"]
					insuranceTotal += details[transportationNumber]["insurance"]
				}
			}

			kk := fmt.Sprintf("%s-%s", k, t)

			pp[kk] = map[string]float64{
				"receiving1": receivingTotal,
				"receiving2": receivingTotal,
				"payment1":   driverTotal,
				"payment2":   profitTotal,
				"payment3":   insuranceTotal,
				"payment4":   driverTotal + profitTotal + insuranceTotal,
			}
		}

		documents = append(documents, pp)
	}

	itemsFile, _ := excelize.OpenFile("app/table/items.xlsx")
	itemsRows1, _ := itemsFile.GetRows("预付")
	prepayment1 := make(map[string]string)
	for k, row := range itemsRows1 {
		if k == 0 {
			continue
		}
		if len(row) < 6 {
			continue
		}
		prepayment1[row[5]] = row[0]
	}
	itemsRows2, _ := itemsFile.GetRows("预收")
	prepayment2 := make(map[string]string)
	for k, row := range itemsRows2 {
		if k == 0 || len(row) < 2 {
			continue
		}
		if len(row) < 2 {
			continue
		}
		prepayment2[row[1]] = row[0]
	}

	ff, err := excelize.OpenFile("app/table/import.xlsx")
	if err != nil {
		logger.Stdout.Error(err.Error())
		return 0, err
	}

	start := 6
	i := serialNumber - 1
	serialNumberMap := make(map[string]bool)
	kkkSlice := []string{"receiving1", "receiving2", "payment1", "payment2", "payment3", "payment4"}
	for _, companyName := range currentCompanies {
		for _, v := range documents {
			for k, vv := range v {
				tmp := strings.Split(k, "-")
				name := strings.ReplaceAll(tmp[0], tmp[1], "")
				t, _ := time.Parse("20060102", tmp[1])

				if name == companyName {
					if _, ok := serialNumberMap[name]; !ok {
						i++
						serialNumberMap[name] = true
					}

					for _, kkk := range kkkSlice {
						ff.SetCellValue("Sheet1", fmt.Sprintf("A%d", start), t.Format("2006-01-02"))
						ff.SetCellValue("Sheet1", fmt.Sprintf("B%d", start), i)

						day := strings.Split(t.Format("2006-01-02"), "-")[2] + "日"

						if kkk == "receiving1" {
							ff.SetCellValue("Sheet1", fmt.Sprintf("C%d", start), "收款")
							ff.SetCellValue("Sheet1", fmt.Sprintf("D%d", start), "1002005")
							ff.SetCellValue("Sheet1", fmt.Sprintf("E%d", start), "")
							ff.SetCellValue("Sheet1", fmt.Sprintf("F%d", start), vv["receiving1"])
							ff.SetCellValue("Sheet1", fmt.Sprintf("G%d", start), "")
							ff.SetCellValue("Sheet1", fmt.Sprintf("H%d", start), prepayment2[name])
						}
						if kkk == "receiving2" {
							ff.SetCellValue("Sheet1", fmt.Sprintf("C%d", start), "收款")
							ff.SetCellValue("Sheet1", fmt.Sprintf("D%d", start), "2203")
							ff.SetCellValue("Sheet1", fmt.Sprintf("E%d", start), "")
							ff.SetCellValue("Sheet1", fmt.Sprintf("F%d", start), "")
							ff.SetCellValue("Sheet1", fmt.Sprintf("G%d", start), vv["receiving1"])
							ff.SetCellValue("Sheet1", fmt.Sprintf("H%d", start), prepayment2[name])
						}
						if kkk == "payment1" {
							ff.SetCellValue("Sheet1", fmt.Sprintf("C%d", start), "付司机运费"+day)
							ff.SetCellValue("Sheet1", fmt.Sprintf("D%d", start), "1123")
							ff.SetCellValue("Sheet1", fmt.Sprintf("E%d", start), "")
							ff.SetCellValue("Sheet1", fmt.Sprintf("F%d", start), vv["payment1"])
							ff.SetCellValue("Sheet1", fmt.Sprintf("G%d", start), "")
							ff.SetCellValue("Sheet1", fmt.Sprintf("H%d", start), prepayment1[name])
						}
						if kkk == "payment2" {
							ff.SetCellValue("Sheet1", fmt.Sprintf("C%d", start), "付司机运费"+day)
							ff.SetCellValue("Sheet1", fmt.Sprintf("D%d", start), "1002005")
							ff.SetCellValue("Sheet1", fmt.Sprintf("E%d", start), "")
							ff.SetCellValue("Sheet1", fmt.Sprintf("F%d", start), vv["payment2"])
							ff.SetCellValue("Sheet1", fmt.Sprintf("G%d", start), "")
							ff.SetCellValue("Sheet1", fmt.Sprintf("H%d", start), "C0016")
						}
						if kkk == "payment3" {
							ff.SetCellValue("Sheet1", fmt.Sprintf("C%d", start), "付司机运费"+day)
							ff.SetCellValue("Sheet1", fmt.Sprintf("D%d", start), "1002005")
							ff.SetCellValue("Sheet1", fmt.Sprintf("E%d", start), "")
							ff.SetCellValue("Sheet1", fmt.Sprintf("F%d", start), vv["payment3"])
							ff.SetCellValue("Sheet1", fmt.Sprintf("G%d", start), "")
							ff.SetCellValue("Sheet1", fmt.Sprintf("H%d", start), "C00165")
						}
						if kkk == "payment4" {
							ff.SetCellValue("Sheet1", fmt.Sprintf("C%d", start), "付司机运费"+day)
							ff.SetCellValue("Sheet1", fmt.Sprintf("D%d", start), "1002005")
							ff.SetCellValue("Sheet1", fmt.Sprintf("E%d", start), "")
							ff.SetCellValue("Sheet1", fmt.Sprintf("F%d", start), "")
							ff.SetCellValue("Sheet1", fmt.Sprintf("G%d", start), vv["payment4"])
							ff.SetCellValue("Sheet1", fmt.Sprintf("H%d", start), prepayment2[name])
						}
						start++
					}
				}
			}
		}
	}

	if err := ff.SaveAs(prefix + "凭证接口导入.xlsx"); err != nil {
		return 0, err
	}

	return i + 1, nil
}
