package table

import (
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"wlhy/model"
	"wlhy/toolbox/logger"

	"github.com/samber/lo"
	"github.com/xuri/excelize/v2"
)

// Zhongqi 处理中骐原始报表
func Zhongqi(xlsxName string) {
	// 打开中骐原始报表
	f, err := excelize.OpenFile(xlsxName)
	if err != nil {
		logger.Stdout.Error(err.<PERSON><PERSON>r())
		return
	}
	defer f.Close()

	// 新建中骐发票备注excel
	ff := excelize.NewFile()

	originPlanNumbers := make(map[string]string)
	notePlanNumbers := make(map[string]string)

	// 按照中骐原始报表设置的固定sheet名称
	sheets := []string{"带钢", "外矿"}
	for _, v := range sheets {
		// 提取全部数据
		index, err := f.GetSheetIndex(v)
		if err != nil {
			logger.Stdout.Error(err.<PERSON><PERSON><PERSON>())
			continue
		}
		f.SetActiveSheet(index)
		rows, err := f.GetRows(v)
		if err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}

		// 提取运单号、计划号
		planNumbers := make(map[string]string)
		notes := []string{}
		for k, row := range rows {
			if k <= 2 {
				continue
			}
			row1Value := strings.TrimSpace(row[1])
			row4Value := strings.TrimSpace(row[4])
			notes = append(notes, row1Value)
			planNumbers[row1Value] = row4Value
			if v == "外矿" {
				originPlanNumbers[row1Value] = row4Value
			}
		}

		// 查询运单数据
		dbRows, err := model.DB.Table("tms_transport_note AS a").
			Select([]string{"a.transportation_number", "b.goods_name", "b.haul_distance", "b.loading_name",
				"b.unload_name", "c.vehicle_license_number", "c.vehicle_type_name", "a.unload_number",
				"a.order_remarks"}).
			Joins("JOIN tms_order AS b ON a.order_id = b.id").
			Joins("LEFT JOIN tms_vehicle AS c ON a.transportation_plate = c.vehicle_license_number").
			Where("a.transportation_number IN (?)", notes).
			Group("a.transportation_number").
			Rows()
		if err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}

		// 设置中骐发票备注excel的sheet和表头
		index, err = ff.NewSheet(v)
		if err != nil {
			logger.Stdout.Error(err.Error())
			return
		}
		ff.SetActiveSheet(index)
		ff.SetCellValue(v, "A1", "计划号")
		ff.SetCellValue(v, "B1", "发票备注")

		// 组装发票备注数据
		data := make(map[string]map[string]any)
		for dbRows.Next() {
			var transportationNumber string
			var goodsName string
			var haulDistance float64
			var loadingName string
			var unloadName string
			var vehicleLicenseNumber string
			var vehicleTypeName string
			var unloadNumber float64
			var orderRemarks string
			if err := dbRows.Scan(&transportationNumber, &goodsName, &haulDistance, &loadingName, &unloadName, &vehicleLicenseNumber, &vehicleTypeName, &unloadNumber, &orderRemarks); err != nil {
				logger.Stdout.Error(err.Error())
				continue
			}

			// 提取运单中的计划号以便于与中骐原始数据做对比
			if v == "外矿" {
				notePlanNumbers[transportationNumber] = orderRemarks
			}

			// 根据sheet名称设置不同的tag
			tag := ""
			if v == "外矿" {
				// 按计划号
				tag = strings.TrimSpace(strings.ReplaceAll(strings.ReplaceAll(planNumbers[transportationNumber], "计划号", ""), "\n", ""))
			} else {
				// 按装卸货地址
				tag = strings.TrimSpace(loadingName + "-" + unloadName)
			}

			address := loadingName + "-" + unloadName
			if _, ok := data[tag]; !ok {
				data[tag] = map[string]any{
					"tag":                    tag,
					"goods_name":             goodsName,
					"haul_distance":          haulDistance,
					"address":                address,
					"vehicle_license_number": vehicleLicenseNumber + ";",
					"vehicle_type_name":      vehicleTypeName + ";",
					"loading_number":         unloadNumber,
				}
			} else {
				data[tag]["loading_number"] = data[tag]["loading_number"].(float64) + unloadNumber
				var builder strings.Builder
				builder.WriteString(data[tag]["vehicle_license_number"].(string))
				builder.WriteString(vehicleLicenseNumber)
				builder.WriteString(";")
				data[tag]["vehicle_license_number"] = builder.String()

				builder.Reset() // 重用 builder
				builder.WriteString(data[tag]["vehicle_type_name"].(string))
				builder.WriteString(vehicleTypeName)
				builder.WriteString(";")
				data[tag]["vehicle_type_name"] = builder.String()
			}
		}

		// 车牌号、车辆类型去重并缩减
		for k := range data {
			t := data[k]
			vehicleLicenseNumbers := strings.Split(t["vehicle_license_number"].(string), ";")
			vehicleLicenseNumbers = lo.Uniq(vehicleLicenseNumbers)
			vehicleTypeNames := strings.Split(t["vehicle_type_name"].(string), ";")
			vehicleTypeNames = lo.Uniq(vehicleTypeNames)

			vehicleTypeNameIndex := 0
			if len(vehicleTypeNames) > 2 {
				vehicleTypeNameIndex = 2
			} else {
				vehicleTypeNameIndex = len(vehicleTypeNames)
			}

			vehicleLicenseNumberIndex := 0
			if len(vehicleLicenseNumbers) > 2 {
				vehicleLicenseNumberIndex = 2
			} else {
				vehicleLicenseNumberIndex = len(vehicleLicenseNumbers)
			}

			data[k]["vehicle_license_number"] = strings.Trim(strings.Join(vehicleLicenseNumbers[:vehicleLicenseNumberIndex], ";"), ";")
			data[k]["vehicle_type_name"] = strings.Trim(strings.Join(vehicleTypeNames[:vehicleTypeNameIndex], ";"), ";")
		}

		// 写入发票备注
		start := 2
		for _, d := range data {
			remark := fmt.Sprintf("计划号:%s\n货物名称:%s\n起止地:%s\n车牌号:%s等\n车型:%s等\n运距:%0.2f公里\n合计数量:%0.2f吨\n", d["tag"], d["goods_name"], d["address"], d["vehicle_license_number"], d["vehicle_type_name"], d["haul_distance"], d["loading_number"])
			ff.SetCellValue(v, "A"+strconv.Itoa(start), d["tag"])
			ff.SetCellValue(v, "B"+strconv.Itoa(start), remark)
			start++
		}
	}

	// 根据中骐原始数据更新发票备注中的计划号
	index1, err := ff.NewSheet("更新计划号")
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	ff.SetActiveSheet(index1)
	ff.SetCellValue("更新计划号", "A1", "运单编号")
	ff.SetCellValue("更新计划号", "B1", "更改前的计划号")
	ff.SetCellValue("更新计划号", "C1", "更改后的计划号")
	ff.SetCellValue("更新计划号", "D1", "更新状态")

	start := 2
	// 提取运单计划号正则表达式
	re, _ := regexp.Compile(`\d`)
	for k1, v1 := range originPlanNumbers {
		orderRemarkSlice := re.FindAllString(notePlanNumbers[k1], -1)
		newOrderRemarks := strings.Join(orderRemarkSlice, "")
		if v1 != newOrderRemarks {
			if err := model.DB.Table("tms_transport_note").
				Where("transportation_number = ?", k1).
				Update("order_remarks", v1).
				Error; err != nil {
				ff.SetCellValue("更新计划号", "A"+strconv.Itoa(start), k1)
				ff.SetCellValue("更新计划号", "B"+strconv.Itoa(start), notePlanNumbers[k1])
				ff.SetCellValue("更新计划号", "C"+strconv.Itoa(start), v1)
				ff.SetCellValue("更新计划号", "D"+strconv.Itoa(start), err.Error())
				start++
				continue
			}
			ff.SetCellValue("更新计划号", "A"+strconv.Itoa(start), k1)
			ff.SetCellValue("更新计划号", "B"+strconv.Itoa(start), notePlanNumbers[k1])
			ff.SetCellValue("更新计划号", "C"+strconv.Itoa(start), v1)
			ff.SetCellValue("更新计划号", "D"+strconv.Itoa(start), "成功")
			start++
		}
	}

	// 保存中骐发票备注excel
	ff.DeleteSheet("Sheet1")
	i, _ := ff.GetSheetIndex("带钢")
	ff.SetActiveSheet(i)
	if err := ff.SaveAs("中骐运单发票备注.xlsx"); err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	logger.Stdout.Info("Done")
}
