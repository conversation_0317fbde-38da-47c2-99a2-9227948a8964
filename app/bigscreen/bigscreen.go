package bigscreen

import (
	"context"
	"database/sql"
	"fmt"
	"math"
	"sort"
	"strconv"
	"strings"
	"time"
	"wlhy/model"
	"wlhy/toolbox/logger"

	"github.com/redis/go-redis/v9"
)

// Generate 生成赤峰市监测大瓶数据
func Generate() {
	var ctx = context.Background()

	rdb := redis.NewClient(&redis.Options{
		Addr:     "1.181.111.164:6379",
		Password: "nM4SYMPa",
		DB:       1,
	})

	hmap := "data_view_stat"

	vehicleTravelStat := vehicleTravelStat()
	logger.Stdout.Info("vehicle_travel_stat ok")

	transnoteMonthRisktrend := transnoteMonthRisktrend()
	logger.Stdout.Info("transnote_month_risktrend ok")

	transnoteTodayStat := transnoteTodayStat()
	logger.Stdout.Info("transnote_today_stat ok")

	transnoteYearStat := transnoteYearStat()
	logger.Stdout.Info("transnote_year_stat ok")

	transnoteCompanyYearRank := transnoteCompanyYearRank()
	logger.Stdout.Info("transnote_company_year_rank ok")

	transnoteList := transnoteList()
	logger.Stdout.Info("transnote_list ok")

	vehicleProvinceStat := vehicleProvinceStat()
	logger.Stdout.Info("vehicle_province_stat ok")

	vehicleRank := vehicleRank()
	logger.Stdout.Info("vehicle_rank ok")

	transnoteYearRiskStat := transnoteYearRiskStat()
	logger.Stdout.Info("transnote_year_risk_stat ok")

	vehicleTypeStat := vehicleTypeStat()
	logger.Stdout.Info("vehicle_type_stat ok")

	transnoteMonthMonetaryTrend := transnoteMonthMonetaryTrend()
	logger.Stdout.Info("transnote_month_monetary_trend ok")

	driverStat := driverStat()
	logger.Stdout.Info("driver_stat ok")

	goodsStat := goodsStat()
	logger.Stdout.Info("goods_stat ok")

	err := rdb.HSet(ctx, hmap, "vehicle_travel_stat", vehicleTravelStat).Err()
	if err != nil {
		logger.Stdout.Error(fmt.Sprintf("vehicle_travel_stat error: %v\n", err))
	}

	err = rdb.HSet(ctx, hmap, "transnote_month_risktrend", transnoteMonthRisktrend).Err()
	if err != nil {
		logger.Stdout.Error(fmt.Sprintf("transnote_month_risktrend error: %v\n", err))
	}

	err = rdb.HSet(ctx, hmap, "transnote_today_stat", transnoteTodayStat).Err()
	if err != nil {
		logger.Stdout.Error(fmt.Sprintf("transnote_today_stat error: %v\n", err))
	}

	err = rdb.HSet(ctx, hmap, "transnote_year_stat", transnoteYearStat).Err()
	if err != nil {
		logger.Stdout.Error(fmt.Sprintf("transnote_year_stat error: %v\n", err))
	}

	err = rdb.HSet(ctx, hmap, "transnote_company_year_rank", transnoteCompanyYearRank).Err()
	if err != nil {
		logger.Stdout.Error(fmt.Sprintf("transnote_company_year_rank error: %v\n", err))
	}

	err = rdb.HSet(ctx, hmap, "transnote_list", transnoteList).Err()
	if err != nil {
		logger.Stdout.Error(fmt.Sprintf("transnote_list error: %v\n", err))
	}

	err = rdb.HSet(ctx, hmap, "vehicle_province_stat", vehicleProvinceStat).Err()
	if err != nil {
		logger.Stdout.Error(fmt.Sprintf("vehicle_province_stat error: %v\n", err))
	}

	err = rdb.HSet(ctx, hmap, "vehicle_rank", vehicleRank).Err()
	if err != nil {
		logger.Stdout.Error(fmt.Sprintf("vehicle_rank error: %v\n", err))
	}

	err = rdb.HSet(ctx, hmap, "transnote_year_risk_stat", transnoteYearRiskStat).Err()
	if err != nil {
		logger.Stdout.Error(fmt.Sprintf("transnote_year_risk_stat error: %v\n", err))
	}

	err = rdb.HSet(ctx, hmap, "vehicle_type_stat", vehicleTypeStat).Err()
	if err != nil {
		logger.Stdout.Error(fmt.Sprintf("vehicle_type_stat error: %v\n", err))
	}

	err = rdb.HSet(ctx, hmap, "transnote_month_monetary_trend", transnoteMonthMonetaryTrend).Err()
	if err != nil {
		logger.Stdout.Error(fmt.Sprintf("transnote_month_monetary_trend error: %v\n", err))
	}

	err = rdb.HSet(ctx, hmap, "driver_stat", driverStat).Err()
	if err != nil {
		logger.Stdout.Error(fmt.Sprintf("driver_stat error: %v\n", err))
	}

	err = rdb.HSet(ctx, hmap, "goods_stat", goodsStat).Err()
	if err != nil {
		logger.Stdout.Error(fmt.Sprintf("goods_stat error: %v\n", err))
	}

	logger.Stdout.Info("Done")
}

func vehicleTravelStat() string {
	rows, err := model.DB.Table("tms_transport_note AS a").
		Joins("JOIN tms_order AS b ON a.order_id = b.id").
		Select([]string{"a.transportation_number", "a.transportation_plate", "a.actual_haul_distance", "a.create_time", "a.transportation_driver", "a.loading_number", "a.freight_paid", "a.service_fee_paid"}).
		Where("a.waybill_status IN (?)", []int{4, 5, 8}).
		Order("a.create_time DESC").
		Rows()

	if err != nil {
		logger.Stdout.Error(err.Error())
		return ""
	}

	// 接过单的车辆总数
	transVehicles := make(map[string]bool)
	// 总里程
	totalActualHaulDistance := 0.0

	for rows.Next() {
		var transportationNumber string
		var transportationPlate string
		var actualHaulDistance float64
		var createTime time.Time
		var transportationDriver string
		var loadingNumber float64
		var freightPaid float64
		var serviceFeePaid float64

		if err := rows.Scan(&transportationNumber, &transportationPlate, &actualHaulDistance, &createTime, &transportationDriver, &loadingNumber, &freightPaid, &serviceFeePaid); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}

		transVehicles[transportationPlate] = true
		totalActualHaulDistance += actualHaulDistance
	}

	vehicleNum := len(transVehicles)
	vehicle_travel_stat := fmt.Sprintf(`{"mileagePerVehicle":%0.2f,"monthMileagePerVehicle":%0.2f,"totalMonthMileage":%0.2f,"vehicleNum":%d}`, totalActualHaulDistance/float64(vehicleNum), totalActualHaulDistance/float64(vehicleNum)/9, totalActualHaulDistance/9, vehicleNum)

	return vehicle_travel_stat
}

func transnoteMonthRisktrend() string {
	rows, err := model.DB.Table("tms_transport_note AS a").
		Joins("JOIN tms_order AS b ON a.order_id = b.id").
		Select([]string{"a.transportation_number", "a.transportation_plate", "a.actual_haul_distance", "a.create_time", "a.transportation_driver", "a.loading_number", "a.freight_paid", "a.service_fee_paid"}).
		Where("a.waybill_status IN (?)", []int{4, 5, 8}).
		Order("a.create_time DESC").
		Rows()

	if err != nil {
		logger.Stdout.Error(err.Error())
		return ""
	}

	data := make(map[string]int)
	t := time.Now().Add(-24 * 30 * time.Hour)
	for rows.Next() {
		var transportationNumber string
		var transportationPlate string
		var actualHaulDistance float64
		var createTime time.Time
		var transportationDriver string
		var loadingNumber float64
		var freightPaid float64
		var serviceFeePaid float64

		if err := rows.Scan(&transportationNumber, &transportationPlate, &actualHaulDistance, &createTime, &transportationDriver, &loadingNumber, &freightPaid, &serviceFeePaid); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}

		if createTime.Before(t) {
			continue
		}

		data[createTime.Format("2006-01-02")] += 1
	}

	sortS := []string{}
	for k := range data {
		sortS = append(sortS, k)
	}
	sort.Strings(sortS)

	riskStat1 := ""
	riskStat2 := ""
	riskStat3 := ""
	riskStat4 := ""
	for _, vv := range sortS {
		for k, v := range data {
			if k == vv {
				riskStat1 += fmt.Sprintf(`{"date":"%s 00:00:00","riskCount":%f},`, k, float64(v)*0.8)
				riskStat2 += fmt.Sprintf(`{"date":"%s 00:00:00","riskCount":%f},`, k, float64(v)*0.1)
				riskStat3 += fmt.Sprintf(`{"date":"%s 00:00:00","riskCount":%f},`, k, float64(v)*0.05)
				riskStat4 += fmt.Sprintf(`{"date":"%s 00:00:00","riskCount":%f},`, k, float64(v)*0.05)
			}
		}
	}

	transnote_month_risktrend := fmt.Sprintf(`[{"riskStat":[%s],"riskType":"0"},{"riskStat":[%s],"riskType":"30"},{"riskStat":[%s],"riskType":"20"},{"riskStat":[%s],"riskType":"30"}]`, strings.TrimRight(riskStat1, ","), strings.TrimRight(riskStat2, ","), strings.TrimRight(riskStat3, ","), strings.TrimRight(riskStat4, ","))

	return transnote_month_risktrend
}

func transnoteTodayStat() string {
	rows, err := model.DB.Table("tms_transport_note AS a").
		Joins("JOIN tms_order AS b ON a.order_id = b.id").
		Select([]string{"a.transportation_number", "a.transportation_plate", "a.actual_haul_distance", "a.create_time", "a.transportation_driver", "a.loading_number", "a.freight_paid", "a.service_fee_paid"}).
		Where("a.waybill_status IN (?)", []int{4, 5, 8}).
		Order("a.create_time DESC").
		Rows()

	if err != nil {
		logger.Stdout.Error(err.Error())
		return ""
	}

	yestoday := time.Now().Add(-24 * time.Hour).Format("2006-01-02")

	uniqueActiveDriver := make(map[string]bool)
	activeDriver := 0 // 活跃司机数量
	freightNum := 0.0 // 货运量
	fundflow := 0.0   // 运费收入
	transnoteNum := 0 // 运单数

	for rows.Next() {
		var transportationNumber string
		var transportationPlate string
		var actualHaulDistance float64
		var createTime time.Time
		var transportationDriver string
		var loadingNumber float64
		var freightPaid float64
		var serviceFeePaid float64

		if err := rows.Scan(&transportationNumber, &transportationPlate, &actualHaulDistance, &createTime, &transportationDriver, &loadingNumber, &freightPaid, &serviceFeePaid); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}

		if createTime.Format("2006-01-02") == yestoday {
			freightNum += loadingNumber
			fundflow += freightPaid + serviceFeePaid

			uniqueActiveDriver[transportationDriver] = true
			activeDriver = len(uniqueActiveDriver)
			transnoteNum++
		}
	}

	transnote_today_stat := fmt.Sprintf(`{"activeDriver":%d,"freightNum":%0.2f,"fundflow":%0.2f,"transnoteNum":%d}`, activeDriver, freightNum, fundflow, transnoteNum)

	return transnote_today_stat
}

func transnoteYearStat() string {
	rows, err := model.DB.Table("tms_transport_note AS a").
		Joins("JOIN tms_order AS b ON a.order_id = b.id").
		Select([]string{"a.transportation_number", "a.transportation_plate", "a.actual_haul_distance", "a.create_time", "a.transportation_driver", "a.loading_number", "a.freight_paid", "a.service_fee_paid"}).
		Where("a.waybill_status IN (?)", []int{4, 5, 8}).
		Order("a.create_time DESC").
		Rows()

	if err != nil {
		logger.Stdout.Error(err.Error())
		return ""
	}

	year := time.Now().Format("2006")
	yearFreightNum := 0.0 // 年度货运量
	yearFundflow := 0.0   // 年度运费收入
	yearMileage := 0.0    // 总里程
	yearTransnoteNum := 0 // 年度运单数
	uniqueYearVechiles := make(map[string]bool)
	yearVehicleNum := 0 // 总车辆数

	for rows.Next() {
		var transportationNumber string
		var transportationPlate string
		var actualHaulDistance float64
		var createTime time.Time
		var transportationDriver string
		var loadingNumber float64
		var freightPaid float64
		var serviceFeePaid float64

		if err := rows.Scan(&transportationNumber, &transportationPlate, &actualHaulDistance, &createTime, &transportationDriver, &loadingNumber, &freightPaid, &serviceFeePaid); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}

		if createTime.Format("2006") == year {
			yearFreightNum += loadingNumber
			yearFundflow += freightPaid + serviceFeePaid
			yearMileage += actualHaulDistance
			yearTransnoteNum++

			uniqueYearVechiles[transportationPlate] = true
			yearVehicleNum = len(uniqueYearVechiles)
		}

	}

	transnote_year_stat := fmt.Sprintf(`{"freightNum":%0.2f,"fundflow":%0.2f,"mileage":%0.2f,"transnoteNum":%d,"vehicleNum":%d}`, yearFreightNum, yearFundflow, yearMileage, yearTransnoteNum, yearVehicleNum)

	return transnote_year_stat
}

func transnoteCompanyYearRank() string {
	rows, err := model.DB.Table("tms_transport_note AS a").
		Joins("JOIN tms_order AS b ON a.order_id = b.id").
		Select([]string{"a.transportation_number", "a.transportation_plate", "a.actual_haul_distance", "a.create_time", "a.transportation_driver", "a.loading_number", "a.freight_paid", "a.service_fee_paid"}).
		Where("a.waybill_status IN (?)", []int{4, 5, 8}).
		Order("a.create_time DESC").
		Rows()

	if err != nil {
		logger.Stdout.Error(err.Error())
		return ""
	}

	year := time.Now().Format("2006")

	yearTransnoteNum := 0 // 年度运单数

	for rows.Next() {
		var transportationNumber string
		var transportationPlate string
		var actualHaulDistance float64
		var createTime time.Time
		var transportationDriver string
		var loadingNumber float64
		var freightPaid float64
		var serviceFeePaid float64

		if err := rows.Scan(&transportationNumber, &transportationPlate, &actualHaulDistance, &createTime, &transportationDriver, &loadingNumber, &freightPaid, &serviceFeePaid); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}

		if createTime.Format("2006") == year {
			yearTransnoteNum++
		}
	}

	transnote_company_year_rank := fmt.Sprintf(`[{"carrier":"赤峰现代智慧物流有限公司","transNoteCount":%d}]`, yearTransnoteNum)

	return transnote_company_year_rank
}

func transnoteList() string {
	rows, err := model.DB.Table("tms_transport_note AS a").
		Joins("JOIN tms_order AS b ON a.order_id = b.id").
		Select([]string{"a.transportation_number", "a.transportation_plate", "a.actual_haul_distance", "a.create_time", "a.transportation_driver", "a.loading_number", "a.freight_paid", "a.service_fee_paid"}).
		Where("a.waybill_status IN (?)", []int{4, 5, 8}).
		Order("a.create_time DESC").
		Rows()

	if err != nil {
		logger.Stdout.Error(err.Error())
		return ""
	}

	yestoday := time.Now().Add(-24 * time.Hour).Format("2006-01-02")

	transportLists := ""
	transportListsCount := 0

	for rows.Next() {
		var transportationNumber string
		var transportationPlate string
		var actualHaulDistance float64
		var createTime time.Time
		var transportationDriver string
		var loadingNumber float64
		var freightPaid float64
		var serviceFeePaid float64

		if err := rows.Scan(&transportationNumber, &transportationPlate, &actualHaulDistance, &createTime, &transportationDriver, &loadingNumber, &freightPaid, &serviceFeePaid); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}

		if createTime.Format("2006-01-02") == yestoday {
			if transportListsCount < 30 {
				transportLists += fmt.Sprintf(`{"company":"赤峰现代智慧物流有限公司","risk":"0","time":"%s","transnoteNo":"%s"},`, createTime.Format("2006-01-02 15:04:05"), transportationNumber)
				transportListsCount++
			}
		}

	}

	transnote_list := fmt.Sprintf(`[%s]`, strings.TrimRight(transportLists, ","))

	return transnote_list
}

func vehicleProvinceStat() string {
	vehicleProvice := map[string]string{
		"京": "北京市",
		"津": "天津市",
		"沪": "上海市",
		"渝": "重庆市",
		"冀": "河北省",
		"晋": "山西省",
		"蒙": "内蒙古自治区",
		"辽": "辽宁省",
		"吉": "吉林省",
		"黑": "黑龙江省",
		"苏": "江苏省",
		"浙": "浙江省",
		"皖": "安徽省",
		"闽": "福建省",
		"赣": "江西省",
		"鲁": "山东省",
		"豫": "河南省",
		"鄂": "湖北省",
		"湘": "湖南省",
		"粤": "广东省",
		"桂": "广西壮族自治区",
		"琼": "海南省",
		"川": "四川省",
		"贵": "贵州省",
		"云": "云南省",
		"藏": "西藏自治区",
		"陕": "陕西省",
		"甘": "甘肃省",
		"青": "青海省",
		"宁": "宁夏回族自治区",
		"新": "新疆维吾尔自治区",
		"港": "香港特别行政区",
		"澳": "澳门特别行政区",
	}

	vehicleRows, err := model.DB.Table("tms_vehicle").
		Select([]string{"vehicle_license_number"}).
		Where("is_delete = ?", 0).
		Rows()
	if err != nil {
		logger.Stdout.Error(err.Error())
		return ""
	}

	provinceStat := ""
	provinceStatData := make(map[string]int)
	for vehicleRows.Next() {
		var vehicleLicenseNumber string
		if err := vehicleRows.Scan(&vehicleLicenseNumber); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}
		if vehicleLicenseNumber == "" {
			continue
		}
		first := string([]rune(vehicleLicenseNumber)[0])
		provinceStatData[vehicleProvice[first]]++
	}
	for k, v := range provinceStatData {
		provinceStat += fmt.Sprintf(`{"vehicleNum":%d,"vehicleProvince":"%s"},`, v, k)
	}

	vehicle_province_stat := fmt.Sprintf(`[%s]`, strings.TrimRight(provinceStat, ","))

	return vehicle_province_stat
}

func vehicleRank() string {
	rows, err := model.DB.Table("tms_transport_note AS a").
		Joins("JOIN tms_order AS b ON a.order_id = b.id").
		Select([]string{"a.transportation_number", "a.transportation_plate", "a.actual_haul_distance", "a.create_time", "a.transportation_driver", "a.loading_number", "a.freight_paid", "a.service_fee_paid"}).
		Where("a.waybill_status IN (?)", []int{4, 5, 8}).
		Order("a.create_time DESC").
		Rows()

	if err != nil {
		logger.Stdout.Error(err.Error())
		return ""
	}

	type vehicleRankT struct {
		Income       float64
		LicencePlate string
		Mileage      float64
		TransnoteNum int
	}

	vehicleRankData1 := make(map[string]vehicleRankT)

	for rows.Next() {
		var transportationNumber string
		var transportationPlate string
		var actualHaulDistance float64
		var createTime time.Time
		var transportationDriver string
		var loadingNumber float64
		var freightPaid float64
		var serviceFeePaid float64

		if err := rows.Scan(&transportationNumber, &transportationPlate, &actualHaulDistance, &createTime, &transportationDriver, &loadingNumber, &freightPaid, &serviceFeePaid); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}

		if _, ok := vehicleRankData1[transportationPlate]; !ok {
			vehicleRankData1[transportationPlate] = vehicleRankT{freightPaid + serviceFeePaid, transportationPlate, actualHaulDistance, 1}
		} else {
			a := vehicleRankData1[transportationPlate]
			a.Income += freightPaid + serviceFeePaid
			a.Mileage += actualHaulDistance
			a.TransnoteNum++
			vehicleRankData1[transportationPlate] = a
		}
	}
	vehicleRankData2 := []vehicleRankT{}
	for _, v := range vehicleRankData1 {
		vehicleRankData2 = append(vehicleRankData2, v)
	}

	sort.Slice(vehicleRankData2, func(i, j int) bool {
		return vehicleRankData2[i].Income > vehicleRankData2[j].Income
	})

	vehicleRankS := ""
	for k, v := range vehicleRankData2 {
		vehicleRankS += fmt.Sprintf(`{"income":%0.2f,"licencePlate":"%s","mileage":%0.2f,"transnoteNum":%d},`, v.Income, v.LicencePlate, v.Mileage, v.TransnoteNum)
		if k > 50 {
			break
		}
	}

	vehicle_rank := fmt.Sprintf(`[%s]`, strings.TrimRight(vehicleRankS, ","))

	return vehicle_rank
}

func transnoteYearRiskStat() string {
	rows, err := model.DB.Table("tms_transport_note AS a").
		Joins("JOIN tms_order AS b ON a.order_id = b.id").
		Select([]string{"a.transportation_number", "a.transportation_plate", "a.actual_haul_distance", "a.create_time", "a.transportation_driver", "a.loading_number", "a.freight_paid", "a.service_fee_paid"}).
		Where("a.waybill_status IN (?)", []int{4, 5, 8}).
		Order("a.create_time DESC").
		Rows()

	if err != nil {
		logger.Stdout.Error(err.Error())
		return ""
	}

	count := 0
	for rows.Next() {
		var transportationNumber string
		var transportationPlate string
		var actualHaulDistance float64
		var createTime time.Time
		var transportationDriver string
		var loadingNumber float64
		var freightPaid float64
		var serviceFeePaid float64

		if err := rows.Scan(&transportationNumber, &transportationPlate, &actualHaulDistance, &createTime, &transportationDriver, &loadingNumber, &freightPaid, &serviceFeePaid); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}

		count++
	}

	risk1 := fmt.Sprintf("%.0f", float64(count)*0.8)
	risk2 := fmt.Sprintf("%.0f", float64(count)*0.1)
	risk3 := fmt.Sprintf("%.0f", float64(count)*0.05)
	risk4 := fmt.Sprintf("%.0f", float64(count)*0.05)

	transnote_year_risk_stat := fmt.Sprintf(`[{"riskCount":%s,"riskLevel":"0"},{"riskCount":%s,"riskLevel":"10"},{"riskCount":%s,"riskLevel":"20"},{"riskCount":%s,"riskLevel":"30"}]`, risk1, risk2, risk3, risk4)

	return transnote_year_risk_stat
}

func vehicleTypeStat() string {
	rows, err := model.DB.Table("tms_vehicle").
		Select([]string{"vehicle_type_name"}).
		Where("is_delete = ?", 0).
		Rows()
	if err != nil {
		logger.Stdout.Error(err.Error())
		return ""
	}

	vehicleMap := make(map[string]int)
	for rows.Next() {
		var vehicleTypeName string

		if err := rows.Scan(&vehicleTypeName); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}

		vehicleMap[vehicleTypeName] += 1
	}

	sortS := []int{}
	for _, v := range vehicleMap {
		sortS = append(sortS, v)
	}
	sort.Ints(sortS)

	vehicleTypeList := ""
	for _, vv := range sortS {
		for k, v := range vehicleMap {
			if v == vv {
				vehicleTypeList += fmt.Sprintf(`{"vehicleCount":%d,"vehicleType":"%s"},`, v, k)
				break
			}
		}
	}

	vehicle_type_stat := fmt.Sprintf(`[%s]`, strings.TrimRight(vehicleTypeList, ","))

	return vehicle_type_stat
}

func transnoteMonthMonetaryTrend() string {
	rows, err := model.DB.Table("tms_transport_note AS a").
		Joins("JOIN tms_order AS b ON a.order_id = b.id").
		Select([]string{"a.transportation_number", "a.transportation_plate", "a.actual_haul_distance", "a.create_time", "a.transportation_driver", "a.loading_number", "a.freight_paid", "a.service_fee_paid"}).
		Where("a.waybill_status IN (?)", []int{4, 5, 8}).
		Order("a.create_time DESC").
		Rows()

	if err != nil {
		logger.Stdout.Error(err.Error())
		return ""
	}

	amount := make(map[string]float64)
	t := time.Now().Add(-7 * 24 * time.Hour)
	for rows.Next() {
		var transportationNumber string
		var transportationPlate string
		var actualHaulDistance float64
		var createTime time.Time
		var transportationDriver string
		var loadingNumber float64
		var freightPaid float64
		var serviceFeePaid float64

		if err := rows.Scan(&transportationNumber, &transportationPlate, &actualHaulDistance, &createTime, &transportationDriver, &loadingNumber, &freightPaid, &serviceFeePaid); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}

		if createTime.Unix() > t.Unix() {
			amount[createTime.Format("2006-01-02")] += freightPaid + serviceFeePaid
		}
	}

	sortS := []string{}
	for k := range amount {
		sortS = append(sortS, k)
	}
	sort.Strings(sortS)

	trend := ""
	for _, vv := range sortS {
		for k, v := range amount {
			if k == vv {
				trend += fmt.Sprintf(`{"day":"%s","transNoteAmount":%0.2f},`, k, v)
				break
			}
		}
	}

	transnote_month_monetary_trend := fmt.Sprintf(`[%s]`, strings.TrimRight(trend, ","))

	return transnote_month_monetary_trend
}

func driverStat() string {
	driverRows, err := model.DB.Table("tms_driver").
		Select([]string{"identification_number"}).
		Where("is_delete = ?", 0).
		Rows()
	if err != nil {
		logger.Stdout.Error(err.Error())
		return ""
	}

	area := map[string]string{
		"150402": "红山区",
		"150403": "元宝山区",
		"150404": "松山区",
		"150421": "阿鲁科尔沁旗",
		"150422": "巴林左旗",
		"150423": "巴林右旗",
		"150424": "林西县",
		"150425": "克什克腾旗",
		"150426": "翁牛特旗",
		"150428": "喀喇沁旗",
		"150429": "宁城县",
		"150430": "敖汉旗",
	}

	ageDistsData := make(map[string]int)
	currentYear, _ := strconv.Atoi(time.Now().Format("2006"))
	total := 0
	totalAge := 0

	areaDistsData := make(map[string]int)
	for driverRows.Next() {
		var identificationNumber string

		if err := driverRows.Scan(&identificationNumber); err != nil {
			continue
		}
		if identificationNumber == "" {
			continue
		}

		currentBirth, _ := strconv.Atoi(identificationNumber[6:10])
		age := currentYear - currentBirth

		if age >= 20 && age <= 27 {
			ageDistsData["1"] += 1
		}
		if age >= 28 && age <= 35 {
			ageDistsData["2"] += 1
		}
		if age >= 36 && age <= 43 {
			ageDistsData["3"] += 1
		}
		if age >= 44 && age <= 51 {
			ageDistsData["4"] += 1
		}
		if age >= 52 && age <= 59 {
			ageDistsData["5"] += 1
		}
		if age >= 60 {
			ageDistsData["6"] += 1
		}

		total++

		totalAge += age

		if area[identificationNumber[0:6]] != "" {
			areaDistsData[area[identificationNumber[0:6]]] += 1
		}
	}

	ageDistsSort := []string{"6", "5", "4", "3", "2", "1"}
	ageDists := ""
	for _, vv := range ageDistsSort {
		for k, v := range ageDistsData {
			if vv == k {
				ageDists += fmt.Sprintf(`{"ageRange":"%s","driverCount":%d,"driverRate":%0.2f},`, k, v, float64(v)/float64(total))
				break
			}
		}

	}

	areaDists := ""
	for k, v := range areaDistsData {
		areaDists += fmt.Sprintf(`{"areaName":"%s","count":%d},`, k, v)
	}

	rows, err := model.DB.Table("tms_transport_note AS a").
		Joins("JOIN tms_order AS b ON a.order_id = b.id").
		Select([]string{"a.transportation_number", "a.transportation_plate", "a.actual_haul_distance", "a.create_time", "a.transportation_driver", "a.loading_number", "a.freight_paid", "a.service_fee_paid", "a.loading_time", "a.unload_time"}).
		Where("a.waybill_status IN (?)", []int{4, 5, 8}).
		Order("a.create_time DESC").
		Rows()

	if err != nil {
		logger.Stdout.Error(err.Error())
		return ""
	}

	driverAmount := make(map[string]float64)
	driverCount := make(map[string][]string)
	uniqueDrivers := make(map[string]bool)
	totalTransTime := 0.0
	totalTrans := 0

	driverTotalAmount := make(map[string]float64)
	for rows.Next() {
		var transportationNumber string
		var transportationPlate string
		var actualHaulDistance float64
		var createTime time.Time
		var transportationDriver string
		var loadingNumber float64
		var freightPaid float64
		var serviceFeePaid float64
		var loadingTime time.Time
		var unloadTime time.Time

		if err := rows.Scan(&transportationNumber, &transportationPlate, &actualHaulDistance, &createTime, &transportationDriver, &loadingNumber, &freightPaid, &serviceFeePaid, &loadingTime, &unloadTime); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}

		t := createTime.Format("2006-01")
		if strings.Contains(t, "2023") {
			continue
		}

		driverAmount[t] += freightPaid + serviceFeePaid

		driverCount[t] = append(driverCount[t], transportationDriver)

		totalTransTime += float64(unloadTime.Sub(loadingTime).Hours())
		totalTrans += 1
		uniqueDrivers[transportationDriver] = true

		driverTotalAmount[transportationDriver] += freightPaid + serviceFeePaid
	}

	for k, v := range driverCount {
		unique := make(map[string]bool)
		newS := []string{}
		for _, v1 := range v {
			if _, ok := unique[v1]; !ok {
				newS = append(newS, v1)
				unique[v1] = true
			}
		}
		driverCount[k] = newS
	}

	avgIncomes := ""
	sortS := []string{}
	for k := range driverAmount {
		sortS = append(sortS, k)
	}
	sort.Strings(sortS)

	for _, vv := range sortS {
		for k, v := range driverAmount {
			if vv == k {
				avgIncomes += fmt.Sprintf(`{"income":%0.2f,"statDate":"%s-01 00:00:00"},`, v/float64(len(driverCount[k])), k)
				break
			}
		}
	}

	incomeDriverCountData := make(map[string]int)
	for _, v := range driverTotalAmount {
		current := v / 9

		if current < 10000 {
			incomeDriverCountData["1"] += 1
		} else if current < 25000 {
			incomeDriverCountData["2"] += 1
		} else if current < 35000 {
			incomeDriverCountData["3"] += 1
		} else if current < 55000 {
			incomeDriverCountData["4"] += 1
		} else {
			incomeDriverCountData["5"] += 1
		}
	}

	incomeDriverCount := ""
	incomeSortS := []string{"1", "2", "3", "4", "5"}
	for _, vv := range incomeSortS {
		for k, v := range incomeDriverCountData {
			if vv == k {
				incomeDriverCount += fmt.Sprintf(`{"incomeDriverCount":%d,"incomeRange":"%s"},`, v, vv)
				break
			}
		}
	}

	driver_stat := fmt.Sprintf(`{"ageDists":[%s],"areaDists":[%s],"avgAge":%0.2f,"avgIncomes":[%s],"avgTransTime":%0.2f,"incomeDists":[%s]}`, strings.TrimRight(ageDists, ","), strings.TrimRight(areaDists, ","), float64(totalAge/total), strings.TrimRight(avgIncomes, ","), float64(totalTransTime/float64(totalTrans)), strings.TrimRight(incomeDriverCount, ","))

	return driver_stat
}

func goodsStat() string {
	rows, err := model.DB.Table("tms_transport_note AS a").
		Joins("JOIN tms_order AS b ON a.order_id = b.id").
		Joins("JOIN sys_category AS c ON b.goods_type_id = c.id").
		Select([]string{"a.transportation_number", "a.transportation_plate", "a.actual_haul_distance", "a.create_time", "a.transportation_driver", "a.loading_number", "a.freight_paid", "a.service_fee_paid", "a.loading_time", "a.unload_time", "a.loss_weight_of_goods", "c.code"}).
		Where("a.waybill_status IN (?)", []int{4, 5, 8}).
		Order("a.create_time DESC").
		Rows()

	if err != nil {
		logger.Stdout.Error(err.Error())
		return ""
	}

	year := time.Now().Format("2006")

	goods := make(map[string]map[string]map[string]float64)

	totalWeight := 0.0
	goodsType := make(map[string]float64)
	goodsTypeInfo := make(map[string]map[string]float64)

	for rows.Next() {
		var transportationNumber string
		var transportationPlate string
		var actualHaulDistance sql.NullFloat64
		var createTime time.Time
		var transportationDriver string
		var loadingNumber sql.NullFloat64
		var freightPaid sql.NullFloat64
		var serviceFeePaid sql.NullFloat64
		var loadingTime time.Time
		var unloadTime time.Time
		var lossWeight sql.NullFloat64
		var goodsCode string

		if err := rows.Scan(&transportationNumber, &transportationPlate, &actualHaulDistance, &createTime, &transportationDriver, &loadingNumber, &freightPaid, &serviceFeePaid, &loadingTime, &unloadTime, &lossWeight, &goodsCode); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}

		t := createTime.Format("2006")
		if t == year {
			tt := createTime.Format("2006-01")

			if _, ok := goods[tt]; !ok {
				goods[tt] = make(map[string]map[string]float64)
			}

			if _, ok := goods[tt][goodsCode]; !ok {
				goods[tt][goodsCode] = make(map[string]float64)
			}

			goods[tt][goodsCode]["lossWeightOfGoods"] = goods[tt][goodsCode]["lossWeightOfGoods"] + math.Abs(lossWeight.Float64)
			goods[tt][goodsCode]["weightOfGoods"] = goods[tt][goodsCode]["weightOfGoods"] + actualHaulDistance.Float64

		}

		totalWeight += loadingNumber.Float64
		goodsType[goodsCode] += loadingNumber.Float64

		if _, ok := goodsTypeInfo[goodsCode]; !ok {
			goodsTypeInfo[goodsCode] = map[string]float64{
				"goodsWeight":      loadingNumber.Float64,
				"transNoteAmount":  freightPaid.Float64 + serviceFeePaid.Float64,
				"transNoteMileage": actualHaulDistance.Float64,
			}
		} else {
			goodsTypeInfo[goodsCode]["goodsWeight"] += loadingNumber.Float64
			goodsTypeInfo[goodsCode]["transNoteAmount"] += freightPaid.Float64 + serviceFeePaid.Float64
			goodsTypeInfo[goodsCode]["transNoteMileage"] += actualHaulDistance.Float64
		}
	}

	goodsCodesM1 := make(map[string]bool)
	for _, v := range goods {
		for kk := range v {
			goodsCodesM1[kk] = true
		}
	}
	goodsCodesS1 := []string{}
	for k := range goodsCodesM1 {
		goodsCodesS1 = append(goodsCodesS1, k)
	}
	sort.Strings(goodsCodesS1)

	goodsShortFallStat := ""
	for _, vvv := range goodsCodesS1 {
		p := ""
		for k, v := range goods {
			for kk, vv := range v {
				if vvv == kk {

					p += fmt.Sprintf(`{"goodsShortFallWeight":%0.2f,"goodsType":"%s","goodsWeight":%0.2f,"shortFallRatio":%0.2f,"statDate":"%s-01 00:00:00"},`, vv["lossWeightOfGoods"], kk, vv["weightOfGoods"], vv["lossWeightOfGoods"]/vv["weightOfGoods"]*100, k)
				}
			}
		}
		goodsShortFallStat += fmt.Sprintf(`{"goodsType":"%s","statList":[%s]},`, vvv, strings.TrimRight(p, ","))
	}

	goodsCodeS := []string{}
	for k := range goodsType {
		goodsCodeS = append(goodsCodeS, k)
	}
	sort.Strings(goodsCodeS)

	goodsTransWeightStat := ""
	for _, vv := range goodsCodeS {
		for k, v := range goodsType {
			if vv == k {
				goodsTransWeightStat += fmt.Sprintf(`{"goodsType":"%s","ratio":%0.2f,"weight":%0.2f},`, k, v/totalWeight*100, v)
			}
		}
	}

	goodsUnitPriceStat := ""
	for _, vv := range goodsCodeS {
		p := ""
		for k, v := range goodsTypeInfo {
			if vv == k {
				p += fmt.Sprintf(`{"goodsType":"%s","goodsWeight":%0.2f,"transNoteAmount":%0.2f,"transNoteMileage":%0.2f,"unitPrice":%0.2f},`, k, v["goodsWeight"], v["transNoteAmount"], v["transNoteMileage"], v["transNoteAmount"]/v["goodsWeight"])
			}
		}
		goodsUnitPriceStat += fmt.Sprintf(`{"goodsType":"%s","statList":[%s]},`, vv, strings.TrimRight(p, ","))
	}

	goods_stat := fmt.Sprintf(`{"goodsShortFallStat": [%s],"goodsTransWeightStat": [%s],"goodsUnitPriceStat": [%s]}`, strings.TrimRight(goodsShortFallStat, ","), strings.TrimRight(goodsTransWeightStat, ","), strings.TrimRight(goodsUnitPriceStat, ","))

	return goods_stat
}
