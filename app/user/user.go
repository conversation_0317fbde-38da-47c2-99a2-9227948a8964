package user

import (
	"bytes"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"sync"
	"text/template"
	"time"
	"wlhy/model"
	"wlhy/thirdparty/oss"
	"wlhy/toolbox"
	"wlhy/toolbox/logger"

	"github.com/google/uuid"
	"github.com/samber/lo"
	"github.com/xuri/excelize/v2"
	"gorm.io/gorm"
)

// ChangeCompanyPhone 修改货主手机号
func ChangeCompanyPhone(oldPhone, newPhone string) {
	err := model.DB.Transaction(func(tx *gorm.DB) error {
		var isExist int64
		tx.Table("sys_user").Where("phone = ?", newPhone).Where("is_delete = ?", 0).Count(&isExist)
		if isExist > 0 {
			return errors.New("phone already exists")
		}

		// 用户表
		if err := tx.Table("sys_user").
			Where("phone = ?", oldPhone).
			Where("username = ?", oldPhone).
			Where("is_delete = ?", 0).
			Updates(map[string]any{
				"phone":    newPhone,
				"username": newPhone,
			}).Error; err != nil {
			return err
		}

		// 货主表
		if err := tx.Table("tms_shipper").
			Where("shipper_phone = ?", oldPhone).
			Where("is_delete = ?", 0).
			Updates(map[string]any{
				"shipper_phone": newPhone,
			}).Error; err != nil {
			return err
		}

		return nil
	})
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	logger.Stdout.Info("Done")
}

// GenrateTaxAgreement 生成税务委托书
func GenerateTaxAgreement(token, driverPhone string) {
	client := http.Client{}

	var driverID string
	if err := model.DB.Table("tms_driver").
		Select([]string{"driver_id"}).
		Where("phone = ?", driverPhone).
		Where("is_delete = ?", 0).
		Scan(&driverID).Error; err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	if driverID == "" {
		logger.Stdout.Error("未找到司机数据")
		return
	}

	b := map[string]string{
		"driverId": driverID,
	}
	bj, _ := json.Marshal(b)

	req, err := http.NewRequest("POST", "https://www.cfhszy.com/gw/hszy-usercenter/agree/taxProxyCommission/regenerateAgree", bytes.NewBuffer([]byte(bj)))
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("x-access-token", token)

	resp, err := client.Do(req)
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		logger.Stdout.Error(resp.Status)
		return
	}
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	logger.Stdout.Info(string(body))
}

// GenrateCmbSubAccount 生成招商银行子单元编号
func GenrateCmbSubAccount(token, driverID string) {
	client := &http.Client{}

	// 生成子单元编号
	b := map[string]string{
		"driverId": driverID,
	}
	bj, _ := json.Marshal(b)

	req, err := http.NewRequest("POST", "https://www.cfhszy.com/gw/hszy-payment/ntocc/sysZhaoshangAccount/insertDriverAccounts", bytes.NewBuffer(bj))
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("x-access-token", "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiIxNzMyMzA1MjgwMzg4ODg2NTMwIiwicm5TdHIiOiJmMjNnMFBOclVjdHp5a1B4MXVYeHVLaTdiVzBhbzl2bSJ9.HFe5xhm0b4oVjFHrbWg_zQGArqqBK7LhFHumMm1bXBc")

	resp, err := client.Do(req)
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		logger.Stdout.Error(resp.Status)
		return
	}
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	logger.Stdout.Info(string(body))
}

// GenerateTemplateSeal 生成模板印章
func GenerateTemplateSeal(token string, driverID string) {
	client := &http.Client{}

	// request
	req, err := http.NewRequest("POST", "https://op.cfhszy.com/gw/hszy-external-interface/junziqian/addTemplateSeal?userId="+driverID, nil)
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("x-access-token", token)

	// response
	resp, err := client.Do(req)
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		logger.Stdout.Error(resp.Status)
		return
	}
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	logger.Stdout.Info(string(body))
}

// GenerateManagementProve 生成挂靠声明
func GenerateManagementProve(dirName, driverID, vehicleID string) error {
	type DriverT struct {
		RealName             string `gorm:"column:realname"`
		IdentificationNumber string `gorm:"column:identification_number"`
		TemplateSeal         string `gorm:"column:template_seal"`
	}

	type VehicleT struct {
		VehicleLicenseNumber string    `gorm:"column:vehicle_license_number"`
		VehicleOwner         string    `gorm:"column:vehicle_owner"`
		CreateTime           time.Time `gorm:"column:create_time"`
	}

	var driver DriverT
	model.DB.Table("sys_user").
		Select([]string{"realname", "identification_number", "template_seal"}).
		Where("id = ?", driverID).
		Where("is_delete = ?", 0).
		Scan(&driver)

	var vehicle VehicleT
	model.DB.Table("tms_vehicle").
		Select([]string{"vehicle_license_number", "vehicle_owner", "create_time"}).
		Where("id = ?", vehicleID).
		Where("is_delete = ?", 0).
		Scan(&vehicle)

	invoiceHtmlTemplatePath := "/Users/<USER>/codes/own/wlhy/app/user/*.html"
	tmpl := template.Must(template.New("vehicle_template.html").ParseGlob(invoiceHtmlTemplatePath))

	filename := uuid.New().String()
	htmlFilename := filepath.Join(dirName, fmt.Sprintf("%s.html", filename))
	pdfFilename := filepath.Join(dirName, fmt.Sprintf("%s.pdf", filename))
	f, _ := os.Create(htmlFilename)

	htmlData := struct {
		Driver  DriverT
		Vehicle VehicleT
		Date    string
	}{
		Driver:  driver,
		Vehicle: vehicle,
		Date:    vehicle.CreateTime.Format("2006-01-02"),
	}

	if err := tmpl.ExecuteTemplate(f, "vehicle_template.html", htmlData); err != nil {
		logger.Stdout.Error(fmt.Sprintf("生成挂靠声明html失败: %v\n", err))
		return err
	}

	// 生成pdf
	cmd := exec.Command("wkhtmltopdf", "--no-outline", "--margin-top", "0", "--margin-right", "0", "--margin-bottom", "0", "--margin-left", "0", "--disable-smart-shrinking", "--load-media-error-handling", "abort", "--page-width", "210mm", "--page-height", "297mm", htmlFilename, pdfFilename)
	if err := cmd.Run(); err != nil {
		logger.Stdout.Error(fmt.Sprintf("生成挂靠声明pdf失败: %v\n", err))
		return err
	}

	// 上传OSS
	cli, err := oss.NewOSSByBucket("cfhswlhyhz")
	if err != nil {
		logger.Stdout.Error(fmt.Sprintf("连接OSS失败: %v\n", err))
		return err
	}
	if err := cli.PutObjectFromFile(filepath.Join("vehicle_statement_pdf", filepath.Base(pdfFilename)), pdfFilename); err != nil {
		logger.Stdout.Error(fmt.Sprintf("上传挂靠声明到OSS失败: %v\n", err))
		return err
	}

	// 更新数据库
	if err := model.DB.Table("tms_vehicle").
		Where("id = ?", vehicleID).
		Where("is_delete = ?", 0).
		Updates(map[string]any{
			"vehicle_affiliated_statement_pdf_url": "https://cfhswlhyhz.oss-cn-hangzhou.aliyuncs.com/vehicle_statement_pdf/" + filepath.Base(pdfFilename),
		}).Error; err != nil {
		logger.Stdout.Error(fmt.Sprintf("更新挂靠声明url失败: %v\n", err))
		return err
	}

	// 删除html文件
	os.Remove(htmlFilename)
	// 删除pdf文件
	os.Remove(pdfFilename)
	return nil
}

// ReDownloadInvoiceData 重新下载发票数据
func ReDownloadInvoiceData(sysOrgCode, token string) {
	f, err := excelize.OpenFile("1.xlsx")
	if err != nil {
		panic(err)
	}
	defer f.Close()

	excelRows, err := f.GetRows("Sheet1")
	if err != nil {
		panic(err)
	}

	vehicleRows, _ := model.DB.Table("tms_vehicle").
		Select([]string{"id", "vehicle_license_number"}).
		Where("is_delete = ?", 0).
		Rows()
	vehicleMap := make(map[string]string)
	for vehicleRows.Next() {
		var id string
		var vehicleLicenseNumber string
		if err := vehicleRows.Scan(&id, &vehicleLicenseNumber); err != nil {
			panic(err)
		}
		vehicleMap[vehicleLicenseNumber] = id
	}

	driverRows, _ := model.DB.Table("tms_driver").
		Select([]string{"driver_id", "identification_number"}).
		Where("is_delete = ?", 0).
		Rows()
	driverMap := make(map[string]string)
	for driverRows.Next() {
		var driverID string
		var identificationNumber string
		if err := driverRows.Scan(&driverID, &identificationNumber); err != nil {
			panic(err)
		}
		driverMap[identificationNumber] = driverID
	}

	tmpUnique := make(map[string][]string)
	for key, row := range excelRows {
		if key == 0 || len(row) < 6 {
			continue
		}

		name := strings.TrimSpace(row[1])
		idCard := strings.TrimSpace(row[2])
		plate := strings.TrimSpace(row[5])

		key := name + "_" + idCard

		if _, ok := tmpUnique[key]; !ok {
			tmpUnique[key] = []string{plate}
		} else {
			tmpUnique[key] = append(tmpUnique[key], plate)
		}
	}

	unique := make(map[string][]string)
	for k, v := range tmpUnique {
		unique[k] = lo.Uniq(v)
	}

	var wg sync.WaitGroup
	limit := make(chan bool, 10)

	for k, v := range unique {
		wg.Add(1)
		limit <- true
		go func(key string, plates []string) {
			defer func() {
				wg.Done()
				<-limit
			}()

			t := strings.Split(k, "_")
			name := t[0]
			idCard := t[1]

			if sysOrgCode == "A07" {
				GenerateTemplateSeal(token, driverMap[idCard])
				for _, plate := range plates {
					if err := GenerateManagementProve("", driverMap[idCard], vehicleMap[plate]); err != nil {
						logger.Stdout.Error(err.Error())
					}
				}
			}

			dirName := filepath.Join("datas", name+"_"+strings.Join(v, "_"))
			downloadInvoiceDataForProcess(idCard, dirName, "A03", v)
		}(k, v)
	}

	wg.Wait()

	fmt.Println("Done")
}

func downloadInvoiceDataForProcess(idCard, dirName, sysOrgCode string, plates []string) {
	// 检查目录是否存在
	_, err := os.Stat(dirName)
	if err != nil {
		if os.IsExist(err) {
			fmt.Printf("目录 %s 已存在\n", dirName)
			return
		}
		if os.IsNotExist(err) {
			fmt.Printf("目录 %s 不存在，创建目录\n", dirName)
			os.MkdirAll(dirName, 0777)
		}
	}

	var identificationFrontImgUrl sql.NullString
	var driverLicenseImgUrl sql.NullString
	var workCertificateImgUrl sql.NullString
	var contractUrl sql.NullString
	var driverName string

	// 获取驾驶员信息
	driverRows, err := model.DB.Table("tms_driver AS a").
		Joins("LEFT JOIN tms_user_agree AS b ON a.driver_id = b.user_id").
		Select([]string{
			"a.identification_front_img_url",
			"a.driver_license_img_url",
			"a.work_certificate_img_url",
			"b.contract_url",
			"a.driver_name",
		}).
		Where("a.identification_number = ?", idCard).
		Where("a.is_delete = ?", 0).
		Where("b.is_delete = ?", 0).
		Where("b.sys_org_code IS NULL OR b.sys_org_code = '' OR b.sys_org_code = ?", sysOrgCode).
		Limit(1).
		Rows()
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	for driverRows.Next() {
		if err := driverRows.Scan(
			&identificationFrontImgUrl,
			&driverLicenseImgUrl,
			&workCertificateImgUrl,
			&contractUrl,
			&driverName,
		); err != nil {
			logger.Stdout.Error(err.Error())
			return
		}
	}

	if identificationFrontImgUrl.Valid && identificationFrontImgUrl.String != "" {
		toolbox.DownloadFile(filepath.Join(dirName, "1.身份证"+filepath.Ext(identificationFrontImgUrl.String)), identificationFrontImgUrl.String)
	}
	if driverLicenseImgUrl.Valid && driverLicenseImgUrl.String != "" {
		toolbox.DownloadFile(filepath.Join(dirName, "2.驾驶证"+filepath.Ext(driverLicenseImgUrl.String)), driverLicenseImgUrl.String)
	}
	if workCertificateImgUrl.Valid && workCertificateImgUrl.String != "" {
		toolbox.DownloadFile(filepath.Join(dirName, "3.从业资格证"+filepath.Ext(workCertificateImgUrl.String)), workCertificateImgUrl.String)
	}
	if contractUrl.Valid && contractUrl.String != "" {
		toolbox.DownloadFile(filepath.Join(dirName, "4.税务代理委托书"+filepath.Ext(contractUrl.String)), contractUrl.String)
	}

	for _, plate := range plates {
		var drivingPermitImgUrl sql.NullString
		var driverLicenseFrontImgUrl sql.NullString
		var transportPermitImgUrl sql.NullString
		var transportPermitAdditionalImgUrl sql.NullString
		var vehicleAffiliatedStatementPdfUrl sql.NullString
		var vehicleOwner string

		// 获取车辆信息
		vehicleRows, err := model.DB.Table("tms_vehicle").
			Select([]string{
				"driving_permit_img_url",
				"driving_front_img_url",
				"transport_permit_img_url",
				"transport_permit_additional_img_url",
				"vehicle_affiliated_statement_pdf_url",
				"vehicle_owner",
			}).
			Where("vehicle_license_number = ?", plate).
			Where("is_delete = ?", 0).
			Limit(1).
			Rows()
		if err != nil {
			logger.Stdout.Error(err.Error())
			return
		}
		for vehicleRows.Next() {
			if err := vehicleRows.Scan(
				&drivingPermitImgUrl,
				&driverLicenseFrontImgUrl,
				&transportPermitImgUrl,
				&transportPermitAdditionalImgUrl,
				&vehicleAffiliatedStatementPdfUrl,
				&vehicleOwner,
			); err != nil {
				logger.Stdout.Error(err.Error())
				return
			}
		}
		//蒙D41430-1744883863061790722-赤峰市元宝山区-赤峰市红山区-merge

		// 下载文件

		if drivingPermitImgUrl.Valid && drivingPermitImgUrl.String != "" {
			toolbox.DownloadFile(filepath.Join(dirName, plate+"_"+"4.行驶证主页"+filepath.Ext(drivingPermitImgUrl.String)), drivingPermitImgUrl.String)
		}
		if driverLicenseFrontImgUrl.Valid && driverLicenseFrontImgUrl.String != "" {
			toolbox.DownloadFile(filepath.Join(dirName, plate+"_"+"5.行驶证副页"+filepath.Ext(driverLicenseFrontImgUrl.String)), driverLicenseFrontImgUrl.String)
		}
		if transportPermitImgUrl.Valid && transportPermitImgUrl.String != "" {
			toolbox.DownloadFile(filepath.Join(dirName, plate+"_"+"6.道路运输证"+filepath.Ext(transportPermitImgUrl.String)), transportPermitImgUrl.String)
		}

		if transportPermitAdditionalImgUrl.Valid && transportPermitAdditionalImgUrl.String != "" {
			toolbox.DownloadFile(filepath.Join(dirName, plate+"_"+"7.截图"+filepath.Ext(transportPermitAdditionalImgUrl.String)), transportPermitAdditionalImgUrl.String)
		}

		if vehicleOwner != "" && vehicleOwner != driverName {
			// fmt.Println(plate)
			// fmt.Println(vehicleOwner, driverName)
			if vehicleAffiliatedStatementPdfUrl.Valid && vehicleAffiliatedStatementPdfUrl.String != "" {
				// fmt.Println("8.挂靠声明已下载")
				toolbox.DownloadFile(filepath.Join(dirName, plate+"_"+"8.挂靠声明"+filepath.Ext(vehicleAffiliatedStatementPdfUrl.String)), vehicleAffiliatedStatementPdfUrl.String)
			} else {
				fmt.Println(plate + "8.挂靠声明无数据")
			}
		}

	}

	logger.Stdout.Info(fmt.Sprintf("Download %s Done\n", idCard))
}
