package user

import (
	"fmt"
	"time"
	"wlhy/model"
	"wlhy/toolbox/logger"

	"golang.org/x/exp/rand"
	"gorm.io/gorm"
)

type VehicleT struct {
	ID                   string
	VehicleLicenseNumber string
	CreateBy             string
}

type DriverT struct {
	ID                string
	DriverID          string
	DriverName        string
	MotorcadeId       string
	Phone             string
	AuditStatus       int
	AttestationStatus int
}

type CaptainRelT struct {
	ID               string
	VehicleID        string
	DriverID         string
	VehicleCaptainID string
	BindingStatus    int
}

type BindingAuditT struct {
	ID               string
	VehicleID        string
	DriverID         string
	VehicleCaptainID string
	AuditStatus      int
}

func BindCarMaster(masterPhone, driverPhone, vehicleLicenseNumber string) {
	// 查询基础数据
	// 查询车主数据
	var master DriverT
	if err := model.DB.Table("tms_driver").
		Select([]string{"id", "driver_id", "driver_name", "motorcade_id", "phone"}).
		Where("is_delete = ?", 0).
		Where("phone = ?", masterPhone).
		First(&master).Error; err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	// 查询司机数据
	var driver DriverT
	if err := model.DB.Table("tms_driver").
		Select([]string{"id", "driver_id", "driver_name", "motorcade_id", "phone"}).
		Where("is_delete = ?", 0).
		Where("phone = ?", driverPhone).
		First(&driver).Error; err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	// 查询车辆数据
	var vehicle VehicleT
	if err := model.DB.Table("tms_vehicle").
		Select([]string{"id", "vehicle_license_number", "create_by"}).
		Where("is_delete = ?", 0).
		Where("vehicle_license_number = ?", vehicleLicenseNumber).
		First(&vehicle).Error; err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	// 查询车主绑定审核数据
	var bindingAudit BindingAuditT
	if err := model.DB.Table("tms_driver_captain_binding_audit").
		Select([]string{"id", "vehicle_id", "driver_id", "vehicle_captain_id", "audit_status"}).
		Where("vehicle_id = ?", vehicle.ID).
		Where("driver_id = ?", driver.DriverID).
		Where("vehicle_captain_id = ?", master.DriverID).
		Where("audit_status = ?", 0).
		First(&bindingAudit).Error; err != nil && err != gorm.ErrRecordNotFound {
		logger.Stdout.Error(err.Error())
		return
	}
	if bindingAudit.ID != "" {
		logger.Stdout.Warn("已存在审核中的绑定申请，请等待审核")
		return
	}

	// 开启事务
	model.DB.Transaction(func(tx *gorm.DB) error {
		// 更新车辆表数据
		updateVehicleData := map[string]any{
			"binding_status":  1,
			"car_driver_id":   master.DriverID,
			"car_driver_name": master.DriverName,
			"car_phone":       master.Phone,
			"motorcade_id":    master.MotorcadeId,
		}
		var vehicleCount int64
		tx.Table("tms_vehicle").Where("car_driver_id = ?", master.DriverID).Count(&vehicleCount)
		if vehicleCount > 1 {
			updateVehicleData["is_use"] = 1
			updateVehicleData["driver_user"] = driver.DriverID
		}
		if err := tx.Table("tms_vehicle").Where("id = ?", vehicle.ID).Updates(updateVehicleData).Error; err != nil {
			logger.Stdout.Error(err.Error())
			return err
		}

		// 删除老数据
		if err := tx.Debug().Table("tms_driver_vehicle_captain_rel").
			Select([]string{"id", "vehicle_id", "driver_id", "vehicle_captain_id", "binding_status"}).
			Where("vehicle_id = ?", vehicle.ID).
			Where("driver_id = ?", driver.DriverID).
			Delete(nil).Error; err != nil {
			logger.Stdout.Error(err.Error())
			return err
		}

		if err := tx.Table("tms_driver_vehicle_captain_rel").Create(map[string]any{
			"id":                 fmt.Sprintf("%d%d", time.Now().UnixMicro(), rand.Intn(999999)),
			"driver_id":          driver.DriverID,
			"vehicle_id":         vehicle.ID,
			"vehicle_captain_id": master.DriverID,
			"binding_status":     1,
			"create_by":          driver.DriverID,
			"update_by":          driver.DriverID,
			"create_time":        time.Now().Format("2006-01-02 15:04:05"),
			"update_time":        time.Now().Format("2006-01-02 15:04:05"),
		}).Error; err != nil {
			logger.Stdout.Error(err.Error())
			return err
		}

		if err := tx.Table("tms_driver_captain_binding_audit").Create(map[string]any{
			"id":                     fmt.Sprintf("%d%d", time.Now().UnixMicro(), rand.Intn(999999)),
			"driver_id":              driver.DriverID,
			"driver_name":            driver.DriverName,
			"driver_phone":           driver.Phone,
			"vehicle_id":             vehicle.ID,
			"vehicle_license_number": vehicle.VehicleLicenseNumber,
			"vehicle_captain_id":     master.DriverID,
			"vehicle_captain_name":   master.DriverName,
			"vehicle_captain_phone":  master.Phone,
			"pic_url":                "",
			"create_by":              driver.DriverID,
			"update_by":              driver.DriverID,
			"create_time":            time.Now().Format("2006-01-02 15:04:05"),
			"update_time":            time.Now().Format("2006-01-02 15:04:05"),
			"audit_status":           1,
			"audit_remark":           "批量添加",
		}).Error; err != nil {
			logger.Stdout.Error(err.Error())
			return err
		}

		return nil
	})
}

func UnBindCarMaster(masterPhone, driverPhone, vehicleLicenseNumber string) {
	// 查询基础数据
	// 查询车主数据
	var master DriverT
	if err := model.DB.Table("tms_driver").
		Select([]string{"id", "driver_id", "driver_name", "motorcade_id", "phone"}).
		Where("is_delete = ?", 0).
		Where("phone = ?", masterPhone).
		First(&master).Error; err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	// 查询司机数据
	var driver DriverT
	if err := model.DB.Table("tms_driver").
		Select([]string{"id", "driver_id", "driver_name", "motorcade_id", "phone"}).
		Where("is_delete = ?", 0).
		Where("phone = ?", driverPhone).
		First(&driver).Error; err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	// 查询车辆数据
	var vehicle VehicleT
	if err := model.DB.Table("tms_vehicle").
		Select([]string{"id", "vehicle_license_number", "create_by"}).
		Where("is_delete = ?", 0).
		Where("vehicle_license_number = ?", vehicleLicenseNumber).
		First(&vehicle).Error; err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	var tmsDriverVehicleCaptainRel CaptainRelT
	if err := model.DB.Table("tms_driver_vehicle_captain_rel").
		Select([]string{"id", "vehicle_id", "driver_id", "vehicle_captain_id", "binding_status"}).
		Where("vehicle_id = ?", vehicle.ID).
		Where("driver_id = ?", driver.DriverID).
		Where("binding_status = ?", 1).
		First(&tmsDriverVehicleCaptainRel).Error; err != nil && err != gorm.ErrRecordNotFound {
		logger.Stdout.Error(err.Error())
		return
	}

	var rel CaptainRelT
	if err := model.DB.Table("tms_driver_vehicle_captain_rel").
		Select([]string{"id", "vehicle_id", "driver_id", "vehicle_captain_id", "binding_status"}).
		Where("vehicle_id = ?", vehicle.ID).
		Where("vehicle_captain_id = ?", master.DriverID).
		Where("binding_status = ?", 1).
		First(&rel).Error; err != nil && err != gorm.ErrRecordNotFound {
		logger.Stdout.Error(err.Error())
		return
	}

	if tmsDriverVehicleCaptainRel.ID == "" && rel.ID == "" {
		logger.Stdout.Warn("车辆未绑定车主")
		return
	}

	err := model.DB.Transaction(func(tx *gorm.DB) error {
		if tmsDriverVehicleCaptainRel.ID != "" {
			if err := tx.Table("tms_driver_vehicle_captain_rel").
				Where("id = ?", tmsDriverVehicleCaptainRel.ID).
				Updates(map[string]any{
					"vehicle_captain_id": vehicle.CreateBy,
					"driver_id":          vehicle.CreateBy,
				}).Error; err != nil {
				logger.Stdout.Error(err.Error())
				return err
			}

			var tmpDriver DriverT
			if err := tx.Table("tms_driver").
				Select([]string{"id", "driver_id", "driver_name", "motorcade_id", "phone", "audit_status", "attestation_status"}).
				Where("driver_id = ?", vehicle.CreateBy).
				First(&tmpDriver).Error; err != nil {
				logger.Stdout.Error(err.Error())
				return err
			}

			if tmpDriver.ID != "" && tmpDriver.AuditStatus == 2 && tmpDriver.AttestationStatus == 2 {
				if err := tx.Table("tms_vehicle").
					Where("id = ?", vehicle.ID).
					Updates(map[string]any{
						"driver_user": vehicle.CreateBy,
					}).Error; err != nil {
					logger.Stdout.Error(err.Error())
					return err
				}
			} else {
				if err := tx.Table("tms_vehicle").
					Where("id = ?", vehicle.ID).
					Updates(map[string]any{
						"driver_user": "",
					}).Error; err != nil {
					logger.Stdout.Error(err.Error())
					return err
				}
			}

		}

		if rel.ID != "" {
			if err := tx.Table("tms_driver_vehicle_captain_rel").
				Where("id = ?", rel.ID).
				Updates(map[string]any{
					"vehicle_captain_id": vehicle.CreateBy,
					"driver_id":          vehicle.CreateBy,
				}).Error; err != nil {
				logger.Stdout.Error(err.Error())
				return err
			}

			var tmpDriver DriverT
			if err := tx.Table("tms_driver").
				Select([]string{"id", "driver_id", "driver_name", "motorcade_id", "phone", "audit_status", "attestation_status"}).
				Where("driver_id = ?", vehicle.CreateBy).
				First(&tmpDriver).Error; err != nil {
				logger.Stdout.Error(err.Error())
				return err
			}

			if tmpDriver.ID != "" && tmpDriver.AuditStatus == 2 && tmpDriver.AttestationStatus == 2 {
				if err := tx.Table("tms_vehicle").
					Where("id = ?", vehicle.ID).
					Updates(map[string]any{
						"driver_user": vehicle.CreateBy,
					}).Error; err != nil {
					logger.Stdout.Error(err.Error())
					return err
				}
			} else {
				if err := tx.Table("tms_vehicle").
					Where("id = ?", vehicle.ID).
					Updates(map[string]any{
						"driver_user": "",
					}).Error; err != nil {
					logger.Stdout.Error(err.Error())
					return err
				}
			}
		}

		var vehicleBindCount int64
		if err := tx.Table("tms_vehicle_bind").
			Where("vehicle_id = ?", vehicle.ID).
			Where("is_delete = ?", 0).
			Where("driver_id != ?", driver.DriverID).
			Count(&vehicleBindCount).Error; err != nil {
			logger.Stdout.Error(err.Error())
			return err
		}

		if vehicleBindCount > 0 {
			if err := tx.Table("tms_vehicle").
				Where("id = ?", vehicle.ID).
				Updates(map[string]any{
					"binding_status": 1,
					"is_use":         1,
				}).Error; err != nil {
				logger.Stdout.Error(err.Error())
				return err
			}
		} else {
			if err := tx.Table("tms_vehicle").
				Where("id = ?", vehicle.ID).
				Updates(map[string]any{
					"binding_status": 0,
					"is_use":         0,
				}).Error; err != nil {
				logger.Stdout.Error(err.Error())
				return err
			}
		}

		if err := tx.Table("tms_vehicle").
			Where("id = ?", vehicle.ID).
			Updates(map[string]any{
				"binding_status":  0,
				"car_driver_id":   "",
				"car_driver_name": "",
				"car_phone":       "",
				"motorcade_id":    "",
				"update_time":     time.Now().Format("2006-01-02 15:04:05"),
			}).Error; err != nil {
			logger.Stdout.Error(err.Error())
			return err
		}
		return nil
	})

	if err != nil {
		logger.Stdout.Error(err.Error())
	}
}
