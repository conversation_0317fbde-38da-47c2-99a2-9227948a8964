package user

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"strings"
	"wlhy/model"
	"wlhy/toolbox/logger"

	"gorm.io/gorm"
)

// MigrateCompany 客户在子公司间互相迁移
func MigrateCompany() {
	type OrderT struct {
		ID          string `gorm:"column:id"`
		GoodsNumber string `gorm:"column:goods_number"`
	}

	// 基础数据
	// 需要迁移的客户公司id
	companyID := "1888127334211821569"
	// token
	token := "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiIxNzMyMzA1MjgwMzg4ODg2NTMwIiwicm5TdHIiOiIyNjVRZVJGdnREM3NiMXdvWHFVQ3k5YnZRNUt3OTZiYSJ9.hZkXwl3rPsBgmNB4e-L_Zf4qLleKAR7nPR37oo1HIsQ"
	// 目标子公司名称
	sysOrgCompanyName := "赤峰现代智慧物流有限公司"
	// 目标子公司id
	sysOrgCompanyID := "a82dc78ba61880beda018fa5eab6c944"
	// 目标子公司编码
	sysOrgCode := "A03"
	// 目标子公司货单编号前缀
	orderPrefix := "TYBM"
	// 目标子公司运单编号前缀
	transportPrefix := "CYBM"
	// 原子公司货单编号前缀
	oldOrderPrefix := "XYTY"
	// 原子公司运单编号前缀
	oldTransportPrefix := "XYCY"

	if len(sysOrgCode) == 0 || len(sysOrgCompanyID) == 0 || len(sysOrgCompanyName) == 0 || len(orderPrefix) == 0 || len(transportPrefix) == 0 || len(oldOrderPrefix) == 0 || len(oldTransportPrefix) == 0 {
		logger.Stdout.Error("子公司信息不完整")
		return
	}

	// 初始化http clien
	client := &http.Client{}

	// 更改货主归属公司
	err := model.DB.Transaction(func(tx *gorm.DB) error {
		if err := model.DB.Table("tms_shipper").Where("company_id = ?", companyID).Updates(map[string]any{
			"sys_org_code":      sysOrgCode,
			"select_company_id": sysOrgCompanyID,
		}).Error; err != nil {
			return err
		}

		if err := model.DB.Table("sys_user_branch_office").Where("user_id = ?", companyID).Updates(map[string]any{
			"sys_org_code":          sysOrgCode,
			"company_id":            sysOrgCompanyID,
			"platform_company_name": sysOrgCompanyName,
		}).Error; err != nil {
			return err
		}

		// TODO: 设置费率

		return nil
	})
	if err != nil {
		logger.Stdout.Error("更改货主归属公司失败:" + err.Error())
		return
	}

	// 关闭货单
	if err := model.DB.Table("tms_order").
		Where("create_company_id = ?", companyID).
		Updates(map[string]any{
			"is_source": 1,
		}).Error; err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	fmt.Println("更改货主归属公司成功")
	os.Exit(1)

	// 查询全部的货单
	var orders []OrderT
	if err := model.DB.Table("tms_order").
		Where("create_company_id = ?", companyID).
		Find(&orders).Error; err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	if len(orders) == 0 {
		logger.Stdout.Error("没有找到货单")
		return
	}

	err = model.DB.Transaction(func(tx *gorm.DB) error {
		for _, v := range orders {
			newGoodsNumber := strings.ReplaceAll(v.GoodsNumber, oldOrderPrefix, orderPrefix)
			// 更改货单的归属
			if err := model.DB.Table("tms_order").Where("id = ?", v.ID).Updates(map[string]any{
				"appoint_company_name": sysOrgCompanyName,
				"appoint_company_id":   sysOrgCompanyID,
				"goods_number":         newGoodsNumber,
				"sys_org_code":         sysOrgCode,
			}).Error; err != nil {
				return err
			}
			if err := model.DB.Table("tms_order_history").Where("id = ?", v.ID).Updates(map[string]any{
				"appoint_company_name": sysOrgCompanyName,
				"appoint_company_id":   sysOrgCompanyID,
				"goods_number":         newGoodsNumber,
				"sys_org_code":         sysOrgCode,
			}).Error; err != nil {
				return err
			}

			fmt.Printf("更改货单的归属，ID:%s, 旧货单号：%s, 新货单号：%s\n", v.ID, v.GoodsNumber, newGoodsNumber)

			// 更改运单归属
			rows, err := model.DB.Table("tms_transport_note").
				Select([]string{"id", "payee_agent_user_id", "transportation_number"}).
				Where("order_id = ?", v.ID).
				Rows()
			if err != nil {
				logger.Stdout.Error(err.Error())
				return err
			}

			for rows.Next() {
				var id string
				var payeeAgentUserID string
				var transportationNumber string
				if err := rows.Scan(&id, &payeeAgentUserID, &transportationNumber); err != nil {
					logger.Stdout.Error(err.Error())
					return err
				}

				// 重新生成合同
				if err := generateTransportContract(client, token, id); err != nil {
					logger.Stdout.Error("重新生成合同失败:" + err.Error())
					continue
				}

				// 生成子单元编号
				if err := generateSubUnitNumber(client, token, payeeAgentUserID); err != nil {
					logger.Stdout.Error("生成子单元编号失败:" + err.Error())
					continue
				}

				// 更改运单号
				newTransportationNumber := strings.ReplaceAll(transportationNumber, oldTransportPrefix, transportPrefix)
				if err := model.DB.Table("tms_transport_note").
					Where("id = ?", id).
					Updates(map[string]any{
						"transportation_number": newTransportationNumber,
						"sys_org_code":          sysOrgCode,
					}).Error; err != nil {
					logger.Stdout.Error(err.Error())
					return err
				}
				fmt.Printf("更改运单号ID:%s, 旧运单号：%s, 新运单号: %s\n", id, transportationNumber, newTransportationNumber)
			}
		}

		return nil
	})
	if err != nil {
		logger.Stdout.Error("更改货单归属失败:" + err.Error())
		return
	}

	fmt.Println("Done")
}

// generateTransportContract 重新生成合同
func generateTransportContract(client *http.Client, token, id string) error {
	// request
	req, err := http.NewRequest("POST", "https://www.cfhszy.com/gw/hszy-order/ntocc/tmsTransportNote/regenerateContract", bytes.NewBuffer([]byte(id)))
	if err != nil {
		return err
	}
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("x-access-token", token)

	// response
	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		return fmt.Errorf("%s", resp.Status)
	}

	_, err = io.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	return nil
}

// generateSubUnitNumber 生成子单元编号
func generateSubUnitNumber(client *http.Client, token, payeeAgentUserID string) error {
	// body
	b := map[string]string{
		"driverId": payeeAgentUserID,
	}
	bj, _ := json.Marshal(b)

	// request
	req, err := http.NewRequest("POST", "https://www.cfhszy.com/gw/hszy-payment/ntocc/sysZhaoshangAccount/insertDriverAccounts", bytes.NewBuffer(bj))
	if err != nil {
		return err
	}
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("x-access-token", token)

	// response
	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		return fmt.Errorf("%s", resp.Status)
	}

	_, err = io.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	return nil
}
