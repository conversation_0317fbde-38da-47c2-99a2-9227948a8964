package user

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"os"
	"strconv"
	"strings"
	"sync"
	"time"
	"wlhy/model"
	"wlhy/thirdparty/zwl"
	"wlhy/toolbox/logger"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type LocationSave struct {
	ID                   int
	TransportationNumber string
	VehicleLicenseNumber string
	LoadingTime          time.Time
	UnloadTime           time.Time
	Locs                 string
}

// SaveLocation 保存车辆轨迹数据
func (l *LocationSave) SaveLocation(startTime, endTime string) {
	f, _ := os.OpenFile("record.txt", os.O_CREATE|os.O_APPEND|os.O_WRONLY, 0755)
	defer f.Close()

	zwl := zwl.NewZWL()

	rr, err := model.DB.Table("tms_vehicle_locations_save").
		Select([]string{"transportation_number"}).
		Where("loading_time BETWEEN ? AND ?", startTime, endTime).
		Rows()
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	exists := make(map[string]struct{})
	for rr.Next() {
		var transportationNumber string
		if err := rr.Scan(&transportationNumber); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}
		exists[transportationNumber] = struct{}{}
	}

	rows, _ := model.DB.Table("tms_transport_note").
		Select([]string{"transportation_number", "transportation_plate", "loading_time", "unload_time"}).
		Where("waybill_status NOT IN ?", []int{6, 7}).
		Where("loading_time BETWEEN ? AND ?", startTime, endTime).
		Rows()

	var mu sync.Mutex
	var wg sync.WaitGroup
	limit := make(chan bool, 10)
	for rows.Next() {
		var transportationNumber string
		var transportationPlate string
		var loadingTime sql.NullTime
		var unloadTime sql.NullTime

		if err := rows.Scan(&transportationNumber, &transportationPlate, &loadingTime, &unloadTime); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}

		if loadingTime.Valid && unloadTime.Valid {

			wg.Add(1)
			limit <- true
			go func(transportationNumber, transportationPlate string, loadingTime, unloadTime sql.NullTime) {
				defer func() {
					if r := recover(); r != nil {
						logger.Stdout.Error(fmt.Sprintf("%v", r))
					}
					<-limit
					wg.Done()
				}()

				if l.locationIsInMongo(transportationNumber) {
					return
				}

				if _, ok := exists[transportationNumber]; ok {
					return
				}

				result, err := zwl.RouterPathRow(transportationPlate, loadingTime.Time.Format("2006-01-02 15:04:05"), unloadTime.Time.Format("2006-01-02 15:04:05"))
				if err != nil {
					logger.Stdout.Error(err.Error())
					return
				}

				mu.Lock()
				defer mu.Unlock()
				f.WriteString(fmt.Sprintf("%s %s\n", transportationNumber, transportationPlate))

				if result != "" {
					if err := model.DB.Table("tms_vehicle_locations_save").Create(&LocationSave{
						TransportationNumber: transportationNumber,
						VehicleLicenseNumber: transportationPlate,
						LoadingTime:          loadingTime.Time,
						UnloadTime:           unloadTime.Time,
						Locs:                 result,
					}).Error; err != nil {
						logger.Stdout.Error(err.Error())
						return
					}
					fmt.Printf("%s %s Done\n", transportationNumber, transportationPlate)
				}
			}(transportationNumber, transportationPlate, loadingTime, unloadTime)
		}
	}
	wg.Wait()
}

func (l *LocationSave) MigrateLocationHistory(startTime, endTime string) {
	client := l.mongodbClient()
	defer func() {
		if err := client.Disconnect(context.TODO()); err != nil {
			logger.Stdout.Error(err.Error())
		}
	}()
	coll := client.Database("admin").Collection("vehicle_track")

	var total int64
	model.DB.Table("tms_vehicle_locations_save").Where("loading_time BETWEEN ? AND ?", startTime, endTime).Count(&total)
	pages := int(math.Ceil(float64(total) / 1000))

	fmt.Printf("total: %d, pages: %d\n", total, pages)

	for i := 1; i <= pages; i++ {
		var results []LocationSave
		if err := model.DB.Table("tms_vehicle_locations_save").
			Where("loading_time BETWEEN ? AND ?", startTime, endTime).
			Offset((i - 1) * 1000).
			Limit(1000).
			Find(&results).
			Error; err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}

		docs := []any{}
		limit := 0
		for _, v := range results {
			if limit == 10 {
				ids, err := coll.InsertMany(context.TODO(), docs)
				if err != nil {
					logger.Stdout.Error(err.Error())
					continue
				}
				fmt.Println(ids.InsertedIDs...)

				docs = []any{}
				limit = 0
			}

			var result bson.M
			err := coll.FindOne(context.TODO(), bson.D{{Key: "_id", Value: v.TransportationNumber}}).Decode(&result)
			if err != nil {
				if errors.Is(err, mongo.ErrNoDocuments) {
					locs := v.Locs
					if !strings.Contains(locs, "无结果") {

						var ll zwl.RouterPathResp
						if err := json.Unmarshal([]byte(v.Locs), &ll); err != nil {
							logger.Stdout.Error(v.TransportationNumber)
							logger.Stdout.Error(err.Error())
							continue
						}

						for k, v := range ll.Result.ParkArray {
							lat, _ := strconv.ParseFloat(v.ParkLat, 64)
							ll.Result.ParkArray[k].ParkLat = fmt.Sprintf("%f", lat/600000)

							lon, _ := strconv.ParseFloat(v.ParkLon, 64)
							ll.Result.ParkArray[k].ParkLon = fmt.Sprintf("%f", lon/600000)

							parkBte, _ := strconv.ParseInt(v.ParkBte, 10, 64)
							seconds1 := parkBte / 1000
							nanoseconds1 := (parkBte % 1000) * 1000000 // 毫秒转纳秒
							bte := time.Unix(seconds1, nanoseconds1)
							ll.Result.ParkArray[k].ParkBte = bte.Format("2006-01-02 15:04:05")

							parkEte, _ := strconv.ParseInt(v.ParkEte, 10, 64)
							seconds2 := parkEte / 1000
							nanoseconds2 := (parkEte % 1000) * 1000000 // 毫秒转纳秒
							ete := time.Unix(seconds2, nanoseconds2)
							ll.Result.ParkArray[k].ParkEte = ete.Format("2006-01-02 15:04:05")
						}

						for k, v := range ll.Result.TrackArray {
							lat, _ := strconv.ParseFloat(v.Lat, 64)
							ll.Result.TrackArray[k].Lat = fmt.Sprintf("%f", lat/600000)

							lon, _ := strconv.ParseFloat(v.Lon, 64)
							ll.Result.TrackArray[k].Lon = fmt.Sprintf("%f", lon/600000)

							t, _ := time.Parse("20060102/150405", v.Gtm)
							ll.Result.TrackArray[k].Gtm = t.Format("2006-01-02 15:04:05")
						}

						upnl, err := json.Marshal(ll)
						if err != nil {
							logger.Stdout.Error(err.Error())
							continue
						}

						l := make(map[string]any)
						if err := json.Unmarshal(upnl, &l); err != nil {
							logger.Stdout.Error(err.Error())
							continue
						}

						delete(l, "status")
						l["success"] = true
						l["message"] = "操作成功"
						l["code"] = 200

						nl, err := json.Marshal(l)
						if err != nil {
							logger.Stdout.Error(err.Error())
							continue
						}

						locs = string(nl)
					}
					docs = append(docs, bson.D{{Key: "_id", Value: v.TransportationNumber}, {Key: "vehicle_number", Value: v.VehicleLicenseNumber}, {Key: "track_data", Value: locs}, {Key: "create_time", Value: v.LoadingTime}, {Key: "_class", Value: "com.hszy.transport.domain.po.VehicleTrack"}})
					limit++
				} else {
					logger.Stdout.Error(err.Error())
				}
			}
		}

		ids, err := coll.InsertMany(context.TODO(), docs)
		if err != nil {
			logger.Stdout.Error(err.Error())
			return
		}

		fmt.Println(ids.InsertedIDs...)
		fmt.Printf("第 %d 页插入完成\n", i)
	}

	fmt.Println("Done")
}

func (l *LocationSave) mongodbClient() *mongo.Client {
	uri := "************************************************************"
	client, err := mongo.Connect(context.TODO(), options.Client().
		ApplyURI(uri))
	if err != nil {
		logger.Stdout.Error(err.Error())
		return nil
	}
	return client
}

func (l *LocationSave) locationIsInMongo(transportationNumber string) bool {
	type LocationForMongo struct {
		ID            primitive.ObjectID `bson:"_id"`
		VehicleNumber string             `bson:"vehicle_number"`
		TrackData     string             `bson:"track_data"`
		CreateTime    time.Time          `bson:"create_time"`
		Class         string             `bson:"_class"`
	}

	client := l.mongodbClient()
	defer func() {
		if err := client.Disconnect(context.TODO()); err != nil {
			logger.Stdout.Error(err.Error())
		}
	}()
	coll := client.Database("admin").Collection("vehicle_track")

	var result LocationForMongo
	if err := coll.FindOne(context.TODO(), bson.D{{Key: "_id", Value: transportationNumber}}).Decode(&result); err != nil {
		return false
	}

	return true
}
