<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>挂靠声明</title>
    <style>
      @font-face {
        font-family: customFont;
        /*本地字体文件路径*/
        src: url("https://appcdn.cfhszy.com/cdn/fonts/DroidSansFallback.ttf");
      }

      * {
        margin: 0;
        padding: 0;
        font-family: "customFont";
      }

      body {
        width: 210mm;
        margin: 0;
        padding: 0;
      }

      .page {
        width: 210mm;
        height: 297mm;
        margin: 0;
        padding: 0;
      }
      .container {
        width: 180mm;
        margin-left: 60px;
        line-height: 30px;
      }
      .underline {
        border-bottom: 1px solid;
        padding: 0 10px;
      }
    </style>
  </head>
  <body>
    <div class="page">
      <div class="container">
        <div style="height: 100px"></div>
        <h1 style="text-align: center; margin-bottom: 25px">挂靠声明</h1>
        <div style="margin-bottom: 25px">赤峰现代智慧物流有限公司：</div>

        <div style="text-indent: 2em; margin-bottom: 25px">
          本人：<span class="underline">{{.Driver.RealName}}</span
          >（身份证号码：<span class="underline"
            >{{.Driver.IdentificationNumber}}</span
          >），为贵司运营的“红山智运”APP（以下简称“平台”）的注册用户。本人在平台注册并实际驾驶的车牌号为<span
            class="underline"
            >{{.Vehicle.VehicleLicenseNumber}}</span
          >的车辆，其行驶证记载的车辆所有人为<span class="underline"
            >{{.Vehicle.VehicleOwner}}</span
          >，事实上该车辆为本人出资购置，车辆实际所有人为本人，与<span
            class="underline"
            >{{.Vehicle.VehicleOwner}}</span
          >仅为挂靠合作，车辆日常经营由本人负责。
        </div>

        <div style="text-align: left; margin-bottom: 25px">特此声明！</div>

        <div style="text-align: right; margin-bottom: 25px">
          声明人： {{.Driver.RealName}}
        </div>
        <div style="text-align: right; margin-bottom: 25px">
          身份证号： {{.Driver.IdentificationNumber}}
        </div>
        <div style="text-align: right; margin-bottom: 25px">
          日期： {{.Date}}
        </div>
        <img
          src="data:image/png;base64,{{.Driver.TemplateSeal}}"
          style="width: 100px; position: absolute; top: 455px; left: 600px"
          alt=""
        />
      </div>
    </div>
  </body>
</html>
