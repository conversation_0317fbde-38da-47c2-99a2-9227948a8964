package payment

import (
	"database/sql"
	"fmt"
	"log"
	"strings"
	"time"
	"wlhy/model"
	"wlhy/thirdparty/cmb"
	"wlhy/toolbox/logger"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

// InsertRoadLossRecord 写入路耗运单记录
// transportationNumbers 运单号
// goodsValue 货物价值
// settlementMethod 结算方式
func InsertRoadLossRecord(transportationNumbers []string, goodsValue float64, settlementMethod int) {
	rows, err := model.DB.Table("tms_transport_note").
		Select([]string{"id", "transportation_driver_id", "transportation_number"}).
		Where("transportation_number IN (?)", transportationNumbers).
		Rows()
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	model.DB.Transaction(func(tx *gorm.DB) error {
		for rows.Next() {
			var id int
			var transportationDriverID int
			var transportationNumber string
			if err := rows.Scan(&id, &transportationDriverID, &transportationNumber); err != nil {
				logger.Stdout.Error(err.Error())
				continue
			}

			var count int64
			if err := tx.Table("tms_road_loss_transport_note").
				Where("transportation_number = ?", transportationNumber).
				Count(&count).Error; err != nil {
				logger.Stdout.Error(err.Error())
				continue
			}
			if count > 0 {
				continue
			}

			if err := tx.Table("tms_road_loss_transport_note").Create(map[string]any{
				"id":                          id,
				"create_by":                   transportationDriverID,
				"create_time":                 time.Now().Format("2006-01-02 15:04:05"),
				"transportation_number":       transportationNumber,
				"is_delete":                   0,
				"road_loss_state":             1,
				"freight_accounting_status":   1,
				"goods_value":                 goodsValue,
				"road_loss_settlement_method": settlementMethod,
			}).Error; err != nil {
				logger.Stdout.Error(err.Error())
				return err
			}
		}

		return nil
	})
}

// CompanyRefund 货主退款（非提现）
func CompanyRefund(companyName string, refundAmount float64) {
	type CompanyInfo struct {
		CompanyID           string
		CompanyName         string
		ShipperName         string
		ShipperPhone        string
		SysOrgCode          string
		AccountBalanceLocal float64
	}
	var company CompanyInfo
	model.DB.Table("tms_shipper AS a").
		Joins("JOIN tms_shipper_balance AS b ON a.company_id = b.shipper_id").
		Select([]string{"a.company_id", "a.company_name", "a.shipper_name", "a.shipper_phone", "a.sys_org_code",
			"b.account_balance_local"}).
		Where("a.company_name LIKE ?", "%"+companyName+"%").
		Where("a.shipper_type = ?", 1).
		Where("a.is_delete = ?", 0).
		Scan(&company)
	if company.CompanyID == "" {
		logger.Stdout.Warn("公司不存在")
		return
	}

	fmt.Printf("%0.2f\n", company.AccountBalanceLocal)
	fmt.Printf("%0.2f\n", company.AccountBalanceLocal+refundAmount)

	if err := model.DB.Table("tms_expense_record").Create(map[string]any{
		"serial_number":                 strings.ReplaceAll(uuid.New().String(), "-", ""),
		"serial_number_type":            0,
		"reasons_for_change":            71, // 运费退款
		"transaction_amount":            refundAmount,
		"account_balance":               company.AccountBalanceLocal + refundAmount, // 运费退款后的余额
		"revenue_and_expenditure_types": 0,
		"account_type":                  1,
		"account_name":                  company.ShipperName,
		"account_number":                company.ShipperPhone,
		"account_id":                    company.CompanyID,
		"remark":                        "运费退款",
		"create_by":                     company.CompanyID,
		"create_time":                   time.Now().Format("2006-01-02 15:04:05"),
		"consumption_time":              time.Now().Format("2006-01-02 15:04:05"),
		"transaction_time":              time.Now().Format("2006-01-02 15:04:05"),
		"trade_status":                  1,
		"sys_org_code":                  company.SysOrgCode,
	}).Error; err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	// 更新余额
	if err := model.DB.Table("tms_shipper_balance").
		Where("shipper_id = ?", company.CompanyID).
		Update("account_balance_local", company.AccountBalanceLocal+refundAmount).Error; err != nil {
		logger.Stdout.Error(err.Error())
	}
}

// CompanyRWithdrawal 货主提现
func CompanyWithdrawal(companyName string, withdrawalAmount float64) {
	type CompanyInfo struct {
		CompanyID           string
		CompanyName         string
		ShipperName         string
		ShipperPhone        string
		SysOrgCode          string
		AccountBalanceLocal float64
	}
	var company CompanyInfo
	model.DB.Table("tms_shipper AS a").
		Joins("JOIN tms_shipper_balance AS b ON a.company_id = b.shipper_id").
		Select([]string{"a.company_id", "a.company_name", "a.shipper_name", "a.shipper_phone", "a.sys_org_code",
			"b.account_balance_local"}).
		Where("a.company_name LIKE ?", "%"+companyName+"%").
		Where("a.shipper_type = ?", 1).
		Where("a.is_delete = ?", 0).
		Scan(&company)
	if company.CompanyID == "" {
		logger.Stdout.Warn("公司不存在")
		return
	}

	fmt.Printf("%0.2f\n", company.AccountBalanceLocal)
	fmt.Printf("%0.2f\n", company.AccountBalanceLocal-withdrawalAmount)

	if err := model.DB.Table("tms_expense_record").Create(map[string]any{
		"serial_number":                 strings.ReplaceAll(uuid.New().String(), "-", ""),
		"serial_number_type":            0,
		"reasons_for_change":            7, // 提现
		"transaction_amount":            withdrawalAmount,
		"account_balance":               company.AccountBalanceLocal - withdrawalAmount, // 提现后的余额
		"revenue_and_expenditure_types": 1,
		"account_type":                  1,
		"account_name":                  company.ShipperName,
		"account_number":                company.ShipperPhone,
		"account_id":                    company.CompanyID,
		"remark":                        "提现",
		"create_by":                     company.CompanyID,
		"create_time":                   time.Now().Format("2006-01-02 15:04:05"),
		"consumption_time":              time.Now().Format("2006-01-02 15:04:05"),
		"transaction_time":              time.Now().Format("2006-01-02 15:04:05"),
		"trade_status":                  1,
		"sys_org_code":                  company.SysOrgCode,
	}).Error; err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	// 更新余额
	if err := model.DB.Table("tms_shipper_balance").
		Where("shipper_id = ?", company.CompanyID).
		Update("account_balance_local", company.AccountBalanceLocal-withdrawalAmount).Error; err != nil {
		logger.Stdout.Error(err.Error())
	}
}

// CompanyTopUp 货主充值
func CompanyTopUp(companyName string, topUpAmount float64) {
	type CompanyInfo struct {
		CompanyID           string
		CompanyName         string
		ShipperName         string
		ShipperPhone        string
		SysOrgCode          string
		AccountBalanceLocal float64
	}
	var company CompanyInfo
	model.DB.Table("tms_shipper AS a").
		Joins("JOIN tms_shipper_balance AS b ON a.company_id = b.shipper_id").
		Select([]string{"a.company_id", "a.company_name", "a.shipper_name", "a.shipper_phone", "a.sys_org_code",
			"b.account_balance_local"}).
		Where("a.company_name LIKE ?", "%"+companyName+"%").
		Where("a.shipper_type = ?", 1).
		Where("a.is_delete = ?", 0).
		Scan(&company)
	if company.CompanyID == "" {
		logger.Stdout.Warn("公司不存在")
		return
	}

	fmt.Printf("%0.2f\n", company.AccountBalanceLocal)
	fmt.Printf("%0.2f\n", company.AccountBalanceLocal+topUpAmount)

	if err := model.DB.Table("tms_expense_record").Create(map[string]any{
		"serial_number":                 strings.ReplaceAll(uuid.New().String(), "-", ""),
		"serial_number_type":            0,
		"reasons_for_change":            0, // 充值
		"transaction_amount":            topUpAmount,
		"account_balance":               company.AccountBalanceLocal + topUpAmount, // 充值后的余额
		"revenue_and_expenditure_types": 0,
		"account_type":                  1,
		"account_name":                  company.ShipperName,
		"account_number":                company.ShipperPhone,
		"account_id":                    company.CompanyID,
		"remark":                        "充值",
		"create_by":                     company.CompanyID,
		"create_time":                   time.Now().Format("2006-01-02 15:04:05"),
		"consumption_time":              time.Now().Format("2006-01-02 15:04:05"),
		"transaction_time":              time.Now().Format("2006-01-02 15:04:05"),
		"trade_status":                  1,
		"sys_org_code":                  company.SysOrgCode,
	}).Error; err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	// 更新余额
	if err := model.DB.Table("tms_shipper_balance").
		Where("shipper_id = ?", company.CompanyID).
		Update("account_balance_local", company.AccountBalanceLocal+topUpAmount).Error; err != nil {
		logger.Stdout.Error(err.Error())
	}
}

// CompanyAmountRecycle 货主借款余额回收
func CompanyAmountRecycle(companyName string, amountRecycle float64) {
	type CompanyInfo struct {
		CompanyID           string
		CompanyName         string
		ShipperName         string
		ShipperPhone        string
		SysOrgCode          string
		AccountBalanceLocal float64
	}
	var company CompanyInfo
	model.DB.Table("tms_shipper AS a").
		Joins("JOIN tms_shipper_balance AS b ON a.company_id = b.shipper_id").
		Select([]string{"a.company_id", "a.company_name", "a.shipper_name", "a.shipper_phone", "a.sys_org_code",
			"b.account_balance_local"}).
		Where("a.company_name LIKE ?", "%"+companyName+"%").
		Where("a.shipper_type = ?", 1).
		Where("a.is_delete = ?", 0).
		Scan(&company)
	if company.CompanyID == "" {
		logger.Stdout.Warn("公司不存在")
		return
	}

	fmt.Printf("%0.2f\n", company.AccountBalanceLocal)
	fmt.Printf("%0.2f\n", company.AccountBalanceLocal-amountRecycle)

	if err := model.DB.Table("tms_expense_record").Create(map[string]any{
		"serial_number":                 strings.ReplaceAll(uuid.New().String(), "-", ""),
		"serial_number_type":            0,
		"reasons_for_change":            72, // 预支回收
		"transaction_amount":            amountRecycle,
		"account_balance":               company.AccountBalanceLocal - amountRecycle, // 预支回收后的余额
		"revenue_and_expenditure_types": 1,
		"account_type":                  1,
		"account_name":                  company.ShipperName,
		"account_number":                company.ShipperPhone,
		"account_id":                    company.CompanyID,
		"remark":                        "预支回收",
		"create_by":                     company.CompanyID,
		"create_time":                   time.Now().Format("2006-01-02 15:04:05"),
		"consumption_time":              time.Now().Format("2006-01-02 15:04:05"),
		"transaction_time":              time.Now().Format("2006-01-02 15:04:05"),
		"trade_status":                  1,
		"sys_org_code":                  company.SysOrgCode,
	}).Error; err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	// 更新余额
	if err := model.DB.Table("tms_shipper_balance").
		Where("shipper_id = ?", company.CompanyID).
		Update("account_balance_local", company.AccountBalanceLocal-amountRecycle).Error; err != nil {
		logger.Stdout.Error(err.Error())
	}
}

// ToPrepaid 到付运费转预付运费
func ToPrepaid(companyName string, transportations []string) {
	if len(transportations) == 0 {
		log.Fatalln("请输入运单编号")
	}
	type PlanT struct {
		WaybillNo         string  `gorm:"column:waybill_No" json:"waybill_No"`
		ServiceAmount     float64 `gorm:"column:service_Amount" json:"service_Amount"`
		TotalAmount       float64 `gorm:"column:total_Amount" json:"total_Amount"`
		ShipperAccBalance float64 `gorm:"column:shipper_acc_balance" json:"shipper_acc_balance"`
		TransportAmount   float64 `gorm:"column:transport_Amount" json:"transport_Amount"`
		CommissionAmount  float64 `gorm:"column:commission_Amount" json:"commission_Amount"`
	}

	type NoteT struct {
		TransportationNumber string `gorm:"column:transportation_number" json:"transportation_number"`
		OrderID              string `gorm:"column:order_id" json:"order_id"`
		OrderVersionNo       int    `gorm:"column:order_version_no" json:"order_version_no"`
	}

	totalServiceAmount := 0.0

	for _, v := range transportations {
		err := model.DB.Transaction(func(tx *gorm.DB) error {
			var plan PlanT

			// 服务费统计，让财务退回
			if err := tx.Table("tms_transport_pay_plan").
				Select([]string{"waybill_No", "service_Amount", "total_Amount", "shipper_acc_balance", "transport_Amount", "commission_Amount"}).
				Where("waybill_No = ?", v).
				Where("is_delete = ?", 0).
				Order("create_time ASC").
				First(&plan).Error; err != nil {
				return err
			}
			totalServiceAmount += plan.ServiceAmount

			// 修改支付类型/修改流水记录/修改提现记录
			if err := tx.Table("tms_transport_pay_plan").
				Where("waybill_No = ?", v).
				Updates(map[string]any{
					"type":                2,
					"total_Amount":        plan.TotalAmount - plan.ServiceAmount,
					"service_Rate":        99,
					"service_Amount":      0,
					"is_oneself":          1,
					"shipper_acc_balance": plan.ShipperAccBalance + plan.ServiceAmount,
				}).Error; err != nil {
				return err
			}

			// 修改流水记录
			// 将货主支出改为预付金额（即司机运费）
			if err := tx.Table("tms_expense_record").
				Where("waybill_number = ?", v).
				Where("reasons_for_change = ?", 99).
				Updates(map[string]any{
					"transaction_amount": plan.TransportAmount + plan.CommissionAmount,
				}).Error; err != nil {
				return err
			}

			// 将货主支出的司机运费后的货主账户余额改为未扣减服务费的余额
			var accountBalance float64
			if err := tx.Table("tms_expense_record").
				Select([]string{"account_balance"}).
				Where("waybill_number = ?", v).
				Where("revenue_and_expenditure_types = ?", 1).
				Where("reasons_for_change = ?", 2).
				Limit(1).
				Scan(&accountBalance).Error; err != nil {
				return err
			}

			if err := tx.Table("tms_expense_record").
				Where("waybill_number = ?", v).
				Where("revenue_and_expenditure_types = ?", 1).
				Where("reasons_for_change = ?", 2).
				Updates(map[string]any{
					"account_balance": accountBalance + plan.ServiceAmount,
				}).Error; err != nil {
				return err
			}

			// 将司机收入的运费改为预付
			if err := tx.Table("tms_expense_record").
				Where("waybill_number = ?", v).
				Where("revenue_and_expenditure_types = ?", 0).
				Where("reasons_for_change = ?", 2).
				Updates(map[string]any{
					"reasons_for_change": 1,
				}).Error; err != nil {
				return err
			}

			// 删除服务费流水
			if err := tx.Table("tms_expense_record").
				Where("waybill_number = ?", v).
				Where("reasons_for_change = ?", 4).
				Updates(map[string]any{
					"is_delete": 1,
				}).Error; err != nil {
				return err
			}

			// 修改提现记录
			// 修改提现类型
			if err := tx.Table("tms_withdraw_application").
				Where("transportation_number = ?", v).
				Updates(map[string]any{
					"type": 2,
				}).Error; err != nil {
				return err
			}

			// 修改结算方式/修改运单状态
			var note NoteT
			if err := tx.Table("tms_transport_note").
				Select([]string{"transportation_number", "order_id", "order_version_no"}).
				Where("transportation_number = ?", v).
				First(&note).Error; err != nil {
				return err
			}

			if err := tx.Table("tms_transport_note").
				Where("transportation_number = ?", v).
				Updates(map[string]any{
					"radio_value":          2,
					"waybill_status":       9,
					"is_confirm_receipt":   0,
					"is_apply_remit":       0,
					"is_freight":           0,
					"total_freight_status": 0,
					"service_fee_paid":     0,
				}).Error; err != nil {
				return err
			}

			if err := tx.Table("tms_order_history").
				Where("id = ? AND version_no = ?", note.OrderID, note.OrderVersionNo).
				Updates(map[string]any{
					"radio_value": 2,
				}).Error; err != nil {
				return err
			}

			fmt.Printf("%s - 需退回服务费金额: %f\n", v, plan.ServiceAmount)

			// 查询货主shipper_id
			var shipperId string
			if err := tx.Table("tms_shipper").
				Select([]string{"shipper_id"}).
				Where("company_name = ?", companyName).
				Where("shipper_type = ? AND is_delete = ?", 1, 0).
				Scan(&shipperId).Error; err != nil {
				fmt.Println(err)
				return err
			}

			// 查询货主余额
			var shipperAccount float64
			if err := tx.Table("tms_shipper_balance").
				Where("shipper_id = ?", shipperId).
				Select([]string{"account_balance_local"}).
				Scan(&shipperAccount).Error; err != nil {
				fmt.Println(err)
				return err
			}
			fmt.Printf("%s - 修改前货主余额:%0.2f\n", v, shipperAccount)

			// 修改货主余额
			if err := tx.Table("tms_shipper_balance").
				Where("shipper_id = ?", shipperId).
				Updates(map[string]any{
					"account_balance_local": shipperAccount + plan.ServiceAmount,
				}).Error; err != nil {
				fmt.Println(err)
				return err
			}

			fmt.Printf("%s - 修改后货主余额:%0.2f\n", v, shipperAccount+plan.ServiceAmount)

			return nil
		})
		if err != nil {
			fmt.Printf("%s - 转换失败\n", v)
			fmt.Println(err)
			continue
		}

		fmt.Printf("%s - 转换成功\n", v)
	}

	fmt.Printf("需退回服务费总额:%0.2f\n", totalServiceAmount)
}

// InsertWithdrawApplication 补充提现记录
func InsertWithdrawApplication(transportationNumber, sysOrgCode string) {
	rows, err := model.DB.Table("tms_transport_note AS a").
		Joins("JOIN tms_driver AS b ON a.payee_id = b.driver_id").
		Joins("JOIN tms_transport_pay_plan AS c ON a.transportation_number = c.waybill_No").
		Joins("JOIN sys_zhaoshang_account AS d ON a.payee_id = d.user_id").
		Select([]string{"a.payee_id", "b.driver_name", "b.phone", "c.transport_Amount", "a.id", "d.dmanbr",
			"d.dmanam", "a.transportation_number"}).
		Where("a.transportation_number = ?", transportationNumber).
		Where("d.sys_org_code = ?", sysOrgCode).
		Rows()
	if err != nil {
		fmt.Println(err)
		return
	}

	for rows.Next() {
		var payeeID string
		var driverName string
		var phone string
		var transportAmount float64

		var transportationID string
		var dmanbr string
		var dmanam string
		var transportationNumber string
		if err := rows.Scan(&payeeID, &driverName, &phone, &transportAmount, &transportationID, &dmanbr, &dmanam,
			&transportationNumber); err != nil {
			fmt.Println(err)
			return
		}

		// 查询银行卡信息
		var bankCardID string
		var bankName string
		var accountOwnerName string
		var bankCardNumber string
		var bindingPhone string
		rr, err := model.DB.Table("tms_bank_card").
			Where("bank_card_user_id = ?", payeeID).
			Select([]string{"id", "bank_name", "account_owner_name", "bank_card_number", "binding_phone"}).
			Where("is_delete = ?", 0).
			Order("create_time DESC").
			Limit(1).
			Rows()
		if err != nil {
			fmt.Println(err)
			return
		}
		for rr.Next() {
			if err := rr.Scan(&bankCardID, &bankName, &accountOwnerName, &bankCardNumber, &bindingPhone); err != nil {
				fmt.Println(err)
				return
			}
		}

		if err := model.DB.Table("tms_withdraw_application").Create(map[string]any{
			"serial_number":         fmt.Sprintf("T%d", time.Now().UnixNano()),
			"applicant_id":          payeeID,
			"applicant_name":        driverName,
			"applicant_phone":       phone,
			"apply_time":            time.Now().Format("2006-01-02 15:04:05"),
			"apply_amount":          transportAmount,
			"application_status":    0,
			"bank_card_id":          bankCardID,
			"bank_name":             bankName,
			"account_owner_name":    accountOwnerName,
			"bank_card_number":      bankCardNumber,
			"binding_phone":         bindingPhone,
			"remark":                "补充提现记录",
			"transfer_type":         "0",
			"withdraw_people_type":  "0",
			"create_time":           time.Now().Format("2006-01-02 15:04:05"),
			"is_delete":             "0",
			"sys_org_code":          sysOrgCode,
			"payment_method":        "6",
			"is_push":               "1",
			"tms_transport_id":      transportationID,
			"type":                  "1",
			"dmanam":                dmanam,
			"dmanbr":                dmanbr,
			"fee_type":              "2",
			"transportation_number": transportationNumber,
			"oil_amount":            "0.0",
			"sxf_Amount":            "0",
			"sxf_status":            "0",
			"is_Approve":            "Y",
		}).Error; err != nil {
			fmt.Println(err)
			return
		}
	}
}

// ServiceFeeAmount 计算服务费
func ServiceFeeAmount(paymentAmount, serviceRate decimal.Decimal) decimal.Decimal {
	num1 := decimal.NewFromInt(1)
	rate1 := serviceRate.Mul(decimal.NewFromFloat(0.01))
	// (1-企业运单服务费率) 用于计算的参数
	rate2 := num1.Sub(rate1)

	// 0.05/(1-0.05)
	// The DivRound method handles division with specified precision and rounding mode.
	// RoundingMode.DOWN in Java is equivalent to decimal.RoundDown.
	result := paymentAmount.Mul(rate1.DivRound(rate2, 20))

	// setScale(2, RoundingMode.HALF_UP) in Java
	// The Round(2) method defaults to RoundHalfUp, setting the scale to 2 decimal places.
	finalResult := result.Round(2)

	return finalResult
}

// CloseBankSubAccount 关闭银行子账户
func CloseBankSubAccount(sysOrgCode string) {
	companyID := ""
	var c *cmb.CmbBank
	if sysOrgCode == "A03" {
		c = cmb.GetHszy()
		companyID = "a82dc78ba61880beda018fa5eab6c944"
	} else if sysOrgCode == "A07" {
		c = cmb.GetYbs()
		companyID = "a82dc78ba61880beda018fa5eab6c955"
	} else {
		return
	}

	selectSql := fmt.Sprintf(`SELECT
	sza.id,
	sza.dmanbr,
	sza.dmanam,
	sza.update_time
FROM
	sys_zhaoshang_account AS sza
WHERE
	sza.sys_org_code = '%s'
	AND sza.status = 1
	AND sza.is_delete = 0
	AND sza.user_type = 1
	AND sza.company_id = '%s'
	AND sza.user_id NOT IN (
	SELECT
		ttn.payee_id
	FROM
		tms_transport_note AS ttn
	WHERE
		ttn.sys_org_code = '%s'
		AND ttn.waybill_status IN (0, 1, 2, 3, 9))
ORDER BY
	sza.update_time ASC
LIMIT 5000`, sysOrgCode, companyID, sysOrgCode)

	rows, err := model.DB.Raw(selectSql).Rows()
	if err != nil {
		fmt.Println(err)
		return
	}

	totalNum := 0
	closeNum := 0
	for rows.Next() {
		var id string
		var dmanbr string
		var dmanam string
		var updateTime sql.NullTime
		if err := rows.Scan(&id, &dmanbr, &dmanam, &updateTime); err != nil {
			fmt.Println(err)
			continue
		}

		totalNum++
		_, err := c.SubAccountClose(dmanbr)
		if err != nil {
			fmt.Println(err)
			continue
		}
		fmt.Printf("%s 子单元 %s 关闭成功\n", dmanam, dmanbr)
		closeNum++

		if err := model.DB.Table("sys_zhaoshang_account").Where("id = ?", id).Updates(map[string]any{
			"status":      0,
			"update_time": time.Now().Format("2006-01-02 15:04:05"),
		}).Error; err != nil {
			fmt.Println(err)
		}
	}

	fmt.Println("totalNum:", totalNum)
	fmt.Println("closeNum:", closeNum)
}
