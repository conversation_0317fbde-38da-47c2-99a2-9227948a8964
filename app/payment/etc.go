package payment

import (
	"encoding/json"
	"fmt"
	"math"
	"strconv"
	"strings"
	"sync"
	"time"
	"wlhy/model"
	"wlhy/thirdparty/jinron"
	"wlhy/toolbox/logger"
)

func Etc() {
	e := jinron.NewETC()

	// 历史单处理
	etcHistoryWaybillStart(e, 0)
	etcHistoryWaybillChunk(e)
}

const (
	EtcPending    = 0 // 待处理
	EtcSuccess    = 1 // 处理成功
	EtcStarted    = 2 // 已开始
	EtcFinished   = 3 // 已结束
	EtcFailed     = 4 // 处理失败
	EtcNoInvoice  = 5 // 未开票
	EtcToHistory  = 6 // 转历史单
	EtcToBeChunk  = 7 // 待拆分
	EtcProcessing = 8 // 处理中
)

// EtcStateOptions 等待更新的字段
type EtcStateOptions struct {
	EtcResult         string
	EtcCode           int
	IsException       bool
	EtcFee            *float64
	EtcInvoiceUrl     *string
	EtcInvoiceExplain *string
	EtcIsProcess      *int
}

// changeHistoryEtcState 修改历史单etc状态
func changeHistoryEtcState(isChunk bool, transportationNumber string, state int, options EtcStateOptions) {
	updateData := map[string]any{
		"is_etc_invoice":   state,
		"etc_waybill_type": 1, // 历史单
		"etc_channel":      2, // 金润
	}

	if options.EtcIsProcess != nil {
		updateData["etc_is_process"] = *options.EtcIsProcess
	}
	if options.EtcFee != nil {
		updateData["etc_fee"] = *options.EtcFee
	}
	if options.EtcInvoiceUrl != nil {
		updateData["etc_invoice_url"] = *options.EtcInvoiceUrl
	}
	if options.EtcInvoiceExplain != nil {
		updateData["etc_invoice_explain"] = *options.EtcInvoiceExplain
	}

	updateData["etc_use_status"] = 0 // 正常
	if options.IsException {
		updateData["etc_use_status"] = 1 // 异常
	}

	if options.EtcResult != "" {
		updateData["etc_result"] = options.EtcResult
	}
	if options.EtcCode != 0 {
		updateData["etc_code"] = options.EtcCode
	}

	// 请求etc渠道商时间
	if state == EtcStarted || state == EtcFinished || options.IsException {
		updateData["etc_apply_time"] = time.Now().Format("2006-01-02 15:04:05")
	}

	// etc流程结束时间
	if state == EtcNoInvoice || state == EtcSuccess {
		updateData["etc_finished_time"] = time.Now().Format("2006-01-02 15:04:05")
	}

	if isChunk {
		if err := model.DB.Table("tms_etc_chunks").
			Where("transportation_number_chunk = ?", transportationNumber).
			Updates(updateData).Error; err != nil {
			logger.Stdout.Error(err.Error())
		}
	} else {
		if err := model.DB.Table("tms_transport_note").
			Where("transportation_number = ?", transportationNumber).
			Updates(updateData).Error; err != nil {
			logger.Stdout.Error(err.Error())
		}
	}
}

// EtcWaybillStartParam 运单参数
type EtcWaybillParam struct {
	TransportationNumber   string    `gorm:"column:transportation_number"`    // 运单号
	TransportationPlate    string    `gorm:"column:transportation_plate"`     // 车牌号
	VehicleLicenseColor    string    `gorm:"column:vehicle_license_color"`    // 车牌颜色
	LoadingName            string    `gorm:"column:loading_name"`             // 装货地址省市区
	LoadingAddress         string    `gorm:"column:loading_address"`          // 装货地址
	UnloadName             string    `gorm:"column:unload_name"`              // 卸货地址省市区
	UnloadAddress          string    `gorm:"column:unload_address"`           // 卸货地址
	LoadingTime            time.Time `gorm:"column:loading_time"`             // 装货时间
	UnloadTime             time.Time `gorm:"column:unload_time"`              // 卸货时间
	FreightPaid            float64   `gorm:"column:freight_paid"`             // 已付付款金额
	ServiceFeePaid         float64   `gorm:"column:service_fee_paid"`         // 已付服务费
	TransportationDriver   string    `gorm:"column:transportation_driver"`    // 运输司机名称
	ElectronicAgreementUrl string    `gorm:"column:electronic_agreement_url"` // 电子协议地址
	ActualHaulDistance     float64   `gorm:"column:actual_haul_distance"`     // 实际运输距离
	FreightAmount          float64   `gorm:"column:freight_amount"`           // 应付运费
	ServiceCharge          float64   `gorm:"column:service_charge"`           // 应付服务费
}

// EtcChunk 运单拆分
type EtcChunk struct {
	ID                        int       `gorm:"column:id"`                          // 主键id
	TransportationNumber      string    `gorm:"column:transportation_number"`       // 运单号
	TransportationNumberChunk string    `gorm:"column:transportation_number_chunk"` // 虚拟运单号
	EtcWaybillType            int       `gorm:"column:etc_waybill_type"`            // etc开票运单类型(0-实时运单,1-历史运 2-未开始)
	EtcUseStatus              int       `gorm:"column:etc_use_status"`              // ETC可开票状态，(0-正常,1-异常)
	IsEtcInvoice              int       `gorm:"column:is_etc_invoice"`              // 运单ETC开票状态:0-未开票，1-已开票，2-已调用运单开始接口，3-已调用运单结束接口，4-开票失败，5-开票流程完成但无票
	EtcFee                    float64   `gorm:"column:etc_fee"`                     // 通行费/etc费用
	EtcInvoiceExplain         string    `gorm:"column:etc_invoice_explain"`         // ETC开票说明
	EtcCode                   int       `gorm:"column:etc_code"`                    // 开始上报ETC返回的code
	EtcResult                 string    `gorm:"column:etc_result"`                  // etc返回结果
	EtcInvoiceUrl             string    `gorm:"column:etc_invoice_url"`             // etc发票下载链接，可能会有多张发票
	EtcIsProcess              int       `gorm:"column:etc_is_process"`              // etc数据处理状态，0:未处理，1:已处理
	CreateTime                time.Time `gorm:"column:create_time"`                 // 创建时间
	EtcRequest                string    `gorm:"column:etc_request"`                 // 上报ETC请求数据
}

func etcHistoryWaybillStart(e *jinron.ETC, minDistance float64) {
	logger.Stdout.Info("etc history waybillStart start")

	// 获取全部拆分运单构建map
	var chunkNotes []EtcChunk
	if err := model.DB.Table("tms_etc_chunks").
		Select("transportation_number_chunk").
		Find(&chunkNotes).Error; err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	chunkNotesMap := make(map[string]bool)
	for _, note := range chunkNotes {
		chunkNotesMap[note.TransportationNumberChunk] = true
	}

	var notes []EtcWaybillParam

	if err := model.DB.Table("tms_transport_note AS a").
		Joins("JOIN tms_order_history AS b ON a.order_id = b.id AND a.order_version_no = b.version_no").
		Joins("JOIN tms_vehicle AS c ON a.transportation_plate = c.vehicle_license_number").
		Joins("JOIN tms_electronic_agreement AS d ON a.transportation_number = d.waybill_number AND d.electronic_agreement_type = 1").
		Select([]string{"a.transportation_number", "a.transportation_plate", "c.vehicle_license_color",
			"b.loading_name", "b.loading_address", "b.unload_name", "b.unload_address", "a.loading_time",
			"a.unload_time", "a.freight_paid", "a.service_fee_paid", "a.transportation_driver",
			"d.electronic_agreement_url", "a.actual_haul_distance", "a.freight_amount", "a.service_charge"}).
		Where("a.waybill_status NOT IN (?)", []int{6, 7}).
		Where("a.is_etc_invoice IN (?)", []int{0, 6}).
		Where("a.etc_use_status = ?", 0).
		Where("a.sys_org_code = ?", "A03").
		Where("a.loading_time >= ?", "2024-04-01").
		Where("a.loading_time <= ?", time.Now().Add(-time.Duration(24*10)*time.Hour).Format("2006-01-02 15:04:05")).
		Where("a.freight_amount >= ?", 1000).
		Limit(2000).
		Order("a.loading_time DESC").
		Find(&notes).Error; err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	vehicleOwnerMap := make(map[string]string)
	vehicleRows, err := model.DB.Table("tms_vehicle").
		Select([]string{"vehicle_license_number", "vehicle_owner"}).
		Where("is_delete = ?", 0).
		Rows()
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	for vehicleRows.Next() {
		var vehicleLicenseNumber string
		var vehicleOwner string
		if err := vehicleRows.Scan(&vehicleLicenseNumber, &vehicleOwner); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}
		vehicleOwnerMap[vehicleLicenseNumber] = vehicleOwner
	}

	var wg sync.WaitGroup
	limit := make(chan bool, 20)

	for _, note := range notes {
		limit <- true
		wg.Add(1)
		go func(note EtcWaybillParam) {
			defer func() {
				if r := recover(); r != nil {
					logger.Stdout.Error(fmt.Sprintf("%v", r))
				}
				<-limit
				wg.Done()
			}()

			// 装卸货地址
			loadingAddress := note.LoadingName + strings.ReplaceAll(note.LoadingAddress, note.LoadingName, "")
			unloadAddress := note.UnloadName + strings.ReplaceAll(note.UnloadAddress, note.UnloadName, "")

			// 运费
			fee := 0
			if note.FreightPaid > 0 {
				fee = int(note.FreightPaid+note.ServiceFeePaid) * 100
			} else {
				fee = int(note.FreightAmount+note.ServiceCharge) * 100
			}

			// 根据车辆所有人公司过滤
			if vehicleOwnerMap[note.TransportationPlate] == "敖汉旗中运运输有限公司" {
				return
			}

			// 运输时间未超过240小时，不可调用历史单接口
			if time.Since(note.LoadingTime).Hours() <= 240 {
				return
			}

			// 实际运输距离小于最小可开票运输距离
			if note.ActualHaulDistance <= minDistance {
				changeHistoryEtcState(false, note.TransportationNumber, EtcFailed, EtcStateOptions{
					IsException: true,
					EtcResult:   "实际运输距离小于最小可开票运输距离",
					EtcCode:     0,
				})
				logger.Stdout.Warn(fmt.Sprintf("%s 实际运输距离小于最小可开票运输距离\n", note.TransportationNumber))
				return
			}

			// 判断装卸货时间是否超出96小时，若超出96小时则拆分
			if note.UnloadTime.Sub(note.LoadingTime).Hours() >= 96 {
				changeHistoryEtcState(false, note.TransportationNumber, EtcFailed, EtcStateOptions{
					IsException: true,
					EtcResult:   "装卸货时间超出96小时，待拆分处理",
					EtcCode:     0,
				})
				logger.Stdout.Error(fmt.Sprintf("%s 装卸货时间超出96小时，待拆分处理\n", note.TransportationNumber))

				// 拆分数据并入库
				var chunks []EtcChunk
				endHours := math.Ceil(note.UnloadTime.Sub(note.LoadingTime).Hours() / 72)
				for i := range int(endHours) {
					// 拆分运单已存在
					if _, ok := chunkNotesMap[note.TransportationNumber+strconv.Itoa(i)]; ok {
						continue
					}

					startTime := note.LoadingTime.Add(time.Duration(i) * 72 * time.Hour)
					predictEndTime := note.LoadingTime.Add(time.Duration(i+1) * 72 * time.Hour)

					historyReq := jinron.WaybillStartReq{
						Num:               note.TransportationNumber + strconv.Itoa(i),
						PlateNum:          note.TransportationPlate,
						PlateColor:        note.VehicleLicenseColor,
						StartTime:         startTime,
						SourceAddr:        loadingAddress,
						DestAddr:          unloadAddress,
						PredictEndTime:    predictEndTime,
						Fee:               fee,
						DriverContractUrl: note.ElectronicAgreementUrl,
					}
					historyReqJson, _ := json.Marshal(historyReq)

					chunks = append(chunks, EtcChunk{
						TransportationNumber:      note.TransportationNumber,
						TransportationNumberChunk: note.TransportationNumber + strconv.Itoa(i),
						EtcWaybillType:            1, // 1-历史运单
						CreateTime:                time.Now(),
						EtcRequest:                string(historyReqJson),
					})
				}

				if len(chunks) > 0 {
					if err := model.DB.Table("tms_etc_chunks").Create(&chunks).Error; err != nil {
						logger.Stdout.Error(err.Error())
					}
				}

				return
			}

			// 调用历史单开始接口----------------
			startReq := jinron.WaybillStartReq{
				Num:               note.TransportationNumber,
				PlateNum:          note.TransportationPlate,
				PlateColor:        note.VehicleLicenseColor,
				SourceAddr:        loadingAddress,
				DestAddr:          unloadAddress,
				StartTime:         note.LoadingTime,
				PredictEndTime:    note.LoadingTime.Add(3 * 24 * time.Hour),
				Fee:               fee,
				DriverContractUrl: note.ElectronicAgreementUrl,
			}

			startResult, err := e.HistoryWaybillStart(&startReq)
			if err != nil {
				logger.Stdout.Error(fmt.Sprintf("%s %s %v", startReq.Num, "调用历史单运单开始接口失败", err))
				if strings.Contains(err.Error(), "operation timed out") {
					return
				}
				changeHistoryEtcState(false, startReq.Num, EtcFailed, EtcStateOptions{
					IsException: true,
					EtcResult:   err.Error(),
					EtcCode:     startResult.Code,
				})
				return
			}
			logger.Stdout.Info(fmt.Sprintf("%s %s %v", startReq.Num, "调用历史单运单开始接口成功", startResult))
			// ----------------

			// 调用历史单结束接口----------------
			endReq := jinron.WaybillEndReq{
				Num:          note.TransportationNumber,
				RealDestAddr: unloadAddress,
				EndTime:      note.UnloadTime,
			}

			endResult, err := e.HistoryWaybillEnd(&endReq)
			if err != nil {
				logger.Stdout.Error(fmt.Sprintf("%s %s %v", endReq.Num, "调用历史单运单结束接口失败", err))
				if strings.Contains(err.Error(), "operation timed out") {
					return
				}
				changeHistoryEtcState(false, endReq.Num, EtcFailed, EtcStateOptions{
					IsException: true,
					EtcResult:   err.Error(),
					EtcCode:     endResult.Code,
				})
				return
			}
			logger.Stdout.Info(fmt.Sprintf("%s %s %v", endReq.Num, "调用历史单运单结束接口成功", endResult))
			// ----------------

			changeHistoryEtcState(false, endReq.Num, EtcFinished, EtcStateOptions{
				IsException: false,
				EtcResult:   endResult.Message,
				EtcCode:     endResult.Code,
			})
		}(note)
	}

	wg.Wait()

	logger.Stdout.Info("etc history waybillStart end")
}

func etcHistoryWaybillChunk(e *jinron.ETC) {
	logger.Stdout.Info("etc history waybillChunk start")

	var wg sync.WaitGroup
	limit := make(chan bool, 20)

	// 1. 调用etc运单开始、结束接口
	var notes []EtcChunk
	if err := model.DB.Table("tms_etc_chunks").
		Where("etc_waybill_type = ?", 1).
		Where("etc_use_status = ?", 0).
		Where("is_etc_invoice = ?", 0).
		Find(&notes).Error; err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	for _, note := range notes {
		wg.Add(1)
		limit <- true
		go func(note EtcChunk) {
			defer func() {
				if r := recover(); r != nil {
					logger.Stdout.Error(fmt.Sprintf("%v", r))
				}
				<-limit
				wg.Done()
			}()

			var req jinron.WaybillStartReq
			if err := json.Unmarshal([]byte(note.EtcRequest), &req); err != nil {
				logger.Stdout.Error(err.Error())
				return
			}

			// 运单开始时间未超过240小时，不可调用历史运单接口，跳过
			if time.Since(req.StartTime).Hours() <= 240 {
				return
			}

			// 调用历史单开始运输接口
			startResult, err := e.HistoryWaybillStart(&req)
			if err != nil {
				changeHistoryEtcState(true, note.TransportationNumberChunk, EtcFailed, EtcStateOptions{
					IsException: true,
					EtcResult:   startResult.Message,
					EtcCode:     startResult.Code,
				})
				return
			}

			// 调用历史单结束接口
			endResult, err := e.HistoryWaybillEnd(&jinron.WaybillEndReq{
				Num:          req.Num,
				RealDestAddr: req.DestAddr,
				EndTime:      req.PredictEndTime,
			})
			if err != nil {
				changeHistoryEtcState(true, note.TransportationNumberChunk, EtcFailed, EtcStateOptions{
					IsException: true,
					EtcResult:   endResult.Message,
					EtcCode:     endResult.Code,
				})
				return
			}

			changeHistoryEtcState(true, note.TransportationNumberChunk, EtcFinished, EtcStateOptions{
				IsException: false,
				EtcResult:   endResult.Message,
				EtcCode:     endResult.Code,
			})
		}(note)

	}
	wg.Wait()

	// 2. 查询etc发票
	if err := model.DB.Table("tms_etc_chunks").
		Where("etc_waybill_type = ?", 1).
		Where("etc_use_status = ?", 0).
		Where("is_etc_invoice IN (?)", []int{3, 8}).
		Find(&notes).Error; err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	for _, note := range notes {
		wg.Add(1)
		limit <- true
		go func(note EtcChunk) {
			defer func() {
				if r := recover(); r != nil {
					logger.Stdout.Error(fmt.Sprintf("%v", r))
				}
				<-limit
				wg.Done()
			}()

			result, err := e.QueryInvoice(note.TransportationNumberChunk)
			if err != nil {
				if strings.Contains(err.Error(), "operation timed out") {
					return
				}

				if strings.Contains(err.Error(), "未查询到运单信息") {
					changeHistoryEtcState(true, note.TransportationNumberChunk, EtcPending, EtcStateOptions{
						IsException: false,
						EtcResult:   "未查询到运单信息",
						EtcCode:     0,
					})
					return
				}

				// 运单状态未结束不能发起查询
				if strings.Contains(err.Error(), "运单状态未结束不能发起查询") {
					var req jinron.WaybillStartReq
					if err := json.Unmarshal([]byte(note.EtcRequest), &req); err != nil {
						logger.Stdout.Error(err.Error())
						return
					}
					endResult, err := e.HistoryWaybillEnd(&jinron.WaybillEndReq{
						Num:          req.Num,
						RealDestAddr: req.DestAddr,
						EndTime:      req.PredictEndTime,
					})
					if err != nil {
						changeHistoryEtcState(true, note.TransportationNumberChunk, EtcFailed, EtcStateOptions{
							IsException: true,
							EtcResult:   endResult.Message,
							EtcCode:     endResult.Code,
						})
						return
					}
					return
				}

				return
			}

			// 待开票和开票中
			if result.Data.WaybillStatus == 1 || result.Data.WaybillStatus == 2 {
				resultData, _ := json.Marshal(result)
				if len(result.Data.Result) > 0 {
					etcFee := 0
					etcInvoiceUrls := []string{}
					for _, v := range result.Data.Result {
						etcFee += v.Fee
						etcInvoiceUrls = append(etcInvoiceUrls, v.InvoiceUrl)
					}
					etcInvoiceUrl, _ := json.Marshal(etcInvoiceUrls)

					opEtcFee := float64(etcFee / 100)
					opEtcInvoiceUrl := string(etcInvoiceUrl)
					changeHistoryEtcState(true, note.TransportationNumberChunk, EtcProcessing, EtcStateOptions{
						IsException:   false,
						EtcResult:     string(resultData),
						EtcCode:       result.Code,
						EtcFee:        &opEtcFee,
						EtcInvoiceUrl: &opEtcInvoiceUrl,
					})
				} else {
					changeHistoryEtcState(true, note.TransportationNumberChunk, EtcProcessing, EtcStateOptions{
						IsException: false,
						EtcResult:   string(resultData),
						EtcCode:     result.Code,
					})
				}
				return
			}

			resultData, _ := json.Marshal(result)

			// 开票完成
			if result.Data.WaybillStatus == 3 {
				// 开票已完成但未开出票
				if len(result.Data.Result) == 0 {
					changeHistoryEtcState(true, note.TransportationNumberChunk, EtcNoInvoice, EtcStateOptions{
						IsException: false,
						EtcResult:   string(resultData),
						EtcCode:     result.Code,
					})
					return
				}

				// 开票成功
				etcFee := 0
				etcInvoiceUrls := []string{}
				for _, v := range result.Data.Result {
					etcFee += v.Fee
					etcInvoiceUrls = append(etcInvoiceUrls, v.InvoiceUrl)
				}
				etcInvoiceUrl, _ := json.Marshal(etcInvoiceUrls)

				opEtcFee := float64(etcFee / 100)
				opEtcInvoiceUrl := string(etcInvoiceUrl)
				changeHistoryEtcState(true, note.TransportationNumberChunk, EtcSuccess, EtcStateOptions{
					IsException:   false,
					EtcResult:     string(resultData),
					EtcCode:       result.Code,
					EtcFee:        &opEtcFee,
					EtcInvoiceUrl: &opEtcInvoiceUrl,
				})
				return
			}

			// 开票失败
			if result.Data.WaybillStatus == 4 {
				changeHistoryEtcState(true, note.TransportationNumberChunk, EtcFailed, EtcStateOptions{
					IsException: false,
					EtcResult:   string(resultData),
					EtcCode:     result.Code,
				})
				return
			}
		}(note)
	}

	wg.Wait()

	// 3. 处理etc发票数据并更新原始运单
	if err := model.DB.Table("tms_etc_chunks").
		Where("etc_waybill_type = ?", 1).
		Where("etc_use_status = ?", 0).
		Where("etc_is_process = ?", 0).
		Find(&notes).Error; err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	batch := make(map[string][]EtcChunk)
	for _, note := range notes {
		batch[note.TransportationNumber] = append(batch[note.TransportationNumber], note)
	}
	for transportationNumber, notes := range batch {
		var originResult jinron.QueryInvoiceResp

		var originalNoteResult string
		model.DB.Table("tms_transport_note").
			Where("transportation_number = ?", transportationNumber).
			Select([]string{"etc_result"}).
			Scan(&originalNoteResult)
		if originalNoteResult != "" && json.Valid([]byte(originalNoteResult)) {
			if err := json.Unmarshal([]byte(originalNoteResult), &originResult); err != nil {
				logger.Stdout.Error(err.Error())
				return
			}
		}

		etcFee := 0
		etcInvoiceUrls := []string{}
		chunkIDs := []int{}
		for k, note := range notes {
			if note.IsEtcInvoice == 1 {
				var result jinron.QueryInvoiceResp
				if err := json.Unmarshal([]byte(note.EtcResult), &result); err != nil {
					logger.Stdout.Error(err.Error())
					continue
				}

				if k == 0 {
					if originResult.Data.WaybillNum == "" {
						originResult = result
						originResult.Data.WaybillNum = transportationNumber
					} else {
						originResult.Data.Result = append(originResult.Data.Result, result.Data.Result...)
					}
				} else {
					originResult.Data.Result = append(originResult.Data.Result, result.Data.Result...)
				}
				chunkIDs = append(chunkIDs, note.ID)
			}
		}

		originResultJson, _ := json.Marshal(originResult)
		etcInvoiceUrl, _ := json.Marshal(etcInvoiceUrls)

		// 去重
		uniM := make(map[string]bool)
		var uni []jinron.QueryInvoiceResult
		for _, v := range originResult.Data.Result {
			if _, ok := uniM[v.InvoiceNum]; ok {
				continue
			}
			uniM[v.InvoiceNum] = true
			uni = append(uni, v)
		}

		originResult.Data.Result = uni
		for _, v := range originResult.Data.Result {
			etcFee += v.Fee
			etcInvoiceUrls = append(etcInvoiceUrls, v.InvoiceUrl)
		}
		opEtcFee := float64(etcFee / 100)
		opEtcInvoiceUrl := string(etcInvoiceUrl)
		changeHistoryEtcState(false, transportationNumber, EtcSuccess, EtcStateOptions{
			IsException:   false,
			EtcResult:     string(originResultJson),
			EtcCode:       originResult.Code,
			EtcFee:        &opEtcFee,
			EtcInvoiceUrl: &opEtcInvoiceUrl,
		})

		if err := model.DB.Table("tms_etc_chunks").
			Where("id IN (?)", chunkIDs).
			Updates(map[string]any{
				"etc_is_process": 1,
			}).Error; err != nil {
			logger.Stdout.Error(err.Error())
		}
	}

	logger.Stdout.Info("etc history waybillChunk end")
}
