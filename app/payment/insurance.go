package payment

import (
	"database/sql"
	"fmt"
	"strconv"
	"strings"
	"wlhy/model"
	"wlhy/toolbox"

	"github.com/xuri/excelize/v2"
)

// InsuranceAmount 保险金额统计
func InsuranceAmount(startDate, endDate string) {
	if startDate == "" || endDate == "" {
		panic("start date or end date is empty")
	}

	f := excelize.NewFile()

	// 太平保险
	rows, _ := model.DB.Table("tms_transport_note_insurance_record AS a").
		Joins("JOIN tms_transport_note AS b ON a.transport_note_number = b.transportation_number").
		Joins("JOIN tms_shipper AS c ON b.create_company_id = c.company_id").
		Select([]string{"b.transportation_number", "b.transportation_plate", "a.insurance_time",
			"a.insurance_rate", "b.freight_amount", "c.company_name", "a.insurance_type", "a.insurance_amount"}).
		Where("a.insurance_company = ?", 1).
		Where("a.insurance_status = ?", 1).
		Where("a.is_cancel_insurance = ?", 0).
		Where("b.waybill_status NOT IN (?)", []int{6, 7}).
		Where("a.insurance_time BETWEEN ? AND ?", startDate, endDate).
		Rows()

	f.NewSheet("太平")
	start := 2
	f.SetCellValue("太平", "A1", "运单号")
	f.SetCellValue("太平", "B1", "车牌号")
	f.SetCellValue("太平", "C1", "投保时间")
	f.SetCellValue("太平", "D1", "保费")
	f.SetCellValue("太平", "E1", "平台保费")
	f.SetCellValue("太平", "F1", "公司名称")
	f.SetCellValue("太平", "G1", "费率")
	f.SetCellValue("太平", "H1", "费率类型")
	for rows.Next() {
		var transportationNumber string
		var transportationPlate string
		var insuranceTime string
		var insuranceRate sql.NullFloat64
		var freightAmount float64
		var companyName string
		var insuranceType int
		var insuranceAmount sql.NullFloat64
		if err := rows.Scan(&transportationNumber, &transportationPlate, &insuranceTime, &insuranceRate, &freightAmount, &companyName, &insuranceType, &insuranceAmount); err != nil {
			panic(err)
		}

		sumPremium := (freightAmount * 100) * (0.0018 * 10000) / (100 * 10000)
		plat := freightAmount * insuranceRate.Float64 / 100

		f.SetCellValue("太平", fmt.Sprintf("A%d", start), transportationNumber)
		f.SetCellValue("太平", fmt.Sprintf("B%d", start), transportationPlate)
		f.SetCellValue("太平", fmt.Sprintf("C%d", start), insuranceTime)
		f.SetCellValue("太平", fmt.Sprintf("D%d", start), sumPremium)
		f.SetCellValue("太平", fmt.Sprintf("E%d", start), plat)
		f.SetCellValue("太平", fmt.Sprintf("F%d", start), companyName)
		f.SetCellValue("太平", fmt.Sprintf("G%d", start), insuranceRate.Float64)

		insuranceTypeName := ""
		switch insuranceType {
		case 0:
			insuranceTypeName = "比例扣费，千分比"
			f.SetCellValue("太平", fmt.Sprintf("G%d", start), insuranceRate.Float64)
		case 1:
			insuranceTypeName = "定额扣费，元"
			f.SetCellValue("太平", fmt.Sprintf("G%d", start), insuranceAmount.Float64)
		case 2:
			insuranceTypeName = "比例扣费，万分比"
			f.SetCellValue("太平", fmt.Sprintf("G%d", start), insuranceRate.Float64)
		}
		f.SetCellValue("太平", fmt.Sprintf("H%d", start), insuranceTypeName)

		start++
	}

	// 东海保险
	rows, _ = model.DB.Table("tms_transport_note_insurance_record AS a").
		Joins("JOIN tms_transport_note AS b ON a.transport_note_number = b.transportation_number").
		Joins("JOIN tms_order_history AS c ON b.order_id = c.id AND b.order_version_no = c.version_no").
		Joins("JOIN tms_shipper AS d ON b.create_company_id = d.company_id").
		Select([]string{"b.transportation_number", "b.transportation_plate", "a.insurance_time",
			"a.insurance_rate", "b.freight_amount", "a.thirdparty_insurance_amount", "a.insurance_amount", "c.goods_type", "d.company_name", "c.goods_name", "a.insurance_type"}).
		Where("a.insurance_company = ?", 2).
		Where("a.insurance_status = ?", 1).
		Where("a.is_cancel_insurance = ?", 0).
		Where("b.waybill_status NOT IN (?)", []int{6, 7}).
		Where("d.shipper_type = ? AND d.is_delete = ?", 1, 0).
		Where("a.insurance_time BETWEEN ? AND ?", startDate, endDate).
		Rows()

	f.NewSheet("东海")
	start = 2
	f.SetCellValue("东海", "A1", "运单号")
	f.SetCellValue("东海", "B1", "车牌号")
	f.SetCellValue("东海", "C1", "投保时间")
	f.SetCellValue("东海", "D1", "保费")
	f.SetCellValue("东海", "E1", "平台保费")
	f.SetCellValue("东海", "F1", "公司名称")
	f.SetCellValue("东海", "G1", "费率")
	f.SetCellValue("东海", "H1", "费率类型")
	for rows.Next() {
		var transportationNumber string
		var transportationPlate string
		var insuranceTime string
		var insuranceRate sql.NullFloat64
		var freightAmount float64
		var thirdpartyInsuranceAmount sql.NullFloat64
		var goodsType string
		var insuranceAmount sql.NullFloat64
		var companyName string
		var goodsName string
		var insuranceType int
		if err := rows.Scan(&transportationNumber, &transportationPlate, &insuranceTime, &insuranceRate, &freightAmount, &thirdpartyInsuranceAmount, &insuranceAmount, &goodsType, &companyName, &goodsName, &insuranceType); err != nil {
			panic(err)
		}

		plat := freightAmount * insuranceRate.Float64 / 100
		if insuranceAmount.Valid && insuranceAmount.Float64 > 0 {
			plat = insuranceAmount.Float64
		}

		insuranceGoodsType := "其他"
		if goodsType == "煤炭及制品" {
			insuranceGoodsType = "煤炭"
		}
		if goodsType == "钢铁" {
			insuranceGoodsType = "钢材"
		}

		// 保费
		premium := ""
		if insuranceGoodsType == "煤炭" {
			premium = "1.00"
		} else {
			tmpPremium := toolbox.RoundToDecimal(freightAmount*0.0016, 2)
			if tmpPremium < 3 {
				premium = "3.00"
			} else {
				premium = fmt.Sprintf("%0.2f", tmpPremium)
			}
		}

		// 特殊规则
		// 内蒙古天壹成信环保科技有限公司 石英砂
		if companyName == "内蒙古天壹成信环保科技有限公司" && goodsName == "石英砂" {
			premium = "3.00"
		}

		if thirdpartyInsuranceAmount.Valid && thirdpartyInsuranceAmount.Float64 > 0 {
			premium = strconv.FormatFloat(thirdpartyInsuranceAmount.Float64, 'f', 2, 64)
		}

		f.SetCellValue("东海", fmt.Sprintf("A%d", start), transportationNumber)
		f.SetCellValue("东海", fmt.Sprintf("B%d", start), transportationPlate)
		f.SetCellValue("东海", fmt.Sprintf("C%d", start), insuranceTime)
		f.SetCellValue("东海", fmt.Sprintf("D%d", start), premium)
		f.SetCellValue("东海", fmt.Sprintf("E%d", start), toolbox.RoundToDecimal(plat, 2))
		f.SetCellValue("东海", fmt.Sprintf("F%d", start), companyName)

		insuranceTypeName := ""
		switch insuranceType {
		case 0:
			insuranceTypeName = "比例扣费，千分比"
			f.SetCellValue("东海", fmt.Sprintf("G%d", start), insuranceRate.Float64)
		case 1:
			insuranceTypeName = "定额扣费，元"
			f.SetCellValue("东海", fmt.Sprintf("G%d", start), insuranceAmount.Float64)
		case 2:
			insuranceTypeName = "比例扣费，万分比"
			f.SetCellValue("东海", fmt.Sprintf("G%d", start), insuranceRate.Float64)
		}
		f.SetCellValue("东海", fmt.Sprintf("H%d", start), insuranceTypeName)

		start++
	}

	f.DeleteSheet("Sheet1")
	f.SetActiveSheet(0)

	if err := f.SaveAs(fmt.Sprintf("保险金额统计 %s-%s.xlsx",
		strings.ReplaceAll(startDate, "-", ""),
		strings.ReplaceAll(endDate, "-", ""))); err != nil {
		panic(err)
	}
}
