package main

import (
	"database/sql"
	"fmt"
	"math"
	"math/rand"
	"os"
	"regexp"
	"slices"
	"strconv"
	"strings"
	"time"
	"wlhy/model"
	"wlhy/toolbox"
	"wlhy/toolbox/logger"

	"github.com/google/uuid"
	"github.com/samber/lo"
	"github.com/xuri/excelize/v2"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

// OverloadStatistics 超载统计
func OverloadStatistics() {
	rows, err := model.DB.Table("tms_transport_note AS a").
		Joins("JOIN tms_shipper AS b ON a.create_company_id = b.company_id").
		Joins("JOIN tms_vehicle AS c on a.transportation_plate = c.vehicle_license_number").
		Joins("LEFT JOIN tms_vehicle as d ON c.trailer_info_id = d.id").
		Select([]string{"b.company_name", "a.loading_number", "c.nuclear_load_weight", "d.nuclear_load_weight AS trailer_nuclear_load_weight", "a.freight_paid", "a.service_fee_paid"}).
		Where("a.waybill_status IN (?)", []int{4, 5, 8}).
		Where("a.finished_time BETWEEN ? AND ?", "2025-04-01 00:00:00", "2025-05-01 00:00:00").
		Group("a.id").
		Rows()
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	type Item struct {
		LoadingNumber         float64
		StandardWeight        float64
		OverloadWeight        float64
		SereveOverloadWeight  float64
		TotalNotes            int
		OverLoadNotes         int
		SereveOverloadNotes   int
		TotalFreight          float64
		OverLoadFreight       float64
		SereveOverLoadFreight float64
	}

	data := make(map[string]Item)

	for rows.Next() {
		var companyName string
		var loadingNumber float64
		var nuclearLoadWeight float64
		var trailerNuclearLoadWeight sql.NullFloat64
		var freightPaid float64
		var serviceFeePaid float64
		if err := rows.Scan(&companyName, &loadingNumber, &nuclearLoadWeight, &trailerNuclearLoadWeight, &freightPaid, &serviceFeePaid); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}

		// 核定载重量
		currentNuclearLoadWeight := nuclearLoadWeight
		if trailerNuclearLoadWeight.Float64 > 0 {
			currentNuclearLoadWeight += trailerNuclearLoadWeight.Float64
		}

		// 统计
		if _, ok := data[companyName]; !ok {
			data[companyName] = Item{}
		}

		t := data[companyName]

		// 总重量
		t.LoadingNumber += loadingNumber

		// 核定总重量
		t.StandardWeight += currentNuclearLoadWeight

		// 超载重量
		t.OverloadWeight += (loadingNumber - currentNuclearLoadWeight)
		if loadingNumber > 100 {
			t.SereveOverloadWeight += (loadingNumber - currentNuclearLoadWeight)
		}

		// 运单数量
		t.TotalNotes += 1
		if loadingNumber > currentNuclearLoadWeight {
			t.OverLoadNotes += 1
		}
		if loadingNumber > 100 {
			t.SereveOverloadNotes += 1
		}

		// 运费统计
		t.TotalFreight += (freightPaid + serviceFeePaid)
		if loadingNumber > currentNuclearLoadWeight {
			t.OverLoadFreight += (freightPaid + serviceFeePaid)
		}
		if loadingNumber > 100 {
			t.SereveOverLoadFreight += (freightPaid + serviceFeePaid)
		}

		data[companyName] = t
	}

	f := excelize.NewFile()
	defer f.Close()

	f.SetCellValue("Sheet1", "A1", "公司名称")
	f.SetCellValue("Sheet1", "B1", "货物重量/吨")
	f.SetCellValue("Sheet1", "C1", "标载重量/吨")
	f.SetCellValue("Sheet1", "D1", "超载重量/吨")
	f.SetCellValue("Sheet1", "E1", "超载重量占比")
	f.SetCellValue("Sheet1", "F1", "严重超载重量占比(相对总重量)")
	f.SetCellValue("Sheet1", "G1", "严重超载重量占比(相对超载重量)")
	f.SetCellValue("Sheet1", "H1", "总运单数")
	f.SetCellValue("Sheet1", "I1", "超载运单数")
	f.SetCellValue("Sheet1", "J1", "超载运单占比")
	f.SetCellValue("Sheet1", "K1", "严重超载运单数")
	f.SetCellValue("Sheet1", "L1", "严重超载运单占比(相对总运单数)")
	f.SetCellValue("Sheet1", "M1", "严重超载运单占比(相对超载运单数)")
	f.SetCellValue("Sheet1", "N1", "总运费")
	f.SetCellValue("Sheet1", "O1", "超载业务运费")
	f.SetCellValue("Sheet1", "P1", "超载业务运费占比")
	f.SetCellValue("Sheet1", "Q1", "严重超载业务运费占比(相对总运费)")
	f.SetCellValue("Sheet1", "R1", "严重超载业务运费占比(相对超载业务运费)")

	start := 2
	for k, v := range data {
		f.SetCellValue("Sheet1", fmt.Sprintf("A%d", start), k)
		f.SetCellValue("Sheet1", fmt.Sprintf("B%d", start), fmt.Sprintf("%0.2f", v.LoadingNumber))
		f.SetCellValue("Sheet1", fmt.Sprintf("C%d", start), fmt.Sprintf("%0.2f", v.StandardWeight))

		dValue := 0.0
		if v.OverloadWeight > 0 {
			dValue = v.OverloadWeight
		}
		f.SetCellValue("Sheet1", fmt.Sprintf("D%d", start), fmt.Sprintf("%0.2f", dValue))

		f.SetCellValue("Sheet1", fmt.Sprintf("E%d", start), fmt.Sprintf("%0.2f%%", v.OverloadWeight/v.StandardWeight*100))
		f.SetCellValue("Sheet1", fmt.Sprintf("F%d", start), fmt.Sprintf("%0.2f%%", v.SereveOverloadWeight/v.LoadingNumber*100))
		f.SetCellValue("Sheet1", fmt.Sprintf("G%d", start), fmt.Sprintf("%0.2f%%", v.SereveOverloadWeight/v.OverloadWeight*100))

		f.SetCellValue("Sheet1", fmt.Sprintf("H%d", start), v.TotalNotes)
		f.SetCellValue("Sheet1", fmt.Sprintf("I%d", start), v.OverLoadNotes)

		f.SetCellValue("Sheet1", fmt.Sprintf("J%d", start), fmt.Sprintf("%0.2f%%", float64(v.OverLoadNotes/v.TotalNotes*100)))
		f.SetCellValue("Sheet1", fmt.Sprintf("K%d", start), v.SereveOverloadNotes)
		f.SetCellValue("Sheet1", fmt.Sprintf("L%d", start), fmt.Sprintf("%0.2f%%", float64(v.SereveOverloadNotes/v.TotalNotes*100)))

		mValue := 0.0
		if v.OverLoadNotes > 0 {
			mValue = float64(v.SereveOverloadNotes / v.OverLoadNotes)
		}
		f.SetCellValue("Sheet1", fmt.Sprintf("M%d", start), fmt.Sprintf("%0.2f", mValue))
		f.SetCellValue("Sheet1", fmt.Sprintf("N%d", start), fmt.Sprintf("%0.2f", v.TotalFreight))

		oValue := 0.0
		if v.OverLoadFreight > 0 {
			oValue = v.OverLoadFreight
		}
		f.SetCellValue("Sheet1", fmt.Sprintf("O%d", start), fmt.Sprintf("%0.2f", oValue))

		f.SetCellValue("Sheet1", fmt.Sprintf("P%d", start), fmt.Sprintf("%0.2f%%", v.OverLoadFreight/v.TotalFreight*100))
		f.SetCellValue("Sheet1", fmt.Sprintf("Q%d", start), fmt.Sprintf("%0.2f%%", v.SereveOverLoadFreight/v.TotalFreight*100))
		f.SetCellValue("Sheet1", fmt.Sprintf("R%d", start), fmt.Sprintf("%0.2f%%", v.SereveOverLoadFreight/v.OverLoadFreight*100))

		start++
	}

	if err := f.SaveAs("超载业务占比.xlsx"); err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	fmt.Println("超载业务占比统计完成")
}

// UpdateCrawlerModel 更新爬虫数据
func UpdateCrawlerModel() {
	f, err := excelize.OpenFile("crawler.xlsx")
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	defer f.Close()

	rows, err := f.GetRows("Sheet1")
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	reg := regexp.MustCompile(`\d-\d`)
	datas := []map[string]string{}
	for key, row := range rows {
		if key == 0 {
			continue
		}

		modelCode := strings.TrimSpace(row[0])

		size := ""
		if row[1] != "" {
			size = strings.TrimSpace(row[1])
		}

		suffix := ""
		if len(row) > 2 {
			suffix = strings.TrimSpace(row[2])
		}

		if modelCode == "" && size == "" {
			continue
		}

		if reg.MatchString(size) {
			t := strings.Split(size, "-")
			t1, _ := strconv.Atoi(t[0])
			t2, _ := strconv.Atoi(t[1])
			for i := t1; i <= t2; i++ {
				datas = append(datas, map[string]string{
					"model_code": modelCode,
					"size":       fmt.Sprintf("%d码", i),
					"suffix":     suffix,
				})
			}
		} else {
			datas = append(datas, map[string]string{
				"model_code": modelCode,
				"size":       size,
				"suffix":     suffix,
			})
		}
	}

	user := "wlhy_all"
	password := "@6BRQkbEL9zGi@wX"
	host := "rm-8vb3b461pm1299j5s8o.mysql.zhangbei.rds.aliyuncs.com"
	port := 3306
	database := "wlhy_test"
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&parseTime=True&loc=Local", user, password, host, port, database)

	db, err := gorm.Open(mysql.New(mysql.Config{
		DSN: dsn,
	}))
	if err != nil {
		logger.Stdout.Error(err.Error())
		return

	}

	modelCodes := []string{}
	saveDatas := []map[string]any{}
	for _, v := range datas {
		if slices.Contains(modelCodes, v["model_code"]) {
			continue
		}
		modelCodes = append(modelCodes, v["model_code"])
		saveDatas = append(saveDatas, map[string]any{
			"model_code": v["model_code"],
			"size":       v["size"],
			"suffix":     v["suffix"],
		})
	}
	err = db.Transaction(func(tx *gorm.DB) error {

		if err := tx.Table("crawler_decalthlon_conversion").Create(saveDatas).Error; err != nil {
			return err
		}

		for _, modelCode := range modelCodes {
			var isExist int64
			if err := tx.Table("crawler_decathlon_model_code").Where("model_code = ?", modelCode).Count(&isExist).Error; err != nil {
				return err
			}
			if isExist > 0 {
				continue
			}
			if err := tx.Table("crawler_decathlon_model_code").Create(map[string]any{
				"model_code": modelCode,
				"check_date": time.Now().Format("2006-01-02"),
			}).Error; err != nil {
				return err
			}
		}
		return nil
	})
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	fmt.Println("Done")

}

func QueryCompanyFreight() {
	rows, err := model.DB.Table("tms_shipper AS a").
		Joins("JOIN tms_order_history AS b ON a.company_id = b.create_company_id").
		Joins("JOIN tms_transport_note as c ON b.id = c.order_id AND b.version_no = c.order_version_no").
		Select([]string{"a.company_name", "b.goods_number", "b.loading_name", "b.loading_address", "b.unload_name",
			"b.unload_address", "c.freight_amount", "c.service_charge", "c.freight_paid", "c.service_fee_paid",
			"c.unload_time", "c.finished_time", "c.waybill_status"}).
		Where("a.company_name = ?", "内蒙古昊通能源有限公司").
		Where("a.shipper_type = ? AND a.is_delete = ?", 1, 0).
		Where("c.waybill_status IN (?)", []int{3, 4, 5, 6, 7, 8, 9}).
		Where("c.loading_time >= ?", "2025-05-01 00:00:00").
		Rows()
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	type GoodsNumberT struct {
		LineName     string
		TotalNotes   int
		TotalFreight float64
		Details      map[string]map[string]float64
	}

	goodsNumbers := make(map[string]GoodsNumberT)

	for rows.Next() {
		var companyName string
		var goodsNumber string
		var loadingName string
		var loadingAddress string
		var unloadName string
		var unloadAddress string
		var freightAmount float64
		var serviceCharge float64
		var freightPaid sql.NullFloat64
		var serviceFeePaid sql.NullFloat64
		var unloadTime sql.NullTime
		var finishedTime sql.NullTime
		var waybillStatus int
		if err := rows.Scan(&companyName, &goodsNumber, &loadingName, &loadingAddress, &unloadName, &unloadAddress,
			&freightAmount, &serviceCharge, &freightPaid, &serviceFeePaid, &unloadTime, &finishedTime, &waybillStatus); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}

		if _, ok := goodsNumbers[goodsNumber]; !ok {
			goodsNumbers[goodsNumber] = GoodsNumberT{
				LineName:     loadingName + "-" + unloadName,
				TotalNotes:   0,
				TotalFreight: 0.0,
				Details: map[string]map[string]float64{
					"0-2": {
						"notes":   0.0,
						"freight": 0.0,
					},
					"3-5": {
						"notes":   0.0,
						"freight": 0.0,
					},
					"6-7": {
						"notes":   0.0,
						"freight": 0.0,
					},
					"8-14": {
						"notes":   0.0,
						"freight": 0.0,
					},
					"15-21": {
						"notes":   0.0,
						"freight": 0.0,
					},
					"21-30": {
						"notes":   0.0,
						"freight": 0.0,
					},
					"30+": {
						"notes":   0.0,
						"freight": 0.0,
					},
					"cancel": {
						"notes":   0.0,
						"freight": 0.0,
					},
				},
			}
		}

		freight := freightAmount + serviceCharge
		if freightPaid.Valid && freightPaid.Float64 > 0 {
			freight = freightPaid.Float64 + serviceFeePaid.Float64
		}

		t := goodsNumbers[goodsNumber]
		t.TotalNotes += 1
		t.TotalFreight += freight

		if lo.Contains([]int{6, 7}, waybillStatus) {
			t.Details["cancel"]["notes"] += 1
			t.Details["cancel"]["freight"] += freight
		} else if waybillStatus == 9 {
			t.Details["30+"]["notes"] += 1
			t.Details["cancel"]["freight"] += freight
		} else {
			diff := math.Ceil(finishedTime.Time.Sub(unloadTime.Time).Hours() / 24)
			if diff <= 2 {
				t.Details["0-2"]["notes"] += 1
				t.Details["0-2"]["freight"] += freight
			} else if diff <= 5 {
				t.Details["3-5"]["notes"] += 1
				t.Details["3-5"]["freight"] += freight
			} else if diff <= 7 {
				t.Details["6-7"]["notes"] += 1
				t.Details["6-7"]["freight"] += freight
			} else if diff <= 14 {
				t.Details["8-14"]["notes"] += 1
				t.Details["8-14"]["freight"] += freight
			} else if diff <= 21 {
				t.Details["15-21"]["notes"] += 1
				t.Details["15-21"]["freight"] += freight
			} else if diff <= 30 {
				t.Details["21-30"]["notes"] += 1
				t.Details["21-30"]["freight"] += freight
			} else {
				t.Details["30+"]["notes"] += 1
				t.Details["30+"]["freight"] += freight
			}
		}

		goodsNumbers[goodsNumber] = t
	}

	cellKey := map[string][]string{
		"0-2":    {"E", "F"},
		"3-5":    {"G", "H"},
		"6-7":    {"I", "J"},
		"8-14":   {"K", "L"},
		"15-21":  {"M", "N"},
		"21-30":  {"O", "P"},
		"30+":    {"Q", "R"},
		"cancel": {"S", "T"},
	}

	f := excelize.NewFile()
	defer f.Close()

	cellIndex := 2
	for k, v := range goodsNumbers {
		f.SetCellValue("Sheet1", fmt.Sprintf("A%d", cellIndex), k)
		f.SetCellValue("Sheet1", fmt.Sprintf("B%d", cellIndex), v.LineName)
		f.SetCellValue("Sheet1", fmt.Sprintf("C%d", cellIndex), v.TotalNotes)
		f.SetCellValue("Sheet1", fmt.Sprintf("D%d", cellIndex), v.TotalFreight)
		for k2, v2 := range v.Details {
			f.SetCellValue("Sheet1", fmt.Sprintf("%s%d", cellKey[k2][0], cellIndex), v2["notes"])
			f.SetCellValue("Sheet1", fmt.Sprintf("%s%d", cellKey[k2][1], cellIndex), v2["freight"])
		}
		cellIndex++
	}

	if err := f.SaveAs("lines.xlsx"); err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
}

// PayNowDrivers 立即付司机信息
func PayNowDrivers(goodsNumbers []string) []string {
	rows, err := model.DB.Table("tms_order AS a").
		Joins("JOIN tms_transport_note AS b ON a.id = b.order_id").
		Joins("JOIN tms_shipper AS c ON b.create_company_id = c.company_id").
		Where("a.goods_number IN (?)", goodsNumbers).
		Where("b.waybill_status NOT IN (?)", []int{6, 7}).
		Where("b.transportation_driver_id = b.payee_id").
		Where("c.shipper_type = ? AND c.is_delete = ?", 1, 0).
		Select([]string{"b.transportation_driver_id", "c.company_name", "a.goods_number", "b.transportation_driver", "b.transportation_phone", "b.transportation_plate", "b.freight_amount"}).
		Rows()
	if err != nil {
		logger.Stdout.Error(err.Error())
		return nil
	}

	driverRows, err := model.DB.Table("tms_transport_immediately_driver_config").
		Select([]string{"driver_id"}).
		Where("is_delete = ?", 0).
		Rows()
	if err != nil {
		logger.Stdout.Error(err.Error())
		return nil
	}

	existDriverIDs := make(map[string]struct{})
	for driverRows.Next() {
		var driverID string
		if err := driverRows.Scan(&driverID); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}
		existDriverIDs[driverID] = struct{}{}
	}

	type ItemT struct {
		CompanyName          string
		GoodsNumber          string
		TransportationDriver string
		TransportationPhone  string
		TransportationPlate  string
		FreightAmount        float64
	}

	m := make(map[string][]ItemT)
	for rows.Next() {
		var transportationDriverId string
		var companyName string
		var goodsNumber string
		var transportationDriver string
		var transportationPhone string
		var transportationPlate string
		var freightAmount float64
		if err := rows.Scan(&transportationDriverId, &companyName, &goodsNumber, &transportationDriver, &transportationPhone, &transportationPlate, &freightAmount); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}

		if _, ok := existDriverIDs[transportationDriverId]; ok {
			continue
		}

		if _, ok := m[transportationDriverId]; !ok {
			m[transportationDriverId] = []ItemT{}
		}

		item := ItemT{
			CompanyName:          companyName,
			GoodsNumber:          goodsNumber,
			TransportationDriver: transportationDriver,
			TransportationPhone:  transportationPhone,
			TransportationPlate:  transportationPlate,
			FreightAmount:        freightAmount,
		}
		m[transportationDriverId] = append(m[transportationDriverId], item)
	}

	f := excelize.NewFile()
	defer f.Close()

	f.SetCellValue("Sheet1", "A1", "司机姓名")
	f.SetCellValue("Sheet1", "B1", "手机号")
	f.SetCellValue("Sheet1", "C1", "车牌号")
	f.SetCellValue("Sheet1", "D1", "运费")
	f.SetCellValue("Sheet1", "E1", "运单数量")
	f.SetCellValue("Sheet1", "F1", "公司名称")
	f.SetCellValue("Sheet1", "G1", "货单号")
	f.SetCellValue("Sheet1", "H1", "司机ID")

	driverIDs := []string{}
	cellIndex := 2
	for driverID, v := range m {
		var noteNum int64
		if err := model.DB.Table("tms_transport_note").
			Where("transportation_driver_id = ?", driverID).
			Where("is_delete = ?", 0).
			Where("waybill_status NOT IN (?)", []int{6, 7}).
			Count(&noteNum).Error; err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}
		if noteNum <= 1 {
			continue
		}

		freightAmount := 0.0
		for _, item := range v {
			freightAmount += item.FreightAmount
		}

		f.SetCellValue("Sheet1", fmt.Sprintf("A%d", cellIndex), v[0].TransportationDriver)
		f.SetCellValue("Sheet1", fmt.Sprintf("B%d", cellIndex), v[0].TransportationPhone)
		f.SetCellValue("Sheet1", fmt.Sprintf("C%d", cellIndex), v[0].TransportationPlate)
		f.SetCellValue("Sheet1", fmt.Sprintf("D%d", cellIndex), freightAmount)
		f.SetCellValue("Sheet1", fmt.Sprintf("E%d", cellIndex), noteNum)
		f.SetCellValue("Sheet1", fmt.Sprintf("F%d", cellIndex), v[0].CompanyName)
		f.SetCellValue("Sheet1", fmt.Sprintf("G%d", cellIndex), v[0].GoodsNumber)
		f.SetCellValue("Sheet1", fmt.Sprintf("H%d", cellIndex), driverID)

		driverIDs = append(driverIDs, driverID)
		cellIndex++
	}

	if err := f.SaveAs("立即付司机信息.xlsx"); err != nil {
		logger.Stdout.Error(err.Error())
		return nil
	}
	return driverIDs
}

// PayNowOrderWhiteList 立即付货单白名单
func PayNowOrderWhiteList(goodsNumbers []string) {
	orderRows, err := model.DB.Table("tms_transport_immediately_config").
		Select([]string{"goods_number"}).
		Where("is_delete = ?", 0).
		Rows()
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	existGoodsNumbers := make(map[string]struct{})
	for orderRows.Next() {
		var goodsNumber string
		if err := orderRows.Scan(&goodsNumber); err != nil {
			panic(err)
		}
		existGoodsNumbers[goodsNumber] = struct{}{}
	}

	rows, err := model.DB.Table("tms_order AS a").
		Joins("JOIN tms_shipper AS b ON a.create_company_id = b.company_id").
		Select([]string{"a.goods_number", "a.id", "b.company_name"}).
		Where("a.goods_number IN (?)", goodsNumbers).
		Where("b.shipper_type = ? AND b.is_delete = ?", 1, 0).
		Rows()
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	orderData := []map[string]any{}
	for rows.Next() {
		var goodsNumber string
		var id string
		var companyName string
		if err := rows.Scan(&goodsNumber, &id, &companyName); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}
		if _, ok := existGoodsNumbers[goodsNumber]; ok {
			continue
		}
		orderData = append(orderData, map[string]any{
			"id":           fmt.Sprintf("%d%d", time.Now().UnixMicro(), rand.Intn(9999)),
			"create_by":    "sys",
			"create_time":  time.Now().Format("2006-01-02 15:04:05"),
			"update_by":    "sys",
			"update_time":  time.Now().Format("2006-01-02 15:04:05"),
			"is_delete":    0,
			"shipper_name": companyName,
			"goods_number": goodsNumber,
			"order_id":     id,
		})

	}

	if err := model.DB.Table("tms_transport_immediately_config").Create(orderData).Error; err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	fmt.Println("Done")
}

// PayNowDriverWhiteList 立即付司机白名单
func PayNowDriverWhiteList(driverIDs []string) {
	driverRows, err := model.DB.Table("tms_transport_immediately_driver_config").
		Select([]string{"driver_id"}).
		Where("is_delete = ?", 0).
		Rows()
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	existDriverIDs := make(map[string]struct{})
	for driverRows.Next() {
		var driverID string
		if err := driverRows.Scan(&driverID); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}
		existDriverIDs[driverID] = struct{}{}
	}

	rows, err := model.DB.Table("tms_driver").
		Select([]string{"driver_id", "driver_name"}).
		Where("driver_id IN (?)", driverIDs).
		Where("is_delete = ?", 0).
		Rows()
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	t, err := os.OpenFile("drivers.txt", os.O_WRONLY|os.O_CREATE|os.O_APPEND, 0666)
	if err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	defer t.Close()

	driverData := []map[string]any{}
	for rows.Next() {
		var driverID string
		var driverName string
		if err := rows.Scan(&driverID, &driverName); err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}

		if _, ok := existDriverIDs[driverID]; ok {
			continue
		}

		driverData = append(driverData, map[string]any{
			"id":          fmt.Sprintf("%d%d", time.Now().UnixMicro(), rand.Intn(9999)),
			"create_by":   "sys",
			"create_time": time.Now().Format("2006-01-02 15:04:05"),
			"update_by":   "sys",
			"update_time": time.Now().Format("2006-01-02 15:04:05"),
			"is_delete":   0,
			"driverName":  driverName,
			"driver_id":   driverID,
		})
		fmt.Println(driverID)
		t.WriteString(driverID + "\n")
	}

	if err := model.DB.Table("tms_transport_immediately_driver_config").Create(driverData).Error; err != nil {
		logger.Stdout.Error(err.Error())
		return
	}

	fmt.Println("Done")
}

func ShouXuFeiMatch() {
	f, err := excelize.OpenFile("6月份手续费.xlsx")
	if err != nil {
		return
	}
	defer f.Close()

	rows, err := f.GetRows("Sheet1")
	if err != nil {
		return
	}

	transportationNumbers := []string{}
	for key, row := range rows {
		if key == 0 {
			continue
		}
		if len(row) < 9 {
			continue
		}

		transportationNumber := strings.ReplaceAll(strings.TrimSpace(row[8]), "手续费", "")

		transportationNumbers = append(transportationNumbers, transportationNumber)
	}

	transportationNumbers = lo.Uniq(transportationNumbers)

	mm := make(map[string]string)
	rr, _ := model.DB.Table("tms_transport_note AS a").
		Joins("JOIN tms_shipper AS b ON a.create_company_id = b.company_id").
		Select([]string{"a.transportation_number", "b.company_name"}).
		Where("a.transportation_number IN (?)", transportationNumbers).
		Where("b.shipper_type = ? AND b.is_delete = ?", 1, 0).
		Rows()
	for rr.Next() {
		var transportationNumber string
		var companyName string
		if err := rr.Scan(&transportationNumber, &companyName); err != nil {
			return
		}
		mm[transportationNumber] = companyName
	}

	for key, row := range rows {
		if key == 0 {
			continue
		}
		if len(row) < 9 {
			continue
		}

		transportationNumber := strings.ReplaceAll(strings.TrimSpace(row[8]), "手续费", "")

		f.SetCellValue("Sheet1", fmt.Sprintf("J%d", key+1), mm[transportationNumber])
	}

	f.SaveAs("6月份手续费-with-company.xlsx")
}

func DuoMengDeOilBack(transportationNumber string) {
	// 油卡金额与货单的关联关系
	oilAmount := map[string]float64{
		"TYBM2506291022446001": 1000,
		"TYBM2506291019475752": 1000,
		"TYBM2506291018579380": 1000,
		"TYBM2506291013378270": 1000,
		"TYBM2506291013012643": 1000,
		"TYBM2506291009171511": 1000,
		"TYBM2506291006250221": 1000,
		"TYBM2506291006080263": 500,
	}
	goodsNumber := []string{}
	for k := range oilAmount {
		goodsNumber = append(goodsNumber, k)
	}

	type NoteT struct {
		GoodsNumber          string `gorm:"column:goods_number"`
		ShipperID            string `gorm:"column:shipper_id"`
		ShipperName          string `gorm:"column:shipper_name"`
		ShipperPhone         string `gorm:"column:shipper_phone"`
		ShipperCompanyName   string `gorm:"column:shipper_company_name"`
		ShipperDmanbr        string `gorm:"column:shipper_dmanbr"`
		DriverID             string `gorm:"column:driver_id"`
		DriverName           string `gorm:"column:driver_name"`
		DriverPhone          string `gorm:"column:driver_phone"`
		DriverDmanbr         string `gorm:"column:driver_dmanbr"`
		TransportationID     string `gorm:"column:transportation_id"`
		TransportationNumber string `gorm:"column:transportation_number"`
		TransportationPlate  string `gorm:"column:transportation_plate"`
		PlatformCompanyID    string `gorm:"column:appoint_company_id"`
		PlatformCompanyName  string `gorm:"column:appoint_company_name"`
		WaybillStatus        int    `gorm:"column:waybill_status"`
	}

	var notes []NoteT
	if err := model.DB.Table("tms_transport_note AS a").
		Joins("JOIN tms_order_history AS b ON a.order_id = b.id AND a.order_version_no = b.version_no").
		Joins("JOIN tms_shipper AS c ON a.create_company_id = c.company_id").
		Joins("JOIN tms_driver AS d ON a.payee_id = d.driver_id").
		Joins("JOIN tms_shipper_balance AS e on c.shipper_id = e.shipper_id").
		Select([]string{"b.goods_number", "c.shipper_id", "c.shipper_name", "c.shipper_phone",
			"c.company_name AS shipper_company_name", "d.driver_id", "d.driver_name", "d.phone AS driver_phone",
			"a.id AS transportation_id", "a.transportation_number", "a.transportation_plate", "b.appoint_company_id", "b.appoint_company_name", "a.waybill_status"}).
		Where("b.goods_number IN ?", goodsNumber).
		Where("a.transportation_number = ?", transportationNumber).
		Where("a.is_delete = ?", 0).
		Where("c.shipper_type = ? AND c.is_delete = ?", 1, 0).
		Where("d.is_delete = ?", 0).
		Scan(&notes).Error; err != nil {
		logger.Stdout.Error(err.Error())
		return
	}
	if len(notes) == 0 {
		return
	}

	for _, note := range notes {
		if lo.Contains([]int{3, 4, 5, 8}, note.WaybillStatus) {
			logger.Stdout.Error(fmt.Sprintf("运单状态不正确: %s, %d", note.TransportationNumber, note.WaybillStatus))
			continue
		}

		err := model.DB.Transaction(func(tx *gorm.DB) error {
			// 查询货主余额
			var shipperBalance model.TmsShipperBalance
			if err := tx.
				Where("shipper_id = ?", note.ShipperID).
				First(&shipperBalance).Error; err != nil {
				return err
			}

			// 查询司机油卡余额
			var driverCompanyOil model.TmsDriverCompanyOil
			if err := tx.
				Where("driver_id = ? AND company_id = ?", note.DriverID, note.PlatformCompanyID).
				First(&driverCompanyOil).Error; err != nil {
				return err
			}

			// 清空运单的freight_paid和service_fee_paid
			if err := tx.Table("tms_transport_note").
				Where("id = ?", note.TransportationID).
				Updates(map[string]any{
					"freight_paid":     0,
					"service_fee_paid": 0,
				}).Error; err != nil {
				return err
			}

			// 删除支付计划
			if err := tx.Model(&model.TmsTransportPayPlan{}).
				Where("waybill_No = ?", note.TransportationNumber).
				Where("type = ?", "2").
				Where("is_delete = ?", 0).
				Updates(map[string]any{
					"is_delete": 1,
				}).Error; err != nil {
				return err
			}

			payBatchNumber := uuid.New().String()

			// 增加货主收入流水
			tx.Model(&model.TmsExpenseRecord{}).Create(&model.TmsExpenseRecord{
				SerialNumber:               toolbox.HszyPrimaryID(),
				PayBatchNumber:             payBatchNumber,
				WaybillID:                  note.TransportationID,
				WaybillNumber:              note.TransportationNumber,
				SerialNumberType:           0,
				ReasonsForChange:           13,
				TransactionAmount:          oilAmount[note.GoodsNumber],
				AccountBalance:             shipperBalance.AccountBalanceLocal + oilAmount[note.GoodsNumber],
				RevenueAndExpenditureTypes: 0,
				AccountType:                1,
				AccountName:                note.ShipperName,
				AccountNumber:              note.ShipperPhone,
				AccountID:                  note.ShipperID,
				DriverInformation:          note.DriverName + note.TransportationPlate,
				Remark:                     "回收预付电子油卡",
				CreateTime:                 time.Now(),
				ConsumptionTime:            time.Now(),
				TransactionTime:            time.Now(),
				TradeStatus:                1,
				UpdateTime:                 time.Now(),
				IsDelete:                   0,
				SysOrgCode:                 "A03",
				IsCapitalReporting:         1,
				CapitalReportingTime:       time.Now(),
				VerifiedTime:               time.Now(),
			})

			// 增加司机支出流水
			tx.Model(&model.TmsExpenseRecord{}).Create(&model.TmsExpenseRecord{
				SerialNumber:               toolbox.HszyPrimaryID(),
				PayBatchNumber:             payBatchNumber,
				WaybillID:                  note.TransportationID,
				WaybillNumber:              note.TransportationNumber,
				SerialNumberType:           0,
				ReasonsForChange:           14,
				TransactionAmount:          -oilAmount[note.GoodsNumber],
				AccountBalance:             driverCompanyOil.OilBalance - oilAmount[note.GoodsNumber],
				RevenueAndExpenditureTypes: 1,
				AccountType:                2,
				AccountName:                note.DriverName,
				AccountNumber:              note.DriverPhone,
				AccountID:                  note.DriverID,
				DriverInformation:          note.DriverName + note.TransportationPlate,
				Remark:                     "回收预付电子油卡",
				CreateTime:                 time.Now(),
				ConsumptionTime:            time.Now(),
				TransactionTime:            time.Now(),
				TradeStatus:                1,
				UpdateTime:                 time.Now(),
				IsDelete:                   0,
				SysOrgCode:                 "A03",
				IsCapitalReporting:         1,
				CapitalReportingTime:       time.Now(),
				IsItVerified:               1,
				VerifiedTime:               time.Now(),
				DriverID:                   note.DriverID,
			})

			// 增加司机油卡扣减流水
			tx.Create(&model.TmsDriverOilWriteOffRecord{
				ID:                   toolbox.HszyPrimaryID(),
				CreateBy:             note.DriverID,
				CreateTime:           time.Now(),
				UpdateBy:             note.DriverID,
				UpdateTime:           time.Now(),
				IsDelete:             0,
				DriverID:             note.DriverID,
				DriverName:           note.DriverName,
				VehicleLicenseNumber: note.TransportationPlate,
				CompanyID:            note.PlatformCompanyID,
				TotalPrice:           oilAmount[note.GoodsNumber],
				WriteOffTime:         time.Now(),
				Desc:                 "回收预付电子油卡",
				CurrentBalance:       driverCompanyOil.OilBalance - oilAmount[note.GoodsNumber],
				TransportationNumber: note.TransportationNumber,
				RecordType:           2,
				DriverType:           1,
			})

			// 扣减司机油卡余额
			if err := tx.Model(&model.TmsDriverCompanyOil{}).
				Where("driver_id = ? AND company_id = ?", note.DriverID, note.PlatformCompanyID).
				Updates(map[string]any{
					"oil_balance": driverCompanyOil.OilBalance - oilAmount[note.GoodsNumber],
				}).Error; err != nil {
				return err
			}

			// 增加货主余额
			if err := tx.Model(&model.TmsShipperBalance{}).
				Where("shipper_id = ?", note.ShipperID).
				Updates(map[string]any{
					"version":               shipperBalance.Version + 1,
					"update_time":           time.Now(),
					"account_balance_local": shipperBalance.AccountBalanceLocal + oilAmount[note.GoodsNumber],
				}).Error; err != nil {
				return err
			}

			return nil
		})

		if err != nil {
			logger.Stdout.Error(err.Error())
			continue
		}
	}

	fmt.Println("Done")
}
