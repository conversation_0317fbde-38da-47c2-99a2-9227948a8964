package email

import (
	"crypto/tls"
	"errors"
	"os"

	"gopkg.in/gomail.v2"
)

type Email struct {
	To      []string
	Subject string
	Attach  string
	Body    string
}

// SendEmail sends an email with the given recipients, subject, and optional attachment.
//
// Parameters:
// - to: a slice of strings representing the email addresses of the recipients.
// - subject: a string representing the subject of the email.
//
// Returns:
// - error: an error if there was a problem sending the email, otherwise nil.
func (e *Email) SendEmail() error {
	if len(e.To) == 0 {
		return errors.New("收件人不能为空")
	}
	if e.Subject == "" {
		return errors.New("主题不能为空")
	}

	d := gomail.NewDialer("smtp.qq.com", 587, "<EMAIL>", "dtusitcqdzrrbcfd")
	d.TLSConfig = &tls.Config{InsecureSkipVerify: true}

	m := gomail.NewMessage()
	m.SetHeader("From", "<EMAIL>")
	m.SetHeader("To", e.To...)
	m.Set<PERSON>eader("Subject", e.Subject)
	if e.Attach != "" {
		_, err := os.Stat(e.Attach)
		if err != nil {
			return errors.New("附件不存在，" + err.Error())
		}
		m.Attach(e.Attach)
	}

	if e.Body != "" {
		m.SetBody("text/html", e.Body)
	}

	// Send the email to Bob, Cora and Dan.
	if err := d.DialAndSend(m); err != nil {
		return err
	}
	return nil
}
