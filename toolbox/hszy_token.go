package toolbox

import (
	"context"
	"encoding/json"
	"fmt"
	"wlhy/toolbox/logger"

	"github.com/redis/go-redis/v9"
)

// HszyTokenT hszy token结构体
type HszyTokenT struct {
	TokenSignList []string
}

// UnmarshalJSON 自定义 JSON 反序列化
func (h *HszyTokenT) UnmarshalJSON(data []byte) error {
	// 临时结构体解析原始 JSON
	type tempTokenSign struct {
		Value string `json:"value"`
	}
	type tempResponse struct {
		TokenSignList []any `json:"tokenSignList"`
	}

	var temp tempResponse
	if err := json.Unmarshal(data, &temp); err != nil {
		return err
	}

	// 校验 tokenSignList 格式
	if len(temp.TokenSignList) != 2 {
		return fmt.Errorf("invalid tokenSignList format")
	}

	// 验证第一个元素为 "java.util.Vector"
	vectorName, ok := temp.TokenSignList[0].(string)
	if !ok || vectorName != "java.util.Vector" {
		return fmt.Errorf("invalid vector name")
	}

	// 解析第二个元素（对象数组）
	tokenData, err := json.Marshal(temp.TokenSignList[1])
	if err != nil {
		return err
	}
	var tokenSigns []tempTokenSign
	if err := json.Unmarshal(tokenData, &tokenSigns); err != nil {
		return err
	}

	// 提取 value 字段
	h.TokenSignList = make([]string, len(tokenSigns))
	for i, ts := range tokenSigns {
		h.TokenSignList[i] = ts.Value
	}

	return nil
}

// HszyToken 获取hszy用户token
func HszyToken(rdb *redis.Client, userID string) (string, error) {
	tokenStr, err := rdb.Get(context.Background(), "X-Access-Token:login:session:"+userID).Result()
	if err != nil {
		logger.Stdout.Error(err.Error())
		return "", err
	}

	var token HszyTokenT
	json.Unmarshal([]byte(tokenStr), &token)

	return token.TokenSignList[len(token.TokenSignList)-1], nil
}
