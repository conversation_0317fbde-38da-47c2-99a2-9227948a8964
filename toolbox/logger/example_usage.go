package main

import (
	"time"

	"github.com/rs/zerolog"
	"wlhy/toolbox/logger"
)

func main() {
	// 示例1: 使用默认配置的全局函数
	logger.Info("这是使用默认配置的日志")
	logger.Error("这是一个错误日志")

	// 示例2: 使用自定义高性能配置
	opts := &logger.Options{
		FilePrefix:           "myapp",
		LogDir:               "custom_logs",
		Level:                zerolog.DebugLevel,
		TimeFormat:           "2006-01-02 15:04:05.000",
		EnableConsole:        true,  // 同时输出到控制台
		EnableFile:           true,  // 输出到文件
		BufferSize:           8192,  // 8KB 缓冲区
		FlushTimeout:         500 * time.Millisecond, // 500ms 自动刷新
		MaxFileSize:          50 * 1024 * 1024, // 50MB 文件大小限制
		CallerSkipFrameCount: 3,
		AddSource:            true,
		FileMode:             0644,
		DirMode:              0755,
	}

	customLogger, err := logger.NewLogger(opts)
	if err != nil {
		panic(err)
	}
	defer customLogger.Close()

	// 高性能日志记录
	customLogger.Debug("这是调试日志")
	customLogger.Info("这是信息日志")
	customLogger.Warn("这是警告日志")
	customLogger.Error("这是错误日志")

	// 示例3: 动态更改默认配置
	newOpts := logger.DefaultOptions()
	newOpts.FilePrefix = "updated"
	newOpts.EnableConsole = true
	newOpts.BufferSize = 16384 // 16KB 缓冲区
	
	if err := logger.SetOptions(newOpts); err != nil {
		panic(err)
	}

	logger.Info("使用新配置的日志")

	// 示例4: 性能测试
	start := time.Now()
	for i := 0; i < 10000; i++ {
		logger.Info("高性能日志测试", i)
	}
	logger.Flush() // 强制刷新缓冲区
	elapsed := time.Since(start)
	logger.Info("写入10000条日志耗时:", elapsed)

	// 示例5: 向后兼容性测试
	oldLogger := logger.NewZerolog("legacy")
	oldLogger.Info().Msg("使用旧接口的日志")

	// 程序结束时关闭日志
	logger.Close()
}

// 性能基准测试函数
func BenchmarkLogger() {
	opts := &logger.Options{
		FilePrefix:   "benchmark",
		LogDir:       "bench_logs",
		Level:        zerolog.InfoLevel,
		EnableFile:   true,
		EnableConsole: false,
		BufferSize:   32768, // 32KB 缓冲区
		FlushTimeout: time.Second,
	}

	benchLogger, err := logger.NewLogger(opts)
	if err != nil {
		panic(err)
	}
	defer benchLogger.Close()

	// 模拟高并发日志写入
	const numGoroutines = 10
	const logsPerGoroutine = 1000

	start := time.Now()
	
	done := make(chan bool, numGoroutines)
	for i := 0; i < numGoroutines; i++ {
		go func(id int) {
			for j := 0; j < logsPerGoroutine; j++ {
				benchLogger.Info("并发日志测试", "goroutine", id, "log", j)
			}
			done <- true
		}(i)
	}

	// 等待所有 goroutine 完成
	for i := 0; i < numGoroutines; i++ {
		<-done
	}

	benchLogger.Flush()
	elapsed := time.Since(start)
	
	totalLogs := numGoroutines * logsPerGoroutine
	logger.Info("并发性能测试完成", 
		"总日志数", totalLogs,
		"耗时", elapsed,
		"QPS", float64(totalLogs)/elapsed.Seconds())
}
