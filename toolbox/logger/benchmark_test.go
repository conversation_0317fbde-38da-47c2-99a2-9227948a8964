package logger

import (
	"os"
	"testing"
	"time"

	"github.com/rs/zerolog"
)

// BenchmarkNewLogger 测试新版本日志性能
func BenchmarkNewLogger(b *testing.B) {
	opts := &Options{
		FilePrefix:   "bench_new",
		LogDir:       "bench_logs",
		Level:        zerolog.InfoLevel,
		EnableFile:   true,
		EnableConsole: false,
		BufferSize:   32768,
		FlushTimeout: time.Second,
		AddSource:    false, // 关闭源码位置以提高性能
	}

	logger, err := NewLogger(opts)
	if err != nil {
		b.Fatal(err)
	}
	defer logger.Close()

	b.ResetTimer()
	b.<PERSON>(func(pb *testing.PB) {
		for pb.Next() {
			logger.Info("benchmark test message", "key", "value", "number", 42)
		}
	})
}

// BenchmarkNewLoggerWithBuffer 测试带缓冲的日志性能
func BenchmarkNewLoggerWithBuffer(b *testing.B) {
	opts := &Options{
		FilePrefix:   "bench_buffer",
		LogDir:       "bench_logs",
		Level:        zerolog.InfoLevel,
		EnableFile:   true,
		EnableConsole: false,
		BufferSize:   65536, // 64KB 缓冲区
		FlushTimeout: 5 * time.Second,
		AddSource:    false,
	}

	logger, err := NewLogger(opts)
	if err != nil {
		b.Fatal(err)
	}
	defer logger.Close()

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			logger.Info("benchmark test message with buffer", "key", "value", "number", 42)
		}
	})
}

// BenchmarkGlobalFunctions 测试全局函数性能
func BenchmarkGlobalFunctions(b *testing.B) {
	// 设置高性能配置
	opts := &Options{
		FilePrefix:   "bench_global",
		LogDir:       "bench_logs",
		Level:        zerolog.InfoLevel,
		EnableFile:   true,
		EnableConsole: false,
		BufferSize:   32768,
		FlushTimeout: time.Second,
		AddSource:    false,
	}

	if err := SetOptions(opts); err != nil {
		b.Fatal(err)
	}
	defer Close()

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			Info("benchmark global function", "key", "value", "number", 42)
		}
	})
}

// BenchmarkDirectZerolog 测试直接使用 zerolog 的性能（对比基准）
func BenchmarkDirectZerolog(b *testing.B) {
	file, err := os.OpenFile("bench_logs/direct_zerolog.log", 
		os.O_WRONLY|os.O_CREATE|os.O_APPEND, 0644)
	if err != nil {
		b.Fatal(err)
	}
	defer file.Close()

	logger := zerolog.New(file).Level(zerolog.InfoLevel)

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			logger.Info().Str("key", "value").Int("number", 42).Msg("benchmark direct zerolog")
		}
	})
}

// TestLogRotation 测试日志轮转功能
func TestLogRotation(t *testing.T) {
	opts := &Options{
		FilePrefix:   "rotation_test",
		LogDir:       "test_logs",
		Level:        zerolog.InfoLevel,
		EnableFile:   true,
		EnableConsole: false,
		BufferSize:   1024,
		FlushTimeout: 100 * time.Millisecond,
		MaxFileSize:  1024, // 1KB 文件大小限制，便于测试
	}

	logger, err := NewLogger(opts)
	if err != nil {
		t.Fatal(err)
	}
	defer logger.Close()

	// 写入足够多的日志触发轮转
	for i := 0; i < 100; i++ {
		logger.Info("rotation test message", "iteration", i, "data", "some long data to fill up the file quickly")
		if i%10 == 0 {
			logger.Flush() // 定期刷新以检查文件大小
		}
	}

	logger.Flush()
	time.Sleep(200 * time.Millisecond) // 等待异步操作完成

	// 检查是否生成了多个文件
	entries, err := os.ReadDir("test_logs")
	if err != nil {
		t.Fatal(err)
	}

	rotationFiles := 0
	for _, entry := range entries {
		if !entry.IsDir() && entry.Name() != "rotation_test_"+time.Now().Format("20060102")+".log" {
			rotationFiles++
		}
	}

	if rotationFiles == 0 {
		t.Log("注意: 可能没有触发文件轮转，这可能是正常的")
	}

	// 清理测试文件
	os.RemoveAll("test_logs")
}

// TestConcurrentAccess 测试并发访问安全性
func TestConcurrentAccess(t *testing.T) {
	opts := &Options{
		FilePrefix:   "concurrent_test",
		LogDir:       "test_logs",
		Level:        zerolog.InfoLevel,
		EnableFile:   true,
		EnableConsole: false,
		BufferSize:   4096,
		FlushTimeout: 100 * time.Millisecond,
	}

	logger, err := NewLogger(opts)
	if err != nil {
		t.Fatal(err)
	}
	defer logger.Close()

	const numGoroutines = 50
	const logsPerGoroutine = 100

	done := make(chan bool, numGoroutines)

	// 启动多个 goroutine 并发写入日志
	for i := 0; i < numGoroutines; i++ {
		go func(id int) {
			defer func() { done <- true }()
			for j := 0; j < logsPerGoroutine; j++ {
				logger.Info("concurrent test", "goroutine", id, "log", j)
			}
		}(i)
	}

	// 等待所有 goroutine 完成
	for i := 0; i < numGoroutines; i++ {
		<-done
	}

	logger.Flush()
	
	// 清理测试文件
	os.RemoveAll("test_logs")
}

// 清理基准测试文件
func init() {
	os.RemoveAll("bench_logs")
	os.MkdirAll("bench_logs", 0755)
}
