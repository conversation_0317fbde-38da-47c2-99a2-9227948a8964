package logger

import (
	"fmt"
	"log"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"github.com/rs/zerolog"
	"github.com/rs/zerolog/pkgerrors"
)

var Log *zerolog.Logger
var loggerWriter *LoggerWriter

func init() {
	Log = NewZerolog("")
}

// LoggerWriter manages log file rotation by date
type LoggerWriter struct {
	FilePrefix string
	file       *os.File
	today      string // Current date for log file
	mu         sync.Mutex
}

// SetFilePrefix updates the file prefix for log files
func (l *LoggerWriter) SetFilePrefix(prefix string) {
	l.mu.Lock()
	defer l.mu.Unlock()
	l.FilePrefix = prefix
}

// openNewFile creates or opens a log file for the current date
func (l *LoggerWriter) openNewFile(currentDate string) error {
	// Close existing file if open
	if l.file != nil {
		if err := l.file.Close(); err != nil {
			log.Printf("Failed to close previous log file: %v", err)
		}
	}

	// Construct log file path
	var filename string
	if l.FilePrefix != "" {
		filename = filepath.Join("logs", l.FilePrefix+"_"+currentDate+".log")
	} else {
		filename = filepath.Join("logs", currentDate+".log")
	}

	// Ensure logs directory exists
	if err := os.MkdirAll(filepath.Dir(filename), 0755); err != nil {
		log.Printf("Failed to create logs directory: %v", err)
		return err
	}

	// Open new file
	f, err := os.OpenFile(filename, os.O_WRONLY|os.O_CREATE|os.O_APPEND, 0644)
	if err != nil {
		log.Printf("Failed to open log file: %v", err)
		return err
	}

	// Update file and date
	l.file = f
	l.today = currentDate
	return nil
}

// Write implements io.Writer for logging to file
func (l *LoggerWriter) Write(p []byte) (n int, err error) {
	l.mu.Lock()
	defer l.mu.Unlock()

	// Get current date
	currentDate := time.Now().Format("20060102")

	// Rotate file if date has changed
	if l.today != currentDate {
		// Double-check date after acquiring lock
		if l.today != currentDate {
			if err := l.openNewFile(currentDate); err != nil {
				return 0, err
			}
		}
	}

	// Write to file
	n, err = l.file.Write(p)
	if err != nil {
		log.Printf("Failed to write log: %v", err)
		return 0, err
	}
	return n, nil
}

// NewZerolog initializes a new zerolog logger
func NewZerolog(filePrefix string) *zerolog.Logger {
	// Configure zerolog field names and format
	zerolog.TimestampFieldName = "t"
	zerolog.LevelFieldName = "l"
	zerolog.MessageFieldName = "m"
	zerolog.TimeFieldFormat = "2006-01-02 15:04:05"
	zerolog.ErrorStackMarshaler = pkgerrors.MarshalStack

	// Initialize LoggerWriter
	loggerWriter = &LoggerWriter{FilePrefix: filePrefix}
	// Open initial log file
	currentDate := time.Now().Format("20060102")
	if err := loggerWriter.openNewFile(currentDate); err != nil {
		log.Fatalf("Failed to initialize log file: %v", err)
	}

	// Create logger with timestamp and caller
	zeroLog := zerolog.New(loggerWriter).With().Timestamp().CallerWithSkipFrameCount(3).Logger()
	return &zeroLog
}

// write formats and logs the message
func write(event *zerolog.Event, args ...any) {
	msg := []string{}
	for _, v := range args {
		msg = append(msg, fmt.Sprintf("%+v", v))
	}
	event.Msg(strings.Join(msg, " "))
}

// SetFilePrefix updates the prefix for log files
func SetFilePrefix(prefix string) {
	loggerWriter.SetFilePrefix(prefix)
}

// Close closes the current log file
func Close() error {
	loggerWriter.mu.Lock()
	defer loggerWriter.mu.Unlock()
	if loggerWriter.file != nil {
		return loggerWriter.file.Close()
	}
	return nil
}

// Debug logs a debug message
func Debug(args ...any) {
	event := Log.Debug()
	write(event, args...)
}

// Info logs an info message
func Info(args ...any) {
	event := Log.Info()
	write(event, args...)
}

// Warn logs a warning message
func Warn(args ...any) {
	event := Log.Warn()
	write(event, args...)
}

// Error logs an error message
func Error(args ...any) {
	event := Log.Error()
	write(event, args...)
}
