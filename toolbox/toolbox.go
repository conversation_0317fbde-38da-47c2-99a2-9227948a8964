package toolbox

import (
	"archive/zip"
	"bytes"
	"errors"
	"fmt"
	"io"
	"math"
	"math/rand/v2"
	"net/http"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"time"

	"github.com/shopspring/decimal"
)

// DownloadFile 下载文件
func DownloadFile(filepath string, url string) error {
	// 判断是否已下载，若已下载则跳过
	if _, err := os.Stat(filepath); err == nil {
		return nil
	}

	// Get the data1435
	resp, err := http.Get(url)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	// Create the file
	out, err := os.Create(filepath)
	if err != nil {
		return err
	}
	defer out.Close()

	// Write the body to file
	_, err = io.Copy(out, resp.Body)
	return err
}

// IsInternalNetwork 判断是否内网,内网返回true,外网返回false
func IsInternalNetwork() bool {
	// 根据IP判断使用内网或外网host
	cmd := exec.Command("curl", "-m", "10", "https://ipinfo.io/ip")
	stdout := &bytes.Buffer{}
	cmd.Stdout = stdout
	if err := cmd.Run(); err != nil {
		return false
	}

	ip := stdout.String()
	if ip == "*************" || ip == "*************" || ip == "************" {
		return true
	}
	return false
}

// 将角度转换为弧度
func degToRad(deg float64) float64 {
	return deg * math.Pi / 180
}

// DistanceByHaversine 使用Haversine 公式计算两点之间的距离，返回结果单位为公里
func DistanceByHaversine(lat1, lon1, lat2, lon2 float64) float64 {
	// 将经纬度从度转换为弧度
	lat1 = degToRad(lat1)
	lon1 = degToRad(lon1)
	lat2 = degToRad(lat2)
	lon2 = degToRad(lon2)

	// 纬度差和经度差
	dlat := lat2 - lat1
	dlon := lon2 - lon1

	// Haversine 公式
	a := math.Sin(dlat/2)*math.Sin(dlat/2) + math.Cos(lat1)*math.Cos(lat2)*math.Sin(dlon/2)*math.Sin(dlon/2)
	c := 2 * math.Atan2(math.Sqrt(a), math.Sqrt(1-a))

	// 地球半径（单位：公里）
	var r = 6371.0

	// 计算距离
	distance := r * c
	return distance
}

// MonthDate 获取指定月份的第一天和最后一天
func MonthDate(year, month string) (string, string) {
	// 获取当前时间
	now, _ := time.Parse("2006-01", year+"-"+month)

	// 获取当前年份和月份
	currentYear, currentMonth, _ := now.Date()

	// 获取当前月份的第一天
	firstDay := time.Date(currentYear, currentMonth, 1, 0, 0, 0, 0, now.Location())

	// 获取下一个月的第一天，然后减去一天得到当前月份的最后一天
	var lastDay time.Time
	if currentMonth == time.December {
		// 如果是12月，下一个月是下一年的1月
		lastDay = time.Date(currentYear+1, time.January, 1, 0, 0, 0, 0, now.Location()).AddDate(0, 0, -1)
	} else {
		lastDay = time.Date(currentYear, currentMonth+1, 1, 0, 0, 0, 0, now.Location()).AddDate(0, 0, -1)
	}

	return firstDay.Format("2006-01-02"), lastDay.Format("2006-01-02")
}

// PreviousMonthSameDay 获取上个月同一天
func PreviousMonthSameDay(t time.Time) time.Time {
	// 提取当前的年、月、日
	year, month, day := t.Date()

	// 上个月的月份
	lastMonth := month - 1

	// 如果是1月，那么上个月是去年的12月
	if lastMonth == 0 {
		lastMonth = 12
		year--
	}

	// 创建上个月的同一日期
	lastMonthDate := time.Date(year, lastMonth, day, t.Hour(), t.Minute(), t.Second(), t.Nanosecond(), t.Location())

	// 检查上个月是否有相同日期
	if lastMonthDate.Month() != lastMonth {
		// 如果没有，比如 3 月 31 日对应 2 月，使用该月的最后一天
		lastMonthDate = time.Date(year, lastMonth+1, 0, t.Hour(), t.Minute(), t.Second(), t.Nanosecond(), t.Location())
	}

	return lastMonthDate
}

// RoundToDecimal 四舍五入
func RoundToDecimal(value float64, scale int) float64 {
	num := decimal.NewFromFloat(value)
	r, _ := num.Round(int32(scale)).Float64()
	return r
}

func Zip(target, source string) error {
	if !strings.Contains(target, ".zip") {
		return errors.New("target is not a zip file")
	}

	if _, err := os.Stat(source); os.IsNotExist(err) {
		return err
	}

	zipFile, err := os.Create(target)
	if err != nil {
		return err
	}
	defer zipFile.Close()

	archive := zip.NewWriter(zipFile)
	defer archive.Close()

	err = filepath.Walk(source, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 获取相对路径
		relPath, err := filepath.Rel(source, path)
		if err != nil {
			return err
		}

		if info.IsDir() {
			if relPath == "." {
				return nil
			}
			_, err := archive.Create(relPath + "/")
			return err
		}

		// 打开文件
		file, err := os.Open(path)
		if err != nil {
			return err
		}
		defer file.Close()

		// 创建 zip 文件条目
		w, err := archive.Create(relPath)
		if err != nil {
			return err
		}

		_, err = io.Copy(w, file)
		return err
	})

	if err != nil {
		return err
	}
	return nil
}

// GetRandomFromMap 随机从 map 中获取一个键值对
func GetRandomFromMap[K comparable, V any](m map[K]V) (K, V, error) {
	var zeroK K
	var zeroV V
	if len(m) == 0 {
		return zeroK, zeroV, fmt.Errorf("map is empty")
	}
	// 设置随机种子

	// 获取所有键
	keys := make([]K, 0, len(m))
	for k := range m {
		keys = append(keys, k)
	}
	// 随机选择一个键
	randomIndex := rand.IntN(len(keys))
	randomKey := keys[randomIndex]
	return randomKey, m[randomKey], nil
}

// HszyPrimaryID 生成主键ID
func HszyPrimaryID() string {
	return fmt.Sprintf("%d%06d", time.Now().Unix(), rand.IntN(999999))
}
