package model

import "time"

type TmsDriverOilWriteOffRecord struct {
	ID                   string    `gorm:"column:id"`
	CreateBy             string    `gorm:"column:create_by"`
	CreateTime           time.Time `gorm:"column:create_time"`
	UpdateBy             string    `gorm:"column:update_by"`
	UpdateTime           time.Time `gorm:"column:update_time"`
	IsDelete             int       `gorm:"column:is_delete"`
	DriverID             string    `gorm:"column:driver_id"`
	DriverName           string    `gorm:"column:driver_name"`
	VehicleLicenseNumber string    `gorm:"column:vehicle_license_number"`
	CompanyID            string    `gorm:"column:company_id"`
	TotalLiter           float64   `gorm:"column:total_liter"`
	UnitPrice            float64   `gorm:"column:unit_price"`
	TotalPrice           float64   `gorm:"column:total_price"`
	WriteOffTime         time.Time `gorm:"column:write_off_time"`
	Desc                 string    `gorm:"column:desc"`
	CurrentBalance       float64   `gorm:"column:current_balance"`
	TransportationNumber string    `gorm:"column:transportation_number"`
	RecordType           int       `gorm:"column:record_type"`
	DriverType           int       `gorm:"column:driver_type"`
	IsGenerateTaxRecord  int       `gorm:"column:is_generate_tax_record"`
	GasStation           string    `gorm:"column:gas_station"`
	OrderNo              string    `gorm:"column:order_no"`
	WriteOffType         int       `gorm:"column:write_off_type"`
}

func (TmsDriverOilWriteOffRecord) TableName() string {
	return "tms_driver_oil_write_off_record"
}
