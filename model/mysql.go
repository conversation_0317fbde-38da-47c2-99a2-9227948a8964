package model

import (
	"fmt"
	"log"
	"os"
	"time"
	"wlhy/toolbox"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

var DB *gorm.DB

func init() {
	// 根据IP判断使用内网或外网host
	host := "hszywlhy.rwlb.zhangbei.rds.aliyuncs.com"
	if toolbox.IsInternalNetwork() {
		host = "mr-m0h09bph11yn5207cc.rwlb.zhangbei.rds.aliyuncs.com"
	}

	port := 3306
	database := "wlhy"
	user := "wlhy_zsw"
	password := "@fspOm@n1uMMo!TU"
	db, err := NewDB(host, port, database, user, password)
	if err != nil {
		panic(err)
	}
	DB = db
}

func NewDB(host string, port int, database, user, password string) (*gorm.DB, error) {
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&parseTime=True&loc=Local", user, password, host, port, database)

	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		DSN: dsn,
	}), &gorm.Config{
		Logger: logger.New(log.New(os.Stdout, "\r\n", log.LstdFlags), logger.Config{
			SlowThreshold:             1000 * time.Millisecond,
			Colorful:                  true,
			IgnoreRecordNotFoundError: true,
			LogLevel:                  logger.Error,
		}),
	})
	if err != nil {
		return nil, err
	}

	// 设置连接池
	/* db, err := gormDB.DB()
	if err != nil {
		return nil, err
	}
	db.SetMaxIdleConns(10)
	db.SetMaxOpenConns(100)
	db.SetConnMaxLifetime(time.Hour) */

	return gormDB, nil
}

// NewTestDB 创建测试数据库连接
func NewTestDB() (*gorm.DB, error) {
	user := "wlhy_all"
	password := "@6BRQkbEL9zGi@wX"
	host := "rm-8vb3b461pm1299j5s8o.mysql.zhangbei.rds.aliyuncs.com"
	port := 3306
	database := "wlhy_test"
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&parseTime=True&loc=Local", user, password, host, port, database)

	db, err := gorm.Open(mysql.New(mysql.Config{
		DSN: dsn,
	}))
	if err != nil {
		return nil, err
	}

	return db, nil
}
