package model

import "time"

type TmsDriverCompanyOil struct {
	ID         string    `gorm:"column:id"`
	CreateTime time.Time `gorm:"column:create_time"`
	CreateBy   string    `gorm:"column:create_by"`
	Version    int       `gorm:"column:version"`
	VersionNo  int       `gorm:"column:version_no"`
	UpdateBy   string    `gorm:"column:update_by"`
	UpdateTime time.Time `gorm:"column:update_time"`
	IsDelete   int       `gorm:"column:is_delete"`
	DriverID   string    `gorm:"column:driver_id"`
	CompanyID  string    `gorm:"column:company_id"`
	OilBalance float64   `gorm:"column:oil_balance"`
	CardNo     string    `gorm:"column:card_no"`
}

func (TmsDriverCompanyOil) TableName() string {
	return "tms_driver_company_oil"
}
