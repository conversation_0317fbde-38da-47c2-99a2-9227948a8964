package model

import (
	"context"
	"wlhy/toolbox/logger"

	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

var Mongo *mongo.Client

func NewMongo() *mongo.Client {
	uri := "************************************************************"
	client, err := mongo.Connect(context.TODO(), options.Client().
		ApplyURI(uri))
	if err != nil {
		logger.Stdout.Error("mongodb连接失败: " + err.Error())
		return nil
	}
	return client
}
