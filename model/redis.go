package model

import (
	"context"
	"wlhy/toolbox/logger"

	"github.com/redis/go-redis/v9"
)

var Redis *redis.Client

// NewRedis 创建redis连接
func NewRedis(db int) *redis.Client {
	cli := redis.NewClient(&redis.Options{
		Addr:     "r-8vbwk7gyngsozh4ocwpd.redis.zhangbei.rds.aliyuncs.com:16379",
		Username: "hszy",
		Password: "8WGSVPkvGQvgQc@",
		DB:       db,
	})

	err := cli.Ping(context.Background()).Err()
	if err != nil {
		logger.Stdout.Error("redis连接失败: " + err.<PERSON>rror())
	}

	return cli
}
