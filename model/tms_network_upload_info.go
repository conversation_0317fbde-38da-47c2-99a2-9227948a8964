package model

import (
	"time"
)

type TmsNetworkUploadInfo struct {
	ID                     string    `gorm:"column:id"`                        //主键
	SysOrgCode             string    `gorm:"column:sys_org_code"`              //所属公司
	AssociationType        int       `gorm:"column:association_type"`          //关联类型（1-司机上报，2-车辆上报，3-运单上报，4-流水上报，5-运单第二次上报，6-运单第三次上报）
	AssociationID          string    `gorm:"column:association_id"`            //关联主表id
	UploadReportCheckState int       `gorm:"column:upload_report_check_state"` //本地校验状态（0-未校验，1-校验通过，2-校验不通过）
	UploadReportCheckMsg   string    `gorm:"column:upload_report_check_msg"`   //本地校验结果
	UploadReportState      int       `gorm:"column:upload_report_state"`       //监管平台上报状态（0-未上报，1-已上报，2-上报失败）
	UploadReportMsg        string    `gorm:"column:upload_report_msg"`         //监管平台上报结果
	UploadReportSendJson   string    `gorm:"column:upload_report_send_json"`   //监管平台请求报文
	UploadReportReturnJson string    `gorm:"column:upload_report_return_json"` //监管平台返回结果报文
	UploadByTime           time.Time `gorm:"column:upload_by_time"`            //监管平台上报时间
	UploadBy               string    `gorm:"column:upload_by"`                 //监管平台上报人id
	UploadByName           string    `gorm:"column:upload_by_name"`            //监管平台上报人名称
	IsUploadCity           int       `gorm:"column:is_upload_city"`            //是否上传市平台
	UploadCityMsg          string    `gorm:"column:upload_city_msg"`           //市平台上报数据
	UploadCityReturnMsg    string    `gorm:"column:upload_city_return_msg"`    //市平台返回结果报文
	CreateBy               string    `gorm:"column:create_by"`                 //创建人
	CreateTime             time.Time `gorm:"column:create_time"`               //创建时间
	UpdateBy               string    `gorm:"column:update_by"`                 //更新人
	UpdateTime             time.Time `gorm:"column:update_time"`               //更新时间
	IsDelete               int       `gorm:"column:is_delete"`                 //是否删除（0-否，1-是）
}

func (t TmsNetworkUploadInfo) TableName() string {
	return "tms_network_upload_info"
}
