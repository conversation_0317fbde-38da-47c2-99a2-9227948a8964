package model

import "time"

type TmsNetworkUploadInfoRecords struct {
	ID            int       `gorm:"column:id"`
	Category      int       `gorm:"column:category"`
	AssociationID string    `gorm:"column:association_id"`
	Result        string    `gorm:"column:result"`
	Batch         int       `gorm:"column:batch"`
	CreatedAt     time.Time `gorm:"column:created_at"`
	UpdatedAt     time.Time `gorm:"column:updated_at"`
}

func (t TmsNetworkUploadInfoRecords) TableName() string {
	return "tms_network_upload_info_records"
}
