package model

import "time"

type TmsShipperBalance struct {
	ID                  string    `gorm:"column:id"`
	ShipperID           string    `gorm:"column:shipper_id"`
	AccountBalanceLocal float64   `gorm:"column:account_balance_local"`
	CreateBy            string    `gorm:"column:create_by"`
	CreateTime          time.Time `gorm:"column:create_time"`
	UpdateTime          time.Time `gorm:"column:update_time"`
	UpdateBy            string    `gorm:"column:update_by"`
	Version             int       `gorm:"column:version"`
}

func (TmsShipperBalance) TableName() string {
	return "tms_shipper_balance"
}
