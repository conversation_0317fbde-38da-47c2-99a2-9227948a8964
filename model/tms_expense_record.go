package model

import "time"

type TmsExpenseRecord struct {
	ID                         int       `gorm:"column:id"`
	SerialNumber               string    `gorm:"column:serial_number"`                 // 流水号
	PayBatchNumber             string    `gorm:"column:pay_batch_number"`              // 关联支付批次号（或网商支付、提现订单号）
	WaybillID                  string    `gorm:"column:waybill_id"`                    // 关联运输单ID
	WaybillNumber              string    `gorm:"column:waybill_number"`                // 运输单号
	SerialNumberType           int       `gorm:"column:serial_number_type"`            // 流水类型（0-账户余额，1-平台油卡，2-线下油卡，3-线下加油，4-定金金额，5-线下支付）
	ReasonsForChange           int       `gorm:"column:reasons_for_change"`            // 变动原因(0-充值,1-预付,2-运费,3-平台油卡,4-服务费,5-支付定金,6-定金退扣,7-提现,8-保费,9-邮寄费,10-异常罚款,11-轨迹查询, 12-消费,13-油费转入,14-油费转出,15-分账,16-运费转账,17-授权转账,18-线下油卡,19-线下加油,20-回单付,21-购买商品,22,信息费,23,保险费,31,油卡购买，71运费退款,72预支回收,,77,提现手续费 80:立即付，99:资金池转货主）
	TransactionAmount          float64   `gorm:"column:transaction_amount"`            // 交易金额
	AccountBalance             float64   `gorm:"column:account_balance"`               // 账户余额
	RevenueAndExpenditureTypes int       `gorm:"column:revenue_and_expenditure_types"` // 收支类型(0-收入,1-支出)
	AccountType                int       `gorm:"column:account_type"`                  // 账户类型(0-个人货主,1-企业货主,2-司机,3-车队,4-外部商户,5-平台商户)
	AccountName                string    `gorm:"column:account_name"`                  // 账户名
	AccountNumber              string    `gorm:"column:account_number"`                // 帐号
	AccountID                  string    `gorm:"column:account_id"`                    // 账号id
	DriverInformation          string    `gorm:"column:driver_information"`            // 司机信息
	Remark                     string    `gorm:"column:remark"`                        // 备注
	CreateBy                   string    `gorm:"column:create_by"`                     // 创建人
	CreateTime                 time.Time `gorm:"column:create_time"`                   // 创建日期
	ConsumptionTime            time.Time `gorm:"column:consumption_time"`              // 消费时间
	TransactionTime            time.Time `gorm:"column:transaction_time"`              // 交易时间
	TradeStatus                int       `gorm:"column:trade_status"`                  // 交易状态（0-待提交，1-成功，2-失败）
	PaymentMethod              int       `gorm:"column:payment_method"`                // 支付渠道(0-农行支付，1-通联支付，2-网商支付)
	PayType                    int       `gorm:"column:pay_type"`                      // 支付方式（0-余额支付）
	UpdateBy                   string    `gorm:"column:update_by"`                     // 更新人
	UpdateTime                 time.Time `gorm:"column:update_time"`                   // 更新日期
	IsDelete                   int       `gorm:"column:is_delete"`                     // 是否删除（0-否，1-是）
	LineTitle                  string    `gorm:"column:line_title"`                    // 装卸货市县
	SysOrgCode                 string    `gorm:"column:sys_org_code"`                  // 所属部门
	TransferVoucher            string    `gorm:"column:transfer_voucher"`              // 转账凭证
	RechargeType               int       `gorm:"column:recharge_type"`                 // 充值类型(0-默认,1-支付宝,2-微信)
	FuelQuantity               string    `gorm:"column:fuel_quantity"`                 // 加油量
	IsCapitalReporting         int       `gorm:"column:is_capital_reporting"`          // 网络货运 资金流水信息上报:0-未上报，1-已上报，2-上报失败
	CapitalReportingTime       time.Time `gorm:"column:capital_reporting_time"`        // 资金流水上报时间
	CapitalReportingErr        string    `gorm:"column:capital_reporting_err"`         // 资金流水上报失败原因
	IsItVerified               int       `gorm:"column:is_it_verified"`                // 是否校验资金流水信息（0-未校验，1-通过，2-失败）
	VerifiedTime               time.Time `gorm:"column:verified_time"`                 // 资金流水信息检验时间
	VerifiedErr                string    `gorm:"column:verified_err"`                  // 资金流水信息校验异常原因
	ActualAmount               float64   `gorm:"column:actual_amount"`                 // 平台保证金子户余额
	AvailableAmount            float64   `gorm:"column:available_amount"`              // 平台可用子户余额
	PayStatus                  string    `gorm:"column:pay_status"`                    // 支付状态  0：不用处理 1：充值 -1：回收
	DriverID                   string    `gorm:"column:driver_id"`                     // 司机ID
	PaymentProof               string    `gorm:"column:payment_proof"`                 // 支付凭证保存路径
}

func (TmsExpenseRecord) TableName() string {
	return "tms_expense_record"
}
