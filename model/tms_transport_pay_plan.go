package model

import "time"

type TmsTransportPayPlan struct {
	ID                         string    `gorm:"column:id"`                           // 主键id
	Type                       int       `gorm:"column:type"`                         // 支付类型  1-到付 2-预付 3-立即付
	PayType                    int       `gorm:"column:payType"`                      // 银行类型 0：招行 1：农行
	PayOutTradeNo              string    `gorm:"column:pay_out_trade_no"`             // 支付流水号
	State                      int       `gorm:"column:state"`                        // 运费支付状态 0.未支付 1.支付中 2.支付成功 3.支付失败
	Reason                     string    `gorm:"column:reason"`                       // 运费支付失败原因
	TotalAmount                float64   `gorm:"column:total_Amount"`                 // 货主付款总额
	ShipperState               int       `gorm:"column:shipper_state"`                // 货主虚拟余额扣减状态
	ShipperReason              string    `gorm:"column:shipper_reason"`               // 货主余额扣减失败原因
	CashPoolState              int       `gorm:"column:cash_pool_state"`              // 资金池扣减状态
	CashPoolReason             string    `gorm:"column:cash_pool_reason"`             // 资金池划拨异常原因
	OpState                    int       `gorm:"column:op_state"`                     // 平台收款状态 0.未支付  2.支付成功 3.支付失败
	OpReason                   string    `gorm:"column:op_reason"`                    // 平台支付失败原因
	TransportAmount            float64   `gorm:"column:transport_Amount"`             // 司机收款运费
	TransportState             int       `gorm:"column:transport_state"`              // 运费支付状态 0.未支付  2.支付成功 3.支付失败
	TransportReason            string    `gorm:"column:transport_reason"`             // 运费支付失败原因
	ImmediatelyAmount          float64   `gorm:"column:immediately_Amount"`           // 立即付金额
	ImmediatelyState           int       `gorm:"column:immediately_state"`            // 立即付金额支付状态 0.未支付  2.支付成功 3.支付失败
	ImmediatelyReason          string    `gorm:"column:immediately_reason"`           // 立即付金额支付失败原因
	ImmediatelyCostAmount      float64   `gorm:"column:immediately_cost_Amount"`      // 立即付费用金额
	ImmediatelyCostState       int       `gorm:"column:immediately_cost_state"`       // 立即付费用支付状态 0.未支付  2.支付成功 3.支付失败
	ImmediatelyCostReason      string    `gorm:"column:immediately_cost_reason"`      // 立即付费用支付失败原因
	ImmediatelyExceptionAmount float64   `gorm:"column:immediately_exception_Amount"` // 立即付异常回款金额
	ImmediatelyExceptionState  int       `gorm:"column:immediately_exception_state"`  // 立即付异常回款状态 0.未支付  2.支付成功 3.支付失败
	ImmediatelyExceptionReason string    `gorm:"column:immediately_exception_reason"` // 立即付异常回款失败原因
	ServiceRate                float64   `gorm:"column:service_Rate"`                 // 服务费率
	ServiceAmount              float64   `gorm:"column:service_Amount"`               // 服务费
	ServiceState               int       `gorm:"column:service_state"`                // 服务费支付状态 0.未支付 1.支付中 2.支付成功 3.支付失败
	ServiceReason              string    `gorm:"column:service_reason"`               // 服务费支付失败原因
	IsInsurance                int       `gorm:"column:is_insurance"`                 // 是否购买保险
	InsuranceAmount            float64   `gorm:"column:insurance_Amount"`             // 保险费
	InsuranceState             int       `gorm:"column:insurance_state"`              // 保险费支付状态 0.未支付 1.支付中 2.支付成功 3.支付失败
	InsuranceReason            string    `gorm:"column:insurance_reason"`             // 保险费支付失败原因
	CommissionAmount           float64   `gorm:"column:commission_Amount"`            // 手续费金额
	CommissionState            int       `gorm:"column:commission_state"`             // 手续费支付状态 0.未支付 1.支付中 2.支付成功 3.支付失败
	CommissionReason           string    `gorm:"column:commission_reason"`            // 手续费支付失败原因
	IsUseOil                   int       `gorm:"column:is_use_oil"`                   // 是否使用了电子油卡，若对应的货源单填写了电子油卡比例则为1，否则为0
	OilAmount                  float64   `gorm:"column:oil_amount"`                   // 油卡金额：付给司机的金额
	PlatformProfit             float64   `gorm:"column:platform_profit"`              // 电子油卡平台利润
	PlatformProfitState        int       `gorm:"column:platform_profit_state"`        // 平台利润支付状态 0.未支付 2.支付成功 3.支付失败
	PlatformProfitReason       string    `gorm:"column:platform_profit_reason"`       // 平台利润支付失败原因
	ShipperProfit              float64   `gorm:"column:shipper_profit"`               // 电子油卡货主利润
	ShipperProfitState         int       `gorm:"column:shipper_profit_state"`         // 货主利润支付状态 0.未支付 2.支付成功 3.支付失败
	ShipperProfitReason        string    `gorm:"column:shipper_profit_reason"`        // 货主利润支付失败原因
	ThirdpartyProfit           float64   `gorm:"column:thirdparty_profit"`            // 电子油卡第三方利润
	ThirdpartyProfitState      int       `gorm:"column:thirdparty_profit_state"`      // 第三方利润支付状态 0.未支付 2.支付成功 3.支付失败
	ThirdpartyProfitReason     string    `gorm:"column:thirdparty_profit_reason"`     // 第三方利润支付失败原因
	CostProfit                 float64   `gorm:"column:cost_profit"`                  // 电子油卡成本
	CostProfitState            int       `gorm:"column:cost_profit_state"`            // 成本支付状态 0.未支付 2.支付成功 3.支付失败
	CostProfitReason           string    `gorm:"column:cost_profit_reason"`           // 成本支付失败原因
	TransportationID           string    `gorm:"column:transportation_id"`            // 运单id
	WaybillNo                  string    `gorm:"column:waybill_No"`                   // 运单号
	ShipperID                  string    `gorm:"column:shipper_id"`                   // 货主ID
	CompanyNo                  string    `gorm:"column:company_no"`                   // 平台公司 id
	IsOneself                  int       `gorm:"column:is_oneself"`                   // 是否代收
	ShipperPayMem              string    `gorm:"column:shipper_pay_mem"`              // 货主付款编号
	ReceiveMem                 string    `gorm:"column:receive_mem"`                  // 收款人编号
	BankCardNumber             string    `gorm:"column:bank_card_number"`             // 提现银行卡号
	WithdrawalStatus           int       `gorm:"column:withdrawal_status"`            // 提现状态 0未体现 2.提现成功 3.提现失败
	CreateBy                   string    `gorm:"column:create_by"`                    // 创建人
	CreateTime                 time.Time `gorm:"column:create_time"`                  // 创建日期
	UpdateBy                   string    `gorm:"column:update_by"`                    // 更新人
	UpdateByName               string    `gorm:"column:update_by_name"`               // 最后操作人姓名
	UpdateTime                 time.Time `gorm:"column:update_time"`                  // 更新日期
	IsDelete                   int       `gorm:"column:is_delete"`                    // 是否删除
	ShipperPayTime             time.Time `gorm:"column:shipper_pay_time"`             // 货主付款时间
	ShipperAccBalance          float64   `gorm:"column:shipper_acc_balance"`          // 货主余额
	IsRepay                    int       `gorm:"column:is_repay"`                     // 是否为需要重新支付，0-否；1-是。当值为1时对应的支付计划的金额不参与余额校验
}

func (TmsTransportPayPlan) TableName() string {
	return "tms_transport_pay_plan"
}
