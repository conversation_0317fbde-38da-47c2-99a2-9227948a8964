package esign

import (
	"bytes"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"strings"
	"time"

	"github.com/google/uuid"
)

var BaseUrl = "https://openapi.esign.cn"
var AppID = "5111876988"
var Secret = "e5e64583355768c74388f37cbdb7a293"

// 身份证OCR识别
//
// @Description:  Identity verification by uploading id card image
//
// @param image the base64 encoded image of id card
// @param category 1: 个人信息页, 2: 国徽页
//
// @return void
func OcrIdCard(image string, category int) {
	httpMethod := "POST"
	serviceApi := "/v2/identity/auth/api/ocr/idcard"
	sign, err := signature(httpMethod, serviceApi)
	if err != nil {
		fmt.Println(err)
	}

	i, _ := encodeImageToBase64(image)
	m := make(map[string]string)
	if category == 1 {
		m["infoImg"] = i
	} else {
		m["emblemImg"] = i
	}
	body, _ := json.Marshal(m)

	req, err := http.NewRequest(httpMethod, BaseUrl+serviceApi, bytes.NewBuffer(body))
	if err != nil {
		fmt.Println(err)
	}
	req.Header.Set("X-Tsign-Open-App-Id", AppID)
	req.Header.Set("Content-Type", "application/json;charset=UTF-8")
	req.Header.Set("X-Tsign-Open-Ca-Timestamp", fmt.Sprintf("%d", time.Now().UnixMilli()))
	req.Header.Set("Accept", "*/*")
	req.Header.Set("X-Tsign-Open-Ca-Signature", sign)
	req.Header.Set("X-Tsign-Open-Auth-Mode", "Signature")

	client := http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		fmt.Println(err)
	}
	defer resp.Body.Close()

	content, _ := io.ReadAll(resp.Body)
	fmt.Println(string(content))
}

// OcrBankCard 银行卡OCR识别
//
// @Description:  Bank card verification by uploading bank card image
//
// @param image the base64 encoded image of bank card
//
// @return void
func OcrBankCard(image string) {
	httpMethod := "POST"
	serviceApi := "/v2/identity/auth/api/ocr/bankcard"
	sign, err := signature(httpMethod, serviceApi)
	if err != nil {
		fmt.Println(err)
	}

	i, _ := encodeImageToBase64(image)
	body, _ := json.Marshal(map[string]string{
		"img": i,
	})

	req, err := http.NewRequest(httpMethod, BaseUrl+serviceApi, bytes.NewBuffer(body))
	if err != nil {
		fmt.Println(err)
	}
	req.Header.Set("X-Tsign-Open-App-Id", AppID)
	req.Header.Set("Content-Type", "application/json;charset=UTF-8")
	req.Header.Set("X-Tsign-Open-Ca-Timestamp", fmt.Sprintf("%d", time.Now().UnixMilli()))
	req.Header.Set("Accept", "*/*")
	req.Header.Set("X-Tsign-Open-Ca-Signature", sign)
	req.Header.Set("X-Tsign-Open-Auth-Mode", "Signature")

	client := http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		fmt.Println(err)
	}
	defer resp.Body.Close()

	content, _ := io.ReadAll(resp.Body)
	fmt.Println(string(content))
}

// OcrLicense 营业执照OCR识别
//
// @Description:  Business license verification by uploading business license image
//
// @param image the base64 encoded image of business license
func OcrLicense(image string) {
	httpMethod := "POST"
	serviceApi := "/v2/identity/auth/api/ocr/license"
	sign, err := signature(httpMethod, serviceApi)
	if err != nil {
		fmt.Println(err)
	}

	i, _ := encodeImageToBase64(image)
	body, _ := json.Marshal(map[string]string{
		"img": i,
	})

	req, err := http.NewRequest(httpMethod, BaseUrl+serviceApi, bytes.NewBuffer(body))
	if err != nil {
		fmt.Println(err)
	}
	req.Header.Set("X-Tsign-Open-App-Id", AppID)
	req.Header.Set("Content-Type", "application/json;charset=UTF-8")
	req.Header.Set("X-Tsign-Open-Ca-Timestamp", fmt.Sprintf("%d", time.Now().UnixMilli()))
	req.Header.Set("Accept", "*/*")
	req.Header.Set("X-Tsign-Open-Ca-Signature", sign)
	req.Header.Set("X-Tsign-Open-Auth-Mode", "Signature")

	client := http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		fmt.Println(err)
	}
	defer resp.Body.Close()

	content, _ := io.ReadAll(resp.Body)
	fmt.Println(string(content))
}

// OcrDrivingLicence 驾驶证OCR识别
//
// @Description:  Driving license verification by uploading driving license image
//
// @param image the base64 encoded image of driving license
// @param backImage the base64 encoded image of driving license back
func OcrDrivingLicence(image, backImage string) {
	httpMethod := "POST"
	serviceApi := "/v2/identity/auth/api/ocr/drivinglicence"
	sign, err := signature(httpMethod, serviceApi)
	if err != nil {
		fmt.Println(err)
	}

	i1, _ := encodeImageToBase64(image)
	i2, _ := encodeImageToBase64(backImage)
	body, _ := json.Marshal(map[string]string{
		"image":     i1,
		"backImage": i2,
		"requestId": uuid.NewString(),
	})

	req, err := http.NewRequest(httpMethod, BaseUrl+serviceApi, bytes.NewBuffer(body))
	if err != nil {
		fmt.Println(err)
	}
	req.Header.Set("X-Tsign-Open-App-Id", AppID)
	req.Header.Set("Content-Type", "application/json;charset=UTF-8")
	req.Header.Set("X-Tsign-Open-Ca-Timestamp", fmt.Sprintf("%d", time.Now().UnixMilli()))
	req.Header.Set("Accept", "*/*")
	req.Header.Set("X-Tsign-Open-Ca-Signature", sign)
	req.Header.Set("X-Tsign-Open-Auth-Mode", "Signature")

	client := http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		fmt.Println(err)
	}
	defer resp.Body.Close()

	content, _ := io.ReadAll(resp.Body)
	fmt.Println(string(content))
}

// OcrDrivingPermit 行驶证OCR识别
//
// @Description:  Driving license verification by uploading driving license image
//
// @param image the base64 encoded image of driving license
// @param backImage the base64 encoded image of driving license back
func OcrDrivingPermit(image, backImage string) {
	httpMethod := "POST"
	serviceApi := "/v2/identity/auth/api/ocr/drivingPermit"
	sign, err := signature(httpMethod, serviceApi)
	if err != nil {
		fmt.Println(err)
	}

	i1, _ := encodeImageToBase64(image)
	i2, _ := encodeImageToBase64(backImage)
	body, _ := json.Marshal(map[string]string{
		"image":     i1,
		"backImage": i2,
		"requestId": uuid.NewString(),
	})

	req, err := http.NewRequest(httpMethod, BaseUrl+serviceApi, bytes.NewBuffer(body))
	if err != nil {
		fmt.Println(err)
	}
	req.Header.Set("X-Tsign-Open-App-Id", AppID)
	req.Header.Set("Content-Type", "application/json;charset=UTF-8")
	req.Header.Set("X-Tsign-Open-Ca-Timestamp", fmt.Sprintf("%d", time.Now().UnixMilli()))
	req.Header.Set("Accept", "*/*")
	req.Header.Set("X-Tsign-Open-Ca-Signature", sign)
	req.Header.Set("X-Tsign-Open-Auth-Mode", "Signature")

	client := http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		fmt.Println(err)
	}
	defer resp.Body.Close()

	content, _ := io.ReadAll(resp.Body)
	fmt.Println(string(content))
}

// signature 签名
func signature(httpMethod, pathAndParameters string) (string, error) {
	httpMethod = strings.ToUpper(httpMethod)
	accepct := "*/*"
	headers := ""
	contentMd5 := ""
	contentType := "application/json;charset=UTF-8"
	Date := ""

	s := httpMethod + "\n" + accepct + "\n" + contentMd5 + "\n" + contentType + "\n" + Date + "\n" + headers + pathAndParameters

	// 创建一个新的HMAC对象，使用SHA256算法
	h := hmac.New(sha256.New, []byte(Secret))

	// 写入消息数据
	_, err := h.Write([]byte(s))
	if err != nil {
		return "", fmt.Errorf("failed to write message to HMAC: %v", err)
	}

	// 计算HMAC摘要
	digest := h.Sum(nil)

	// 使用Base64编码摘要
	return base64.StdEncoding.EncodeToString(digest), nil
}

// encodeImageToBase64 将图片文件转换为Base64字符串
func encodeImageToBase64(imagePath string) (string, error) {
	// 读取图片文件为字节数组
	imageBytes, err := os.ReadFile(imagePath)
	if err != nil {
		return "", fmt.Errorf("failed to read image file: %v", err)
	}

	// 将图片字节数组编码为Base64字符串
	base64Encoded := base64.StdEncoding.EncodeToString(imageBytes)
	return base64Encoded, nil
}
