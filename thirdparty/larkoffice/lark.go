package larkoffice

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"path/filepath"
	"time"

	lark "github.com/larksuite/oapi-sdk-go/v3"
	larkcore "github.com/larksuite/oapi-sdk-go/v3/core"
	larkim "github.com/larksuite/oapi-sdk-go/v3/service/im/v1"
)

const (
	AppID     = "cli_a8cd55edd070900c"
	AppSecret = "hMLXk28fpaWuJz1hzdKnCbfHsN4C1j5t"
)

// LarkUserID 飞书用户ID
type LarkUserID struct {
	ZhaoShengWei string
	ZhaoChenYing string
	WangPeng     string
	YuLei        string
	ShanJingBo   string
	WangBingXin  string
	YuHongCen    string
	DeJiaNing    string
	ChaiRuiPeng  string
	MengFanXue   string
	XiaoYiYing   string
	LunXue       string
}

// LarkOffice 飞书机器人
type LarkOffice struct {
	client     *lark.Client
	ReceivesID LarkUserID
}

// NewLarkOffice 创建飞书机器人客户端
func NewLarkOffice() *LarkOffice {
	var client = lark.NewClient(
		AppID,
		AppSecret, // 默认配置为自建应用
		lark.WithLogLevel(larkcore.LogLevelDebug),
		lark.WithReqTimeout(3*time.Second),
		lark.WithEnableTokenCache(true),
		lark.WithHelpdeskCredential("id", "token"),
		lark.WithHttpClient(http.DefaultClient),
	)
	return &LarkOffice{
		client: client,
		ReceivesID: LarkUserID{
			ZhaoShengWei: "c7c7gc12",
			ZhaoChenYing: "fg949bc5",
			WangPeng:     "8bge1b7d",
			YuLei:        "9ecb5f98",
			ShanJingBo:   "ee126625",
			WangBingXin:  "13e587d2",
			YuHongCen:    "abf49g4d",
			DeJiaNing:    "b84cdg3a",
			ChaiRuiPeng:  "1cag11e3",
			MengFanXue:   "68638572",
			XiaoYiYing:   "bfd97g2b",
			LunXue:       "7c692c5e",
		},
	}
}

// UploadXlsFile 上传Excel文件
func (l *LarkOffice) UploadXlsFile(filename string) (string, error) {
	f, err := os.Open(filename)
	if err != nil {
		return "", err
	}
	defer f.Close()

	// 创建请求对象
	req := larkim.NewCreateFileReqBuilder().
		Body(larkim.NewCreateFileReqBodyBuilder().
			FileType(`xls`).
			FileName(filepath.Base(filename)).
			File(f).
			Build()).
		Build()

	// 发起请求
	resp, err := l.client.Im.V1.File.Create(context.Background(), req)
	if err != nil {
		return "", err
	}

	// 服务端错误处理
	if !resp.Success() {
		return "", fmt.Errorf("logId: %s, error response: \n%s", resp.RequestId(), larkcore.Prettify(resp.CodeError))
	}

	return *resp.Data.FileKey, nil
}

// SendFileMessage 发送文件消息
func (l *LarkOffice) SendFileMessage(receiveID, fileKey string) error {
	// 创建请求对象
	req := larkim.NewCreateMessageReqBuilder().
		ReceiveIdType(`user_id`).
		Body(larkim.NewCreateMessageReqBodyBuilder().
			ReceiveId(receiveID).
			MsgType(`file`).
			Content(`{"file_key":"` + fileKey + `"}`).
			Build()).
		Build()

	// 发起请求
	resp, err := l.client.Im.V1.Message.Create(context.Background(), req)

	// 处理错误
	if err != nil {
		return err
	}

	// 服务端错误处理
	if !resp.Success() {
		return fmt.Errorf("logId: %s, error response: \n%s", resp.RequestId(), larkcore.Prettify(resp.CodeError))
	}
	return nil
}

// SendTextMessage 发送文本消息
func (l *LarkOffice) SendTextMessage(receiveID, content string) error {
	// 创建请求对象
	textContent := map[string]string{
		"text": content,
	}
	textContentJson, _ := json.Marshal(textContent)

	req := larkim.NewCreateMessageReqBuilder().
		ReceiveIdType(`user_id`).
		Body(larkim.NewCreateMessageReqBodyBuilder().
			ReceiveId(receiveID).
			MsgType(`text`).
			Content(string(textContentJson)).
			Build()).
		Build()

	// 发起请求
	resp, err := l.client.Im.V1.Message.Create(context.Background(), req)

	// 处理错误
	if err != nil {
		return err
	}

	// 服务端错误处理
	if !resp.Success() {
		return fmt.Errorf("logId: %s, error response: \n%s", resp.RequestId(), larkcore.Prettify(resp.CodeError))
	}
	return nil
}
