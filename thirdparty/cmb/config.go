package cmb

import "wlhy/toolbox/logger"

func GetTest() *CmbBank {
	return &CmbBank{
		ApiUrl:     "http://cdctest.cmburl.cn/cdcserver/api/v2",
		Accnbr:     "***************",
		UID:        "U006562723",
		Bbknbr:     "41",
		Sm4Key:     "VuAzSWQhsoNqzn0K",
		PrivateKey: "NBtl7WnuUtA2v5FaebEkU0/Jj1IodLGT6lQqwkzmd2E=",
		PublicKey:  "BNsIe9U0x8IeSe4h/dxUzVEz9pie0hDSfMRINRXc7s1UIXfkExnYECF4QqJ2SnHxLv3z/99gsfDQrQ6dzN5lZj0=",
		BusMod: map[string]string{
			"NTDMADLT": "S400F", // 子单元关闭
			"NTDMAMNT": "S400F", // 子单元更新
			"NTDMAADD": "S400F", // 子单元新增
			"NTDMATRX": "S400F", // 子单元内部转账
			"BB6BTHHL": "S4014", // 超网代发
			"BB1PAYOP": "S4018", // 支付转账
			"BB1PAYQR": "S4018", // 查询支付转账结果
			"BB1PAYBH": "S4018", // 批量支付转账
			"BB1QRYBT": "S4018", // 批量支付转账查询
			"BB1QRYBD": "S4018", // 批量支付转账明细查询
			// S400B 赤峰现代智慧物流有限公司 支付转账 模式名称
		},
		Logger: logger.NewZerolog("cmb"),
	}
}

func GetYbs() *CmbBank {
	return &CmbBank{
		ApiUrl:     "https://cdc.cmbchina.com/cdcserver/api/v2",
		Accnbr:     "***************",
		UID:        "U028913611",
		Bbknbr:     "41",
		Sm4Key:     "Cy2JCWXrLQV1aQkH",
		PrivateKey: "2DO2D+bN/IVluAi0DYNLNCxrKGBUkZMMDO2bIvjBrOU=",
		PublicKey:  "BOCefgPuab08voi9y4DEBEawZyCkCC8gtLQHUh7HoF68AP8dR2E/7vWBH6UrhHTNp97I6c82Z1X7ok5cF2cb6mo=",
		BusMod: map[string]string{
			"NTDMADLT": "S4010", // 子单元关闭
			"NTDMAMNT": "S4010", // 子单元更新
			"NTDMAADD": "S4010", // 子单元新增
			"NTDMATRX": "S4010", // 子单元内部转账
			"BB6BTHHL": "D001E", // 超网代发
			"BB1PAYOP": "D001E", // 支付转账
			"BB1PAYQR": "D001E", // 查询支付转账结果
			"BB1PAYBH": "D001E", // 批量支付转账
			"BB1QRYBT": "D001E", // 批量支付转账查询
			"BB1QRYBD": "D001E", // 批量支付转账明细查询
		},
		Logger: logger.NewZerolog("cmb"),
	}
}

func GetHszy() *CmbBank {
	return &CmbBank{
		ApiUrl:     "https://cdc.cmbchina.com/cdcserver/api/v2",
		Accnbr:     "***************",
		UID:        "U028913611",
		Bbknbr:     "41",
		Sm4Key:     "Cy2JCWXrLQV1aQkH",
		PrivateKey: "2DO2D+bN/IVluAi0DYNLNCxrKGBUkZMMDO2bIvjBrOU=",
		PublicKey:  "BOCefgPuab08voi9y4DEBEawZyCkCC8gtLQHUh7HoF68AP8dR2E/7vWBH6UrhHTNp97I6c82Z1X7ok5cF2cb6mo=",
		BusMod: map[string]string{
			"NTDMADLT": "S4010", // 子单元关闭
			"NTDMAMNT": "S4010", // 子单元更新
			"NTDMAADD": "S4010", // 子单元新增
			"NTDMATRX": "S4010", // 子单元内部转账
			"BB6BTHHL": "D001E", // 超网代发
			"BB1PAYOP": "D001E", // 支付转账
			"BB1PAYQR": "D001E", // 查询支付转账结果
			"BB1PAYBH": "D001E", // 批量支付转账
			"BB1QRYBT": "D001E", // 批量支付转账查询
			"BB1QRYBD": "D001E", // 批量支付转账明细查询
		},
		Logger: logger.NewZerolog("cmb"),
	}
}
