package cmb

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"
)

// Signature 请求报文和响应结果的签名
type signature struct {
	Sigdat string `json:"sigdat"` // 签名字符串
	Sigtim string `json:"sigtim"` // 签名时间
}

// ReqHead 请求报文head
type reqHead struct {
	Funcode string `json:"funcode"` // 功能码
	UserID  string `json:"userid"`  // 用户ID
	ReqID   string `json:"reqid"`   // 请求ID
}

// ReqRequest 请求报文
type reqRequest struct {
	Head reqHead        `json:"head"` // 报文请求head
	Body map[string]any `json:"body"` // 报文请求body
}

// BankReqT 请求报文
type bankReqT struct {
	Request   reqRequest `json:"request"`   // 请求体
	Signature signature  `json:"signature"` // 签名
}

// RespHead 响应报文head
type respHead struct {
	Bizcode    string `json:"bizcode"`    // 业务码
	Funcode    string `json:"funcode"`    // 功能码
	ReqID      string `json:"reqid"`      // 请求ID
	RspID      string `json:"rspid"`      // 响应ID
	ResultCode string `json:"resultcode"` // 结果码
	ResultMsg  string `json:"resultmsg"`  // 结果信息
	UserID     string `json:"userid"`     // 用户ID
}

// RespResponse 响应报文
type respResponse struct {
	Head respHead       `json:"head"` // 响应结果head
	Body map[string]any `json:"body"` // 响应结果body
}

// BankRespT 响应报文
type bankRespT struct {
	Response  respResponse `json:"response"`  // 响应体
	Signature signature    `json:"signature"` // 签名
}

// HttpCall 发送HTTP请求
//
// 参数:
//   - fucode 功能码
//   - requestBody 请求体
func (c *CmbBank) HttpCall(fucode string, requestBody any) (bankRespT, error) {
	// 将请求体转换为 JSON 并按照ascii排序
	reqJson, _ := json.Marshal(requestBody)
	reqJson, err := sortedByAscii(reqJson)
	if err != nil {
		return bankRespT{}, err
	}

	sign, err := sm2Sign(c.PrivateKey, c.UID, reqJson)
	if err != nil {
		return bankRespT{}, err
	}

	reqJson = bytes.ReplaceAll(reqJson, []byte("__signature_sigdat__"), sign)

	// reqJsonStr := strings.ReplaceAll(string(reqJson), "__signature_sigdat__", sign)
	// fmt.Printf("请求报文：%s\n", string(reqJson))

	// 对请求体进行SM4加密
	reqEncrypt, err := sm4Encrypt(c.Sm4Key, c.UID, reqJson)
	if err != nil {
		return bankRespT{}, err
	}
	// fmt.Printf("加密后请求报文：%s\n", reqEncrypt)

	// 构建表单数据
	bodyData := map[string]string{
		"UID":     c.UID,
		"FUNCODE": fucode,
		"ALG":     "SM",
		"DATA":    reqEncrypt,
	}

	// 创建HTTP客户端并发送请求
	client := &http.Client{
		Timeout: time.Second * 10, // 设置超时
	}

	// 将map转换为字符串
	var values []string
	for key, value := range bodyData {
		values = append(values, fmt.Sprintf("%s=%s", key, url.QueryEscape(value)))
	}
	dataString := strings.Join(values, "&")

	// 创建新的POST请求
	req, err := http.NewRequest("POST", c.ApiUrl, bytes.NewBufferString(dataString))
	if err != nil {
		return bankRespT{}, err
	}
	// 设置请求头
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		return bankRespT{}, fmt.Errorf("error sending request: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		// fmt.Println("HTTP状态码异常:", resp.Status)
		respBody, _ := io.ReadAll(resp.Body)
		return bankRespT{}, fmt.Errorf("error response: %v", string(respBody))
	}

	// 读取响应体
	respBody, _ := io.ReadAll(resp.Body)
	// fmt.Printf("原始响应结果：%s\n", string(respBody))

	respContent, err := sm4Decrypt(c.Sm4Key, c.UID, respBody)
	if err != nil {
		return bankRespT{}, fmt.Errorf("error decrypting response: %v", err)
	}
	// fmt.Printf("解密后响应结果：%s\n", string(respContent))

	// 解析响应结果
	var result bankRespT
	if err := json.Unmarshal(respContent, &result); err != nil {
		return bankRespT{}, fmt.Errorf("error parsing response: %v", err)
	}

	if result.Response.Head.ResultCode != "SUC0000" {
		return bankRespT{}, fmt.Errorf("招行响应结果失败(非SUC0000): %s, 错误信息: %s", result.Response.Head.ResultCode, result.Response.Head.ResultMsg)
	}

	// 验证响应结果签名
	/* verify, err := sm2Verify(c.PublicKey, c.UID, result, respContent)
	if err != nil {
		return bankRespT{}, err
	}
	if !verify {
		return bankRespT{}, fmt.Errorf("招行响应结果签名验证失败: %s", result.Signature.Sigdat)
	} else {
		fmt.Println("招行响应结果签名验证成功")
	} */

	return result, nil
}
