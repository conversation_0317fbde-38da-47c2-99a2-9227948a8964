package cmb

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"time"
)

// TransactionNumber 查询交易流水号
//
// 该方法构建并发送一个请求，以查询给定日期范围内的交易信息。
// 若响应中指出查询未完成，则会递归调用自身继续查询。
// 初次调用时传入空字符串作为queryAcctNbr参数，传入nil作为TRANSQUERYBYBREAKPOINT_Y1参数。
//
// 参数:
//   - beginDate: 查询开始日期，格式为YYYYMMDD
//   - endDate: 查询结束日期，格式为YYYYMMDD
//   - queryAcctNbr: 查询账户编号
//   - TRANSQUERYBYBREAKPOINT_Y1: 查询断点信息，若不为nil，则会被包含在请求体中
func (c *CmbBank) TransactionNumber(beginDate, endDate string, queryAcctNbr string, TRANSQUERYBYBREAKPOINT_Y1 map[string]any) (TransactionNumberBody, error) {
	fucode := "trsQryByBreakPoint"

	// 构建请求体
	requestBody := bankReqT{
		Request: reqRequest{
			Head: reqHead{
				Funcode: fucode,
				UserID:  c.UID,
				ReqID:   ReqID(),
			},
			Body: map[string]any{
				"TRANSQUERYBYBREAKPOINT_X1": []map[string]any{
					{
						"cardNbr":             c.Accnbr,
						"beginDate":           beginDate,
						"endDate":             endDate,
						"transactionSequence": "1",
						"currencyCode":        "10",
						"queryAcctNbr":        queryAcctNbr,
					},
				},
			},
		},
		Signature: signature{
			Sigdat: "__signature_sigdat__",
			Sigtim: time.Now().Format("**************"),
		},
	}

	if TRANSQUERYBYBREAKPOINT_Y1 != nil {
		requestBody.Request.Body["TRANSQUERYBYBREAKPOINT_Y1"] = TRANSQUERYBYBREAKPOINT_Y1
	}

	// 发送HTTP请求
	result, err := c.HttpCall(fucode, requestBody)
	if err != nil {
		return TransactionNumberBody{}, fmt.Errorf("error sending request: %v", err)
	}

	// 序列化json并反序列化至响应body结构体
	tmpJson, err := json.Marshal(result.Response.Body)
	if err != nil {
		return TransactionNumberBody{}, fmt.Errorf("error parsing response: %v", err)
	}

	var body TransactionNumberBody
	if err := json.Unmarshal(tmpJson, &body); err != nil {
		return TransactionNumberBody{}, fmt.Errorf("error parsing response: %v", err)
	}

	// 打印日志
	c.Logger.Info().Interface("requestBody", requestBody).Msg("请求体")
	c.Logger.Info().Interface("result", result).Msg("结果")
	c.Logger.Info().Interface("body", body).Msg("body")

	for _, v := range body.TRANSQUERYBYBREAKPOINT_Z2 {
		c.Logger.Info().Interface("v", v).Msg(fmt.Sprintf("记账子单元: %s, 业务参考号: %s, 流水号: %s\n", v.VirtualNbr, v.YurRef, v.TransSequenceIdn))
	}

	// 处理TRANSQUERYBYBREAKPOINT_Y1，若未查询完则继续查询
	if body.TRANSQUERYBYBREAKPOINT_Z1[0].CtnFlag == "Y" {
		TRANSQUERYBYBREAKPOINT_Y1Json, err := json.Marshal(body.TRANSQUERYBYBREAKPOINT_Y1)
		if err != nil {
			return TransactionNumberBody{}, fmt.Errorf("error parsing response: %v", err)
		}
		var TRANSQUERYBYBREAKPOINT_Y1Map map[string]any
		if err := json.Unmarshal(TRANSQUERYBYBREAKPOINT_Y1Json, &TRANSQUERYBYBREAKPOINT_Y1Map); err != nil {
			return TransactionNumberBody{}, fmt.Errorf("error parsing response: %v", err)
		}

		c.TransactionNumber(beginDate, endDate, body.TRANSQUERYBYBREAKPOINT_Z1[0].QueryAcctNbr, TRANSQUERYBYBREAKPOINT_Y1Map)
	}

	return body, nil
}

// ElectronicReceipt 通过流水号和记账日期查询电子回单
//
// 参数:
//   - queryDate 记账日期，YYYY-MM-DD
//   - transactionNumber 流水号
func (c *CmbBank) ElectronicReceipt(queryDate, transactionNumber string) ([]byte, error) {
	fucode := "DCSIGREC"

	// 构建请求体
	requestBody := bankReqT{
		Request: reqRequest{
			Head: reqHead{
				Funcode: fucode,
				UserID:  c.UID,
				ReqID:   ReqID(),
			},
			Body: map[string]any{
				"eacnbr": c.Accnbr,
				"quedat": queryDate,
				"trsseq": transactionNumber,
			},
		},
		Signature: signature{
			Sigdat: "__signature_sigdat__",
			Sigtim: time.Now().Format("**************"),
		},
	}

	// 发送HTTP请求
	result, err := c.HttpCall(fucode, requestBody)
	if err != nil {
		return nil, fmt.Errorf("error sending request: %v", err)
	}

	// 序列化json并反序列化至响应body结构体
	tmpJson, err := json.Marshal(result.Response.Body)
	if err != nil {
		return nil, fmt.Errorf("error parsing response: %v", err)
	}

	var body ElectronicReceiptBody
	if err := json.Unmarshal(tmpJson, &body); err != nil {
		return nil, fmt.Errorf("error parsing response: %v", err)
	}

	// 打印日志
	c.Logger.Info().Interface("requestBody", requestBody).Msg("请求体")
	c.Logger.Info().Interface("result", result).Msg("结果")
	c.Logger.Info().Interface("body", body).Msg("body")

	// 保存base64编码后的文件
	decodedBytes, err := base64.StdEncoding.DecodeString(body.Fildat)
	if err != nil {
		return nil, fmt.Errorf("error parsing response: %v", err)
	}

	// 保存文件
	/* err = os.WriteFile("receipt.pdf", decodedBytes, 0644)
	if err != nil {
		return nil, fmt.Errorf("error saving file: %v", err)
	} */

	return decodedBytes, nil
}
