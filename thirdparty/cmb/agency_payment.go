package cmb

import (
	"encoding/json"
	"fmt"
	"math/rand/v2"
	"strconv"
	"strings"
	"time"
)

// AgencyPayment 超网代发
//
// 参数:
//   - payeeName 收款人姓名
//   - payeeBankCard 收款人银行卡号
//   - amount 金额
//   - payeeSubAccount 付款子单元
//   - remark 交易备注
func (c *CmbBank) AgencyPayment(payeeName, payeeBankCard, amount, payeeSubAccount, remark string) (AgencyPaymentBody, error) {
	fucode := "BB6BTHHL"

	// 构建请求体
	requestBody := bankReqT{
		Request: reqRequest{
			Head: reqHead{
				Funcode: fucode,
				UserID:  c.UID,
				ReqID:   ReqID(),
			},
			Body: map[string]any{
				"bb6busmod": []map[string]any{
					{
						"buscod": "N03020",
						"busmod": c.BusMod[fucode],
					},
				},
				"bb6cdcbhx1": []map[string]any{
					{
						"bbknbr": c.Bbknbr,
						"begtag": "Y",
						"endtag": "Y",
						"accnbr": c.Accnbr,
						"ttlamt": amount, // 总金额
						"ttlcnt": "1",
						"ttlnum": "1",
						"curamt": amount, // 本次金额
						"curcnt": "1",
						"ccynbr": "10",
						"ntfinf": remark,
						"trstyp": "BYBK",
						"nusage": remark,
						"eptdat": time.Now().Format("********"),
						"epttim": time.Now().Add(2 * time.Minute).Format("150405"),
						"yurref": Yurref(),
						"dmanbr": payeeSubAccount, // 付款子单元
						"chlflg": "Y",             // 结算通道，默认为Y，超网代发
					},
				},
				"bb6cdcdlx1": []map[string]any{
					{
						"trxseq": "********",    // 交易序号，8位，批次内唯一
						"accnbr": payeeBankCard, // 收款方银行账号
						"accnam": payeeName,     // 收款人姓名
						"trsamt": amount,        // 交易金额
						"trsdsp": remark,        // 交易备注
					},
				},
			},
		},
		Signature: signature{
			Sigdat: "__signature_sigdat__",
			Sigtim: time.Now().Format("**************"),
		},
	}

	// 发送HTTP请求
	result, err := c.HttpCall(fucode, requestBody)
	if err != nil {
		return AgencyPaymentBody{}, fmt.Errorf("error sending request: %v", err)
	}

	// 响应body结构体

	// 序列化json并反序列化至响应body结构体
	tmpJson, err := json.Marshal(result.Response.Body)
	if err != nil {
		return AgencyPaymentBody{}, fmt.Errorf("error parsing response: %v", err)
	}

	var body AgencyPaymentBody
	if err := json.Unmarshal(tmpJson, &body); err != nil {
		return AgencyPaymentBody{}, fmt.Errorf("error parsing response: %v", err)
	}

	// 打印日志
	c.Logger.Info().Interface("requestBody", requestBody).Msg("请求体")
	c.Logger.Info().Interface("result", result).Msg("结果")
	c.Logger.Info().Interface("body", body).Msg("body")

	reqsta := map[string]string{
		"OPR": "数据接收中",
		"AUT": "等待审批",
		"NTE": "终审完毕",
		"APW": "银行人工审批",
		"WRF": "可疑",
		"BNK": "银行处理中",
		"FIN": "完成",
	}

	for _, v := range body.Bb6cdcbhz1 {
		c.Logger.Info().Interface("v", v).Msg(fmt.Sprintf("代发状态 : %s\n", reqsta[v.Reqsta]))
	}

	return body, nil
}

// AgencyPaymentQuery 查询超网代发结果
//
// yurref 业务参考号
func (c *CmbBank) AgencyPaymentQuery(yurref string) (AgencyPaymentQueryBody, error) {
	fucode := "BB6BPDQY"

	// 构建请求体
	requestBody := bankReqT{
		Request: reqRequest{
			Head: reqHead{
				Funcode: fucode,
				UserID:  c.UID,
				ReqID:   ReqID(),
			},
			Body: map[string]any{
				"bb6bpdqyy1": []map[string]any{
					{
						"buscod": "N03020",
						"yurref": yurref,
					},
				},
			},
		},
		Signature: signature{
			Sigdat: "__signature_sigdat__",
			Sigtim: time.Now().Format("**************"),
		},
	}

	// 发送HTTP请求
	result, err := c.HttpCall(fucode, requestBody)
	if err != nil {
		return AgencyPaymentQueryBody{}, fmt.Errorf("error sending request: %v", err)
	}

	// 响应body结构体

	// 序列化json并反序列化至响应body结构体
	tmpJson, err := json.Marshal(result.Response.Body)
	if err != nil {
		return AgencyPaymentQueryBody{}, fmt.Errorf("error parsing response: %v", err)
	}

	var body AgencyPaymentQueryBody
	if err := json.Unmarshal(tmpJson, &body); err != nil {
		return AgencyPaymentQueryBody{}, fmt.Errorf("error parsing response: %v", err)
	}

	// 打印日志
	c.Logger.Info().Interface("requestBody", requestBody).Msg("请求体")
	c.Logger.Info().Interface("result", result).Msg("结果")
	c.Logger.Info().Interface("body", body).Msg("body")

	reqsta := map[string]string{
		"OPR": "数据接收中",
		"AUT": "等待审批",
		"NTE": "终审完毕",
		"APW": "银行人工审批",
		"WRF": "可疑",
		"BNK": "银行处理中",
		"FIN": "完成",
	}
	for _, v := range body.Bb6bpdqyz1 {
		c.Logger.Info().Interface("v", v).Msg(fmt.Sprintf("批次状态 : %s\n", reqsta[v.Reqsta]))
	}

	stscod := map[string]string{
		"E": "失败",
		"A": "登记",
		"S": "成功",
	}
	for _, v := range body.Bb6bpdqyz2 {
		c.Logger.Info().Interface("v", v).Msg(fmt.Sprintf("收款人:%s, 收款银行卡号:%s, 交易金额:%s, 交易状态:%s\n", v.Accnam, v.Accnbr, v.Trsamt, stscod[v.Stscod]))
	}

	return body, nil
}

// TransferPayment 转账支付
//
// 参数:
//   - dmaNbr 记账子单元编号
//   - crtAcc 收方账号
//   - crtNam 收方户名
//   - crtBnk 收方开户行名称
//   - nusAge 用途
//   - trsAmt 金额
func (c *CmbBank) TransferPayment(dmaNbr, crtAcc, crtNam, crtBnk, nusAge, trsAmt string) (TransferPaymentBody, string, error) {
	if dmaNbr == "" {
		dmaNbr = "**********"
	}
	if crtAcc == "" || crtNam == "" || crtBnk == "" || nusAge == "" || trsAmt == "" {
		return TransferPaymentBody{}, "", fmt.Errorf("参数错误")
	}

	checkTrsAmt, err := strconv.ParseFloat(trsAmt, 64)
	if err != nil {
		return TransferPaymentBody{}, "", fmt.Errorf("金额格式错误")
	}
	if checkTrsAmt <= 0 {
		return TransferPaymentBody{}, "", fmt.Errorf("金额必须大于0")
	}

	fucode := "BB1PAYOP"

	// 初始数据
	yurRef := Yurref()
	bnkFlg := "N"
	if strings.Contains(crtBnk, "招商") {
		bnkFlg = "Y"
	}

	// 构建请求体
	requestBody := bankReqT{
		Request: reqRequest{
			Head: reqHead{
				Funcode: fucode,
				UserID:  c.UID,
				ReqID:   ReqID(),
			},
			Body: map[string]any{
				"bb1paybmx1": []map[string]any{
					{
						"busCod": "N02030",
						"busMod": c.BusMod[fucode],
					},
				},
				"bb1payopx1": []map[string]any{
					{
						"yurRef": yurRef,   // 业务参考号
						"dbtAcc": c.Accnbr, // 转出账号
						"dmaNbr": dmaNbr,   // 记账子单元编号
						"crtAcc": crtAcc,   // 收方账号
						"crtNam": crtNam,   // 收方户名
						"crtBnk": crtBnk,   // 收方开户行名称
						"ccyNbr": "10",     // 币种，固定值
						"trsAmt": trsAmt,   // 金额，两位小数
						"bnkFlg": bnkFlg,   // 收方为招商银行填Y，否则填N
						"stlChn": "I",      // 结算通道，默认为快速
						"nusAge": nusAge,   // 用途
						"drpFlg": "B",
					},
				},
			},
		},
		Signature: signature{
			Sigdat: "__signature_sigdat__",
			Sigtim: time.Now().Format("**************"),
		},
	}

	// 发送HTTP请求
	result, err := c.HttpCall(fucode, requestBody)
	if err != nil {
		return TransferPaymentBody{}, "", fmt.Errorf("error sending request: %v", err)
	}

	// 响应body结构体

	// 序列化json并反序列化至响应body结构体
	tmpJson, err := json.Marshal(result.Response.Body)
	if err != nil {
		return TransferPaymentBody{}, "", fmt.Errorf("error parsing response: %v", err)
	}

	var body TransferPaymentBody
	if err := json.Unmarshal(tmpJson, &body); err != nil {
		return TransferPaymentBody{}, "", fmt.Errorf("error parsing response: %v", err)
	}

	// 打印日志
	c.Logger.Info().Interface("requestBody", requestBody).Msg("请求体")
	c.Logger.Info().Interface("result", result).Msg("结果")
	c.Logger.Info().Interface("body", body).Msg("body")

	reqSts := map[string]string{
		"AUT": "等待审批",
		"NTE": "终审完毕",
		"BNK": "银行处理中",
		"WRF": "银行处理中",
		"FIN": "完成",
		"OPR": "数据接收中",
	}

	rtnFlg := map[string]string{
		"S": "银行支付成功",
		"F": "银行支付失败",
		"B": "银行支付被退票",
		"R": "企业审批否决",
		"D": "企业过期不审批",
		"C": "企业撤销",
		"U": "银行挂账",
	}

	for _, v := range body.Bb1payopz1 {
		c.Logger.Info().Interface("v", v).Msg(fmt.Sprintf("请求状态状态 : %s\n", reqSts[v.ReqSts]))
		if v.ReqSts == "FIN" {
			c.Logger.Info().Interface("v", v).Msg(fmt.Sprintf("业务处理结果 : %s\n", rtnFlg[v.RtnFlg]))
		}
	}

	return body, yurRef, nil
}

// TransferPaymentQuery 查询转账支付结果
//
// 参数:
//   - yurRef 业务参考号
func (c *CmbBank) TransferPaymentQuery(yurRef string) (TransferPaymentQueryBody, error) {
	if yurRef == "" {
		return TransferPaymentQueryBody{}, fmt.Errorf("业务参考号不能为空")
	}
	fucode := "BB1PAYQR"

	// 构建请求体
	requestBody := bankReqT{
		Request: reqRequest{
			Head: reqHead{
				Funcode: fucode,
				UserID:  c.UID,
				ReqID:   ReqID(),
			},
			Body: map[string]any{
				"bb1payqrx1": []map[string]string{
					{
						"busCod": "N02030",
						"yurRef": yurRef,
					},
				},
			},
		},
		Signature: signature{
			Sigdat: "__signature_sigdat__",
			Sigtim: time.Now().Format("**************"),
		},
	}

	// 发送HTTP请求
	result, err := c.HttpCall(fucode, requestBody)
	if err != nil {
		return TransferPaymentQueryBody{}, fmt.Errorf("error sending request: %v", err)
	}

	// 响应body结构体

	// 序列化json并反序列化至响应body结构体
	tmpJson, err := json.Marshal(result.Response.Body)
	if err != nil {
		return TransferPaymentQueryBody{}, fmt.Errorf("error parsing response: %v", err)
	}

	var body TransferPaymentQueryBody
	if err := json.Unmarshal(tmpJson, &body); err != nil {
		return TransferPaymentQueryBody{}, fmt.Errorf("error parsing response: %v", err)
	}

	// 打印日志
	c.Logger.Info().Interface("requestBody", requestBody).Msg("请求体")
	c.Logger.Info().Interface("result", result).Msg("结果")
	c.Logger.Info().Interface("body", body).Msg("body")

	reqSts := map[string]string{
		"AUT": "等待审批",
		"NTE": "终审完毕",
		"BNK": "银行处理中",
		"WRF": "银行处理中",
		"FIN": "完成",
		"OPR": "数据接收中",
	}

	rtnFlg := map[string]string{
		"S": "银行支付成功",
		"F": "银行支付失败",
		"B": "银行支付被退票",
		"R": "企业审批否决",
		"D": "企业过期不审批",
		"C": "企业撤销",
		"U": "银行挂账",
	}

	for _, v := range body.Bb1payqrz1 {
		c.Logger.Info().Interface("v", v).Msg(fmt.Sprintf("请求状态状态 : %s\n", reqSts[v.ReqSts]))
		if v.ReqSts == "FIN" {
			c.Logger.Info().Interface("v", v).Msg(fmt.Sprintf("业务处理结果 : %s\n", rtnFlg[v.RtnFlg]))
			if v.RtnFlg != "S" {
				c.Logger.Info().Interface("v", v).Msg(fmt.Sprintf("错误信息 : %s\n", v.RtnNar))
			}
		}
	}

	return body, nil
}

// TransferPaymentBatch 批量转账支付
//
// 参数:
//   - records 转账支付记录
//
// 返回:
//   - TransferPaymentBatchBody 响应
//   - string 批次编号
//   - error 错误
func (c *CmbBank) TransferPaymentBatch(records []TransferPaymentBatchRecord) (TransferPaymentBatchBody, string, error) {
	fucode := "BB1PAYBH"

	if len(records) == 0 {
		return TransferPaymentBatchBody{}, "", fmt.Errorf("records is empty")
	}
	if len(records) > 1000 {
		return TransferPaymentBatchBody{}, "", fmt.Errorf("records length is greater than 1000")
	}

	// 构建初始数据
	bthNbr := fmt.Sprintf("%s%06d", time.Now().Format("**************"), rand.IntN(999999))

	bb1paybhx1 := []map[string]any{}
	for _, v := range records {

		if v.DmaNbr == "" {
			v.DmaNbr = "**********"
		}
		if v.CrtAcc == "" || v.CrtNam == "" || v.CrtBnk == "" || v.NusAge == "" || v.TrsAmt == "" {
			return TransferPaymentBatchBody{}, "", fmt.Errorf("参数错误")
		}

		checkTrsAmt, err := strconv.ParseFloat(v.TrsAmt, 64)
		if err != nil {
			return TransferPaymentBatchBody{}, "", fmt.Errorf("金额格式错误")
		}
		if checkTrsAmt <= 0 {
			return TransferPaymentBatchBody{}, "", fmt.Errorf("金额必须大于0")
		}

		bnkFlg := "N"
		if strings.Contains(v.CrtBnk, "招商") {
			bnkFlg = "Y"
		}

		bb1paybhx1 = append(bb1paybhx1, map[string]any{
			"yurRef": v.YurRef, // 业务参考号
			"dbtAcc": v.DbtAcc, // 转出账号
			"dmaNbr": v.DmaNbr, // 记账子单元编号
			"crtAcc": v.CrtAcc, // 收方账号
			"crtNam": v.CrtNam, // 收方户名
			"crtBnk": v.CrtBnk, // 收方开户行名称
			"ccyNbr": "10",     // 币种，固定值
			"trsAmt": v.TrsAmt, // 金额，两位小数
			"bnkFlg": bnkFlg,   // 收方为招商银行填Y，否则填N
			"stlChn": "I",      // 结算通道，默认为快速
			"nusAge": v.NusAge, // 用途
			"drpFlg": "B",
		})
	}

	// 构建请求体
	requestBody := bankReqT{
		Request: reqRequest{
			Head: reqHead{
				Funcode: fucode,
				UserID:  c.UID,
				ReqID:   ReqID(),
			},
			Body: map[string]any{
				"bb1bmdbhx1": []map[string]any{
					{
						"busCod": "N02030",
						"busMod": c.BusMod[fucode],
						"bthNbr": bthNbr,          // 批次编号 todo
						"dtlNbr": len(bb1paybhx1), // 笔数 todo
						"ctnFlg": "N",             // 是否续传
					},
				},
				"bb1paybhx1": bb1paybhx1,
			},
		},
		Signature: signature{
			Sigdat: "__signature_sigdat__",
			Sigtim: time.Now().Format("**************"),
		},
	}

	// 发送HTTP请求
	result, err := c.HttpCall(fucode, requestBody)
	if err != nil {
		return TransferPaymentBatchBody{}, "", fmt.Errorf("error sending request: %v", err)
	}

	// 响应body结构体

	// 序列化json并反序列化至响应body结构体
	tmpJson, err := json.Marshal(result.Response.Body)
	if err != nil {
		return TransferPaymentBatchBody{}, "", fmt.Errorf("error parsing response: %v", err)
	}

	var body TransferPaymentBatchBody
	if err := json.Unmarshal(tmpJson, &body); err != nil {
		return TransferPaymentBatchBody{}, "", fmt.Errorf("error parsing response: %v", err)
	}

	// 打印日志
	c.Logger.Info().Interface("requestBody", requestBody).Msg("请求体")
	c.Logger.Info().Interface("result", result).Msg("结果")
	c.Logger.Info().Interface("body", body).Msg("body")

	reqSts := map[string]string{
		"AUT": "等待审批",
		"NTE": "终审完毕",
		"BNK": "银行处理中",
		"WRF": "银行处理中",
		"FIN": "完成",
		"OPR": "数据接收中",
	}

	rtnFlg := map[string]string{
		"S": "银行支付成功",
		"F": "银行支付失败",
		"B": "银行支付被退票",
		"R": "企业审批否决",
		"D": "企业过期不审批",
		"C": "企业撤销",
		"U": "银行挂账",
	}

	for _, v := range body.Bb1paybhz1 {
		c.Logger.Info().Interface("v", v).Msg(fmt.Sprintf("请求状态状态 : %s\n", reqSts[v.ReqSts]))
		if v.ReqSts == "FIN" {
			c.Logger.Info().Interface("v", v).Msg(fmt.Sprintf("业务处理结果 : %s\n", rtnFlg[v.RtnFlg]))
		}
	}

	return body, bthNbr, nil
}

// TransferPaymentBatchResult 批量转账支付批次结果查询
//
// 参数:
//   - startDay 开始日期 格式：YYYYMMDD
//   - endDay 结束日期 格式：YYYYMMDD
//
// 返回:
//   - TransferPaymentBatchResultBody 响应
//   - error 错误
func (c *CmbBank) TransferPaymentBatchResult(startDay, endDay string) (TransferPaymentBatchResultBody, error) {
	if startDay == "" || endDay == "" {
		return TransferPaymentBatchResultBody{}, fmt.Errorf("开始日期和结束日期不能为空")
	}

	t1, err := time.Parse("********", startDay)
	if err != nil {
		return TransferPaymentBatchResultBody{}, fmt.Errorf("开始日期格式错误")
	}
	t2, err := time.Parse("********", endDay)
	if err != nil {
		return TransferPaymentBatchResultBody{}, fmt.Errorf("结束日期格式错误")
	}
	if t1.After(t2) {
		return TransferPaymentBatchResultBody{}, fmt.Errorf("开始日期不能大于结束日期")
	}
	if time.Now().Format("********") != endDay {
		return TransferPaymentBatchResultBody{}, fmt.Errorf("结束日期不能大于当前日期")
	}

	fucode := "BB1QRYBT"

	// 构建请求体
	requestBody := bankReqT{
		Request: reqRequest{
			Head: reqHead{
				Funcode: fucode,
				UserID:  c.UID,
				ReqID:   ReqID(),
			},
			Body: map[string]any{
				"bb1qrybtx1": []map[string]string{
					{
						"begDat": startDay,
						"endDat": endDay,
					},
				},
			},
		},
		Signature: signature{
			Sigdat: "__signature_sigdat__",
			Sigtim: time.Now().Format("**************"),
		},
	}

	// 发送HTTP请求
	result, err := c.HttpCall(fucode, requestBody)
	if err != nil {
		return TransferPaymentBatchResultBody{}, fmt.Errorf("error sending request: %v", err)
	}

	// 响应body结构体

	// 序列化json并反序列化至响应body结构体
	tmpJson, err := json.Marshal(result.Response.Body)
	if err != nil {
		return TransferPaymentBatchResultBody{}, fmt.Errorf("error parsing response: %v", err)
	}

	var body TransferPaymentBatchResultBody
	if err := json.Unmarshal(tmpJson, &body); err != nil {
		return TransferPaymentBatchResultBody{}, fmt.Errorf("error parsing response: %v", err)
	}

	// 打印日志
	c.Logger.Info().Interface("requestBody", requestBody).Msg("请求体")
	c.Logger.Info().Interface("result", result).Msg("结果")
	c.Logger.Info().Interface("body", body).Msg("body")

	reqSts := map[string]string{
		"OPR": "接收中",
		"NTE": "待处理",
		"FIN": "完成",
	}

	rtnFlg := map[string]string{
		"S": "成功",
		"F": "失败",
	}

	for _, v := range body.Bb1qrybtz1 {
		c.Logger.Info().Interface("v", v).Msg(fmt.Sprintf("请求状态状态 : %s\n", reqSts[v.ReqSts]))
		if v.ReqSts == "FIN" {
			c.Logger.Info().Interface("v", v).Msg(fmt.Sprintf("业务处理结果 : %s\n", rtnFlg[v.RtnFlg]))
			if v.RtnFlg != "S" {
				c.Logger.Info().Interface("v", v).Msg(fmt.Sprintf("错误信息 : %s\n", v.ErrTxt))
			}
		}
	}

	return body, nil
}

// TransferPaymentBatchQuery 批量转账支付查询
//
// 参数:
//   - bthNbr 批次编号
//
// 返回:
//   - TransferPaymentBatchQueryBody 响应
//   - error 错误
func (c *CmbBank) TransferPaymentBatchQuery(bthNbr string) (TransferPaymentBatchQueryBody, error) {
	if bthNbr == "" {
		return TransferPaymentBatchQueryBody{}, fmt.Errorf("批次编号不能为空")
	}
	fucode := "BB1QRYBD"

	// 构建请求体
	requestBody := bankReqT{
		Request: reqRequest{
			Head: reqHead{
				Funcode: fucode,
				UserID:  c.UID,
				ReqID:   ReqID(),
			},
			Body: map[string]any{
				"bb1qrybdy1": []map[string]any{
					{
						"bthNbr": bthNbr,
					},
				},
			},
		},
		Signature: signature{
			Sigdat: "__signature_sigdat__",
			Sigtim: time.Now().Format("**************"),
		},
	}

	// 发送HTTP请求
	result, err := c.HttpCall(fucode, requestBody)
	if err != nil {
		return TransferPaymentBatchQueryBody{}, fmt.Errorf("error sending request: %v", err)
	}

	// 响应body结构体

	// 序列化json并反序列化至响应body结构体
	tmpJson, err := json.Marshal(result.Response.Body)
	if err != nil {
		return TransferPaymentBatchQueryBody{}, fmt.Errorf("error parsing response: %v", err)
	}

	var body TransferPaymentBatchQueryBody
	if err := json.Unmarshal(tmpJson, &body); err != nil {
		return TransferPaymentBatchQueryBody{}, fmt.Errorf("error parsing response: %v", err)
	}

	// 打印日志
	c.Logger.Info().Interface("requestBody", requestBody).Msg("请求体")
	c.Logger.Info().Interface("result", result).Msg("结果")
	c.Logger.Info().Interface("body", body).Msg("body")

	reqSts := map[string]string{
		"AUT": "等待审批",
		"NTE": "终审完毕",
		"BNK": "银行处理中",
		"WRF": "银行处理中",
		"FIN": "完成",
		"OPR": "数据接收中",
	}

	rtnFlg := map[string]string{
		"S": "银行支付成功",
		"F": "银行支付失败",
		"B": "银行支付被退票",
		"R": "企业审批否决",
		"D": "企业过期不审批",
		"C": "企业撤销",
		"U": "银行挂账",
	}

	for _, v := range body.Bb1qrybdz1 {
		c.Logger.Info().Interface("v", v).Msg(fmt.Sprintf("请求状态状态 : %s\n", reqSts[v.ReqSts]))
		if v.ReqSts == "FIN" {
			c.Logger.Info().Interface("v", v).Msg(fmt.Sprintf("业务处理结果 : %s\n", rtnFlg[v.RtnFlg]))
			if v.RtnFlg != "S" {
				c.Logger.Info().Interface("v", v).Msg(fmt.Sprintf("错误信息 : %s\n", v.ErrTxt))
			}
		}
	}

	return body, nil
}
