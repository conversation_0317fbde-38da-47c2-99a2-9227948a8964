package cmb

import (
	"bytes"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"math/big"
	"sort"
	"strings"
	"time"

	cryptoRand "crypto/rand"

	"github.com/tjfoc/gmsm/sm2"
	"github.com/tjfoc/gmsm/sm4"
	"github.com/tjfoc/gmsm/x509"
	"golang.org/x/exp/rand"
)

// ReqID 生成请求ID
func ReqID() string {
	return fmt.Sprintf("%s%d", strings.ReplaceAll(time.Now().Format("20060102150405.000"), ".", ""), rand.Intn(99999999))
}

// Yurref 生成业务参考号
func Yurref() string {
	t := strings.ReplaceAll(time.Now().Format("20060102150405.000"), ".", "")
	ri := rand.Intn(9999999999999)
	return fmt.Sprintf("%s%13d", t, ri)
}

// 按照键名排序的结构体
type sortedAscii struct {
	Keys   []string
	Values map[string]interface{}
}

// 自定义排序方法
func (s *sortedAscii) Len() int {
	return len(s.Keys)
}

func (s *sortedAscii) Swap(i, j int) {
	s.Keys[i], s.Keys[j] = s.Keys[j], s.Keys[i]
}

func (s *sortedAscii) Less(i, j int) bool {
	return s.Keys[i] < s.Keys[j] // 按 ASCII 顺序排序
}

// sortedByAscii 对 JSON 数据进行ascii排序
//
// jsonData : 需要进行ascii排序的JSON数据
func sortedByAscii(jsonData []byte) ([]byte, error) {
	// 解码 JSON 到 map
	var jsonObj map[string]any
	if err := json.Unmarshal(jsonData, &jsonObj); err != nil {
		return nil, err
	}

	// 定义一个存储排序后的键值对的 SortedMap
	sortedMap := &sortedAscii{
		Values: make(map[string]any),
	}

	// 遍历 map，将所有的键加入 Keys 列表
	var keys []string
	for key := range jsonObj {
		keys = append(keys, key)
	}
	sort.Strings(keys) // 按 ASCII 顺序排序

	// 将排序后的键值对存储到 sortedMap
	for _, key := range keys {
		sortedMap.Keys = append(sortedMap.Keys, key)
		sortedMap.Values[key] = jsonObj[key]
	}

	// 将排序后的 map 转回 JSON 字符串
	sortedJSON, err := json.Marshal(sortedMap.Values)
	if err != nil {
		return nil, err
	}
	return sortedJSON, nil
}

// sm2Sign SM2签名
//
// 参数:
//   - privateKey sm2私钥(base64编码)
//   - uid 网银账户uid
//   - content 签名内容
//
// 返回:
//
//	签名结果(base64编码)
func sm2Sign(privateKey, uid string, content []byte) ([]byte, error) {
	uid = uid + "00000000000000000000"
	uid = uid[:16]

	// 解析私钥
	pk, err := base64.StdEncoding.DecodeString(privateKey)
	if err != nil {
		return nil, err
	}
	hexPk := hex.EncodeToString(pk)
	priv, err := x509.ReadPrivateKeyFromHex(hexPk)
	if err != nil {
		return nil, err
	}

	// 签名
	r, s, err := sm2.Sm2Sign(priv, content, []byte(uid), cryptoRand.Reader)
	if err != nil {
		return nil, err
	}

	// r s 补位为32位字节
	rBytes := r.Bytes()
	sBytes := s.Bytes()
	rPadded := padBytes(rBytes)
	sPadded := padBytes(sBytes)

	// 拼接
	signatureDER := append(rPadded, sPadded...)

	return []byte(base64.StdEncoding.EncodeToString(signatureDER)), nil
}

// padBytes 补位字节slice为32位字节
//
// 为了满足SM2签名的DER编码要求，需要将r s补位为32位字节
//
// 参数:
//   - input 需要补位的字节slice
//
// 返回:
//
//	补位后的字节slice
func padBytes(input []byte) []byte {
	const length32 = 32
	if len(input) == length32 {
		return input
	}
	padded := make([]byte, length32)
	copy(padded[length32-len(input):], input)
	return padded
}

func sm2Verify(publicKey, uid string, resp bankRespT, originResp []byte) (bool, error) {
	uid = uid + "00000000000000000000"
	uid = uid[:16]

	// 解析公钥
	publicKeyBytes, _ := base64.StdEncoding.DecodeString(publicKey)
	hexPk := hex.EncodeToString(publicKeyBytes)
	pubk, err := x509.ReadPublicKeyFromHex(hexPk)
	if err != nil {
		return false, err
	}

	todoVerify := bytes.ReplaceAll(originResp, []byte(resp.Signature.Sigdat), []byte("__signature_sigdat__"))
	todoVerify, err = sortedByAscii(todoVerify)
	if err != nil {
		return false, err
	}

	sign, err := base64.StdEncoding.DecodeString(resp.Signature.Sigdat)
	if err != nil {
		return false, err
	}

	// 确认签名长度为 64 字节
	if len(sign) != 64 {
		return false, fmt.Errorf("签名长度非法，必须为 64 字节")
	}

	// 分割 r 和 s
	rBytes := sign[:32]
	sBytes := sign[32:]

	// 将字节数组转换为大整数
	r := new(big.Int).SetBytes(rBytes)
	s := new(big.Int).SetBytes(sBytes)

	return sm2.Sm2Verify(pubk, todoVerify, []byte(uid), r, s), nil
}

// sm4Encrypt sm4加密
//
// 参数:
//   - sm4Key sm4 key
//   - uid 网银账户uid
//   - encryptContent 加密内容
func sm4Encrypt(sm4Key, uid string, encryptContent []byte) (string, error) {
	uid = uid + "00000000000000000000"
	uid = uid[:16]
	sm4.IV = []byte(uid)

	if err := sm4.SetIV([]byte(uid)); err != nil {
		return "", err
	}
	encrypt, err := sm4.Sm4Cbc([]byte(sm4Key), encryptContent, true)
	if err != nil {
		return "", err
	}

	return base64.StdEncoding.EncodeToString(encrypt), nil
}

// sm4Decrypt sm4解密
//
// 参数:
//   - sm4Key sm4 key
//   - uid 网银账户uid
//   - decryptContent 解密内容
func sm4Decrypt(sm4Key string, uid string, decryptContent []byte) ([]byte, error) {
	uid = uid + "00000000000000000000"
	uid = uid[:16]

	// 解码base64
	decode := make([]byte, base64.StdEncoding.DecodedLen(len(decryptContent)))
	n, err := base64.StdEncoding.Decode(decode, decryptContent)
	if err != nil {
		return nil, err
	}
	decode = decode[:n]

	// 设置iv
	sm4.IV = []byte(uid)
	if err := sm4.SetIV([]byte(uid)); err != nil {
		return nil, err
	}

	// 解密
	decrypt, err := sm4.Sm4Cbc([]byte(sm4Key), decode, false)
	if err != nil {
		return nil, err
	}

	return decrypt, nil
}
