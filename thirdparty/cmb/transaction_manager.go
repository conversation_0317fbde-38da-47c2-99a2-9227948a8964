package cmb

import (
	"encoding/json"
	"errors"
	"fmt"
	"time"
)

var ErrSubAccountNotFound = errors.New("子单元不存在")

// SubAccountAdd 新增子单元
//
// 参数:
//   - dmanbr 子单元编号
//   - dmanam 子单元名称
func (c *CmbBank) SubAccountAdd(dmanbr, dmanam string) (SubAccountAddBody, error) {
	fucode := "NTDMAADD"

	// 构建请求体
	requestBody := bankReqT{
		Request: reqRequest{
			Head: reqHead{
				Funcode: fucode,
				UserID:  c.UID,
				ReqID:   ReqID(),
			},
			Body: map[string]any{
				"ntbusmody": []map[string]any{
					{"busmod": c.BusMod[fucode]},
				},
				"ntdmaaddx": []map[string]any{
					{
						"accnbr": c.Accnbr,
						"dmanbr": dmanbr,
						"dmanam": dmanam,
						"ovrctl": "N",
						"clstyp": "N",
						"bcktyp": "Y",
						"yurref": Yurref(),
					},
				},
			},
		},
		Signature: signature{
			Sigdat: "__signature_sigdat__",
			Sigtim: time.Now().Format("**************"),
		},
	}

	// 发送HTTP请求
	result, err := c.HttpCall(fucode, requestBody)
	if err != nil {
		return SubAccountAddBody{}, fmt.Errorf("error sending request: %v", err)
	}

	// 解析响应报文
	var body SubAccountAddBody
	tmpJson, err := json.Marshal(result.Response.Body)
	if err != nil {
		return SubAccountAddBody{}, fmt.Errorf("error parsing response: %v", err)
	}

	if err := json.Unmarshal(tmpJson, &body); err != nil {
		return SubAccountAddBody{}, fmt.Errorf("error parsing response: %v", err)
	}

	// 打印日志
	c.Logger.Info().Interface("requestBody", requestBody).Msg("请求体")
	c.Logger.Info().Interface("result", result).Msg("结果")
	c.Logger.Info().Interface("body", body).Msg("body")

	return body, nil
}

// SubAccountQuery 查询子单元
//
// dmanbr 子单元编号
func (c *CmbBank) SubAccountQuery(dmanbr string) (SubAccountQueryBody, error) {
	fucode := "NTDMALST"

	// 构建请求体
	requestBody := bankReqT{
		Request: reqRequest{
			Head: reqHead{
				Funcode: fucode,
				UserID:  c.UID,
				ReqID:   ReqID(),
			},
			Body: map[string]any{
				"ntdmalstx": map[string]any{
					"accnbr": c.Accnbr,
					"dmanbr": dmanbr,
				},
			},
		},
		Signature: signature{
			Sigdat: "__signature_sigdat__",
			Sigtim: time.Now().Format("**************"),
		},
	}

	// 发送HTTP请求
	result, err := c.HttpCall(fucode, requestBody)
	if err != nil {
		return SubAccountQueryBody{}, fmt.Errorf("error sending request: %v", err)
	}

	// 序列化json并反序列化至响应body结构体
	tmpJson, err := json.Marshal(result.Response.Body)
	if err != nil {
		return SubAccountQueryBody{}, fmt.Errorf("error parsing response: %v", err)
	}

	var body SubAccountQueryBody
	if err := json.Unmarshal(tmpJson, &body); err != nil {
		return SubAccountQueryBody{}, fmt.Errorf("error parsing response: %v", err)
	}

	// 打印日志
	c.Logger.Info().Interface("requestBody", requestBody).Msg("请求体")
	c.Logger.Info().Interface("result", result).Msg("结果")
	c.Logger.Info().Interface("body", body).Msg("body")

	if len(result.Response.Body) == 0 {
		return SubAccountQueryBody{}, ErrSubAccountNotFound
	}

	return body, nil
}

// SubAccountUpdate 更新子单元
//
// 参数:
//   - dmanbr 子单元编号
//   - dmanam 子单元名称
func (c *CmbBank) SubAccountUpdate(dmanbr, dmanam string) (SubAccountUpdateBody, error) {
	fucode := "NTDMAMNT"

	// 构建请求体
	requestBody := bankReqT{
		Request: reqRequest{
			Head: reqHead{
				Funcode: fucode,
				UserID:  c.UID,
				ReqID:   ReqID(),
			},
			Body: map[string]any{
				"ntbusmody": []map[string]any{
					{"busmod": c.BusMod[fucode]},
				},
				"ntdmamntx1": []map[string]any{
					{
						"bbknbr": "75",
						"accnbr": c.Accnbr,
						"dmanbr": dmanbr,
						"dmanam": dmanam,
						"ovrctl": "N",
						"clstyp": "N",
						"bcktyp": "Y",
						"yurref": Yurref(),
					},
				},
			},
		},
		Signature: signature{
			Sigdat: "__signature_sigdat__",
			Sigtim: time.Now().Format("**************"),
		},
	}

	// 发送HTTP请求
	result, err := c.HttpCall(fucode, requestBody)
	if err != nil {
		return SubAccountUpdateBody{}, fmt.Errorf("error sending request: %v", err)
	}

	// 序列化json并反序列化至响应body结构体
	tmpJson, err := json.Marshal(result.Response.Body)
	if err != nil {
		return SubAccountUpdateBody{}, fmt.Errorf("error parsing response: %v", err)
	}

	var body SubAccountUpdateBody
	if err := json.Unmarshal(tmpJson, &body); err != nil {
		return SubAccountUpdateBody{}, fmt.Errorf("error parsing response: %v", err)
	}

	// 打印日志
	c.Logger.Info().Interface("requestBody", requestBody).Msg("请求体")
	c.Logger.Info().Interface("result", result).Msg("结果")
	c.Logger.Info().Interface("body", body).Msg("body")

	return body, nil
}

// SubAccountClose 关闭子单元
//
// dmanbr 子单元编号
func (c *CmbBank) SubAccountClose(dmanbr string) (SubAccountCloseBody, error) {
	fucode := "NTDMADLT"

	// 构建请求体
	requestBody := bankReqT{
		Request: reqRequest{
			Head: reqHead{
				Funcode: fucode,
				UserID:  c.UID,
				ReqID:   ReqID(),
			},
			Body: map[string]any{
				"ntbusmody": []map[string]any{
					{"busmod": c.BusMod[fucode]},
				},
				"ntdmadltx1": []map[string]any{
					{
						"accnbr": c.Accnbr,
						"yurref": Yurref(),
					},
				},
				"ntdmadltx2": []map[string]any{
					{"dmanbr": dmanbr},
				},
			},
		},
		Signature: signature{
			Sigdat: "__signature_sigdat__",
			Sigtim: time.Now().Format("**************"),
		},
	}

	// 发送HTTP请求
	result, err := c.HttpCall(fucode, requestBody)
	if err != nil {
		return SubAccountCloseBody{}, fmt.Errorf("error sending request: %v", err)
	}

	// 序列化json并反序列化至响应body结构体
	tmpJson, err := json.Marshal(result.Response.Body)
	if err != nil {
		return SubAccountCloseBody{}, fmt.Errorf("error parsing response: %v", err)
	}

	var body SubAccountCloseBody
	if err := json.Unmarshal(tmpJson, &body); err != nil {
		return SubAccountCloseBody{}, fmt.Errorf("error parsing response: %v", err)
	}

	// 打印日志
	c.Logger.Info().Interface("requestBody", requestBody).Msg("请求体")
	c.Logger.Info().Interface("result", result).Msg("结果")
	c.Logger.Info().Interface("body", body).Msg("body")

	return body, nil
}

// SubAccountTransfer 子单元内部转账
//
// 参数:
//   - dmadbt 付款方子单元编号
//   - dmacrt 收款方子单元编号
//   - trxamt 转账金额
//   - trxtxt 转账说明
func (c *CmbBank) SubAccountTransfer(dmadbt, dmacrt, trxamt, trxtxt string) (SubAccountTransferBody, error) {
	fucode := "NTDMATRX"

	// 构建请求体
	requestBody := bankReqT{
		Request: reqRequest{
			Head: reqHead{
				Funcode: fucode,
				UserID:  c.UID,
				ReqID:   ReqID(),
			},
			Body: map[string]any{
				"ntbusmody": []map[string]any{
					{"busmod": c.BusMod[fucode]},
				},
				"ntdmatrxx1": []map[string]any{
					{
						"accnbr": c.Accnbr,
						"dmadbt": dmadbt,
						"dmacrt": dmacrt,
						"trxamt": trxamt,
						"trxtxt": trxtxt,
						"yurref": Yurref(),
					},
				},
			},
		},
		Signature: signature{
			Sigdat: "__signature_sigdat__",
			Sigtim: time.Now().Format("**************"),
		},
	}

	// 发送HTTP请求
	result, err := c.HttpCall(fucode, requestBody)
	if err != nil {
		return SubAccountTransferBody{}, fmt.Errorf("error sending request: %v", err)
	}

	// 序列化json并反序列化至响应body结构体
	tmpJson, err := json.Marshal(result.Response.Body)
	if err != nil {
		return SubAccountTransferBody{}, fmt.Errorf("error parsing response: %v", err)
	}

	var body SubAccountTransferBody
	if err := json.Unmarshal(tmpJson, &body); err != nil {
		return SubAccountTransferBody{}, fmt.Errorf("error parsing response: %v", err)
	}

	// 打印日志
	c.Logger.Info().Interface("requestBody", requestBody).Msg("请求体")
	c.Logger.Info().Interface("result", result).Msg("结果")
	c.Logger.Info().Interface("body", body).Msg("body")

	return body, nil
}

// SubAccountQueryBalance 查询子单元余额
//
// dmanbr 子单元编号
func (c *CmbBank) SubAccountQueryBalance(dmanbr string) (SubAccountQueryBalanceBody, error) {
	fucode := "NTDMABAL"

	// 构建请求体
	requestBody := bankReqT{
		Request: reqRequest{
			Head: reqHead{
				Funcode: fucode,
				UserID:  c.UID,
				ReqID:   ReqID(),
			},
			Body: map[string]any{
				"ntdmabalx": []map[string]any{
					{
						"accnbr": c.Accnbr,
						"dmanbr": dmanbr,
					},
				},
			},
		},
		Signature: signature{
			Sigdat: "__signature_sigdat__",
			Sigtim: time.Now().Format("**************"),
		},
	}

	// 发送HTTP请求
	result, err := c.HttpCall(fucode, requestBody)
	if err != nil {
		return SubAccountQueryBalanceBody{}, fmt.Errorf("error sending request: %v", err)
	}

	// 序列化json并反序列化至响应body结构体
	tmpJson, err := json.Marshal(result.Response.Body)
	if err != nil {
		return SubAccountQueryBalanceBody{}, fmt.Errorf("error parsing response: %v", err)
	}

	var body SubAccountQueryBalanceBody
	if err := json.Unmarshal(tmpJson, &body); err != nil {
		return SubAccountQueryBalanceBody{}, fmt.Errorf("error parsing response: %v", err)
	}

	// 打印日志
	c.Logger.Info().Interface("requestBody", requestBody).Msg("请求体")
	c.Logger.Info().Interface("result", result).Msg("结果")
	c.Logger.Info().Interface("body", body).Msg("body")

	return body, nil
}

// SubAccountQueryRecord 查询子单元交易记录
//
// dmanbr 子单元编号
func (c *CmbBank) SubAccountQueryRecord(dmanbr, ctnkey string, startDay, endDay string) (SubAccountQueryRecordBody, error) {
	fucode := "NTDMTHLS"

	// 构建请求体
	requestBody := bankReqT{
		Request: reqRequest{
			Head: reqHead{
				Funcode: fucode,
				UserID:  c.UID,
				ReqID:   ReqID(),
			},
			Body: map[string]any{
				"ntdmthlsy": []map[string]any{
					{
						"accnbr": c.Accnbr,
						"dmanbr": dmanbr,
						"begdat": startDay,
						"enddat": endDay,
						"ctnkey": ctnkey,
					},
				},
			},
		},
		Signature: signature{
			Sigdat: "__signature_sigdat__",
			Sigtim: time.Now().Format("**************"),
		},
	}

	// 发送HTTP请求
	result, err := c.HttpCall(fucode, requestBody)
	if err != nil {
		return SubAccountQueryRecordBody{}, fmt.Errorf("error sending request: %v", err)
	}

	// 响应body结构体

	// 序列化json并反序列化至响应body结构体
	tmpJson, err := json.Marshal(result.Response.Body)
	if err != nil {
		return SubAccountQueryRecordBody{}, fmt.Errorf("error parsing response: %v", err)
	}

	var body SubAccountQueryRecordBody
	if err := json.Unmarshal(tmpJson, &body); err != nil {
		return SubAccountQueryRecordBody{}, fmt.Errorf("error parsing response: %v", err)
	}

	// 打印日志
	c.Logger.Info().Interface("requestBody", requestBody).Msg("请求体")
	c.Logger.Info().Interface("result", result).Msg("结果")
	c.Logger.Info().Interface("body", body).Msg("body")

	return body, nil
}
