package cmb

type TransactionNumberBody struct {
	TRANSQUERYBYBREAKPOINT_Y1 []struct {
		AcctNbr            string `json:"acctNbr"`
		ExpectNextSequence string `json:"expectNextSequence"`
		TransDate          string `json:"transDate"`
	} `json:"TRANSQUERYBYBREAKPOINT_Y1"`

	TRANSQUERYBYBREAKPOINT_Z1 []struct {
		CreditAmount string `json:"creditAmount"`
		CreditNums   string `json:"creditNums"`
		CtnFlag      string `json:"ctnFlag"`
		DebitAmount  string `json:"debitAmount"`
		DebitNums    string `json:"debitNums"`
		QueryAcctNbr string `json:"queryAcctNbr"`
	} `json:"TRANSQUERYBYBREAKPOINT_Z1"`

	TRANSQUERYBYBREAKPOINT_Z2 []struct {
		AcctOnlineBal       string `json:"acctOnlineBal"`
		BillNumber          string `json:"billNumber"`
		BusinessName        string `json:"businessName"`
		BusinessText        string `json:"businessText"`
		CtpAcctNbr          string `json:"ctpAcctNbr"`
		CtpBankAddress      string `json:"ctpBankAddress"`
		CtpBankName         string `json:"ctpBankName"`
		CurrencyNbr         string `json:"currencyNbr"`
		ExtendedRemark      string `json:"extendedRemark"`
		FatOrSonAccount     string `json:"fatOrSonAccount"`
		FatOrSonBankAddress string `json:"fatOrSonBankAddress"`
		FatOrSonBankName    string `json:"fatOrSonBankName"`
		FatOrSonCompanyName string `json:"fatOrSonCompanyName"`
		InfoFlag            string `json:"infoFlag"`
		LastTransDate       string `json:"lastTransDate"`
		LastTransTime       string `json:"lastTransTime"`
		LastTransType       string `json:"lastTransType"`
		LastTransUser       string `json:"lastTransUser"`
		LoanCode            string `json:"loanCode"`
		MchOrderNbr         string `json:"mchOrderNbr"`
		RemarkTextClt       string `json:"remarkTextClt"`
		RequestNbr          string `json:"requestNbr"`
		ReversalFlag        string `json:"reversalFlag"`
		TextCode            string `json:"textCode"`
		TransAmount         string `json:"transAmount"`
		TransDate           string `json:"transDate"`
		TransSequenceIdn    string `json:"transSequenceIdn"`
		TransTime           string `json:"transTime"`
		ValueDate           string `json:"valueDate"`
		VirtualNbr          string `json:"virtualNbr"`
		YurRef              string `json:"yurRef"`
	} `json:"TRANSQUERYBYBREAKPOINT_Z2"`
}

type ElectronicReceiptBody struct {
	Checod string `json:"checod"`
	Fildat string `json:"fildat"`
	Istnbr string `json:"istnbr"`
}

type AgencyPaymentBody struct {
	Bb6cdcbhz1 []struct {
		Reqnbr string `json:"reqnbr"`
		Reqsta string `json:"reqsta"`
	} `json:"bb6cdcbhz1"`
}

type AgencyPaymentQueryBody struct {
	Bb6bpdqyz1 []struct {
		Accnam string `json:"accnam"`
		Accnbr string `json:"accnbr"`
		Agctyp string `json:"agctyp"`
		Athflg string `json:"athflg"`
		Bbknbr string `json:"bbknbr"`
		Buscod string `json:"buscod"`
		Busmod string `json:"busmod"`
		Ccynbr string `json:"ccynbr"`
		Dmanbr string `json:"dmanbr"`
		Eptdat string `json:"eptdat"`
		Epttim string `json:"epttim"`
		Errdsp string `json:"errdsp"`
		Nusage string `json:"nusage"`
		Oprdat string `json:"oprdat"`
		Oprsqn string `json:"oprsqn"`
		Oprstp string `json:"oprstp"`
		Reqnbr string `json:"reqnbr"`
		Reqsta string `json:"reqsta"`
		Rtnflg string `json:"rtnflg"`
		Seqcod string `json:"seqcod"`
		Stscod string `json:"stscod"`
		Sucamt string `json:"sucamt"`
		Sucnum string `json:"sucnum"`
		Totamt string `json:"totamt"`
		Trsnum string `json:"trsnum"`
		Trsreq string `json:"trsreq"`
		Trsset string `json:"trsset"`
		Trstyp string `json:"trstyp"`
		Yurref string `json:"yurref"`
	} `json:"bb6bpdqyz1"`

	Bb6bpdqyz2 []struct {
		Accnam string `json:"accnam"`
		Accnbr string `json:"accnbr"`
		Bthnbr string `json:"bthnbr"`
		Stscod string `json:"stscod"`
		Trsamt string `json:"trsamt"`
		Trsdat string `json:"trsdat"`
		Trsdsp string `json:"trsdsp"`
		Trxseq string `json:"trxseq"`
	} `json:"bb6bpdqyz2"`
}

type TransferPaymentBody struct {
	Bb1payopz1 []struct {
		Reqnbr string `json:"reqNbr"`
		EvtIst string `json:"evtIst"`
		ReqSts string `json:"reqSts"`
		RtnFlg string `json:"rtnFlg"`
		ErrCod string `json:"errCod"`
		ErrTxt string `json:"errTxt"`
		MsgTxt string `json:"msgTxt"`
	} `json:"bb1payopz1"`
}

type TransferPaymentQueryBody struct {
	Bb1payqrz1 []struct {
		AathFlg string `json:"athFlg"`
		BnkFlg  string `json:"bnkFlg"`
		BusCod  string `json:"busCod"`
		BusMod  string `json:"busMod"`
		BusNar  string `json:"busNar"`
		CcyNbr  string `json:"ccyNbr"`
		CrtAcc  string `json:"crtAcc"`
		CrtAdr  string `json:"crtAdr"`
		CrtBbk  string `json:"crtBbk"`
		CrtBnk  string `json:"crtBnk"`
		CrtNam  string `json:"crtNam"`
		DbtAcc  string `json:"dbtAcc"`
		DbtBbk  string `json:"dbtBbk"`
		DbtNam  string `json:"dbtNam"`
		DmaNbr  string `json:"dmaNbr"`
		EptDat  string `json:"eptDat"`
		EptTim  string `json:"eptTim"`
		LgnNam  string `json:"lgnNam"`
		NtfCh1  string `json:"ntfCh1"`
		NtfCh2  string `json:"ntfCh2"`
		NusAge  string `json:"nusAge"`
		OprAls  string `json:"oprAls"`
		OprDat  string `json:"oprDat"`
		OprSqn  string `json:"oprSqn"`
		RcvBrd  string `json:"rcvBrd"`
		ReqNbr  string `json:"reqNbr"`
		ReqSts  string `json:"reqSts"`
		RtnFlg  string `json:"rtnFlg"`
		RtnNar  string `json:"rtnNar"`
		StlChn  string `json:"stlChn"`
		TrsAmt  string `json:"trsAmt"`
		TrsTyp  string `json:"trsTyp"`
		TrxSeq  string `json:"trxSeq"`
		TrxSet  string `json:"trxSet"`
		UsrNam  string `json:"usrNam"`
		YurRef  string `json:"yurRef"`
	} `json:"bb1payqrz1"`
}

type TransferPaymentBatchRecord struct {
	YurRef string `json:"yurRef"` // 业务参考号
	DbtAcc string `json:"dbtAcc"` // 转出账号
	DmaNbr string `json:"dmaNbr"` // 记账子单元编号
	CrtAcc string `json:"crtAcc"` // 收方账号
	CrtNam string `json:"crtNam"` // 收方户名
	CrtBnk string `json:"crtBnk"` // 收方开户行名称
	TrsAmt string `json:"trsAmt"` // 金额，两位小数
	NusAge string `json:"nusAge"` // 用途
}

type TransferPaymentBatchBody struct {
	Bb1paybhz1 []struct {
		BthNbr string `json:"bthNbr"`
		ReqSts string `json:"reqSts"`
		RtnFlg string `json:"rtnFlg"`
		ErrCod string `json:"errCod"`
		ErrTxt string `json:"errTxt"`
		MsgTxt string `json:"msgTxt"`
	} `json:"bb1paybhz1"`
}

type TransferPaymentBatchResultBody struct {
	Bb1qrybtz1 []struct {
		BthNbr string `json:"bthNbr"`
		BusCod string `json:"busCod"`
		BusMod string `json:"busMod"`
		DtlAmt string `json:"dtlAmt"`
		DtlNum string `json:"dtlNum"`
		ReqSts string `json:"reqSts"`
		Rsv30z string `json:"rsv30z"`
		RtnFlg string `json:"rtnFlg"`
		SucAmt string `json:"sucAmt"`
		SucNum string `json:"sucNum"`
		TrsDat string `json:"trsDat"`
		TrsTim string `json:"trsTim"`
		ErrTxt string `json:"errTxt"`
	} `json:"bb1qrybtz1"`
}

type TransferPaymentBatchQueryBody struct {
	Bb1qrybdy1 []map[string]any `json:"bb1qrybdy1"`
	Bb1qrybdz1 []struct {
		BnkFlg string `json:"bnkFlg"`
		BrdNbr string `json:"brdNbr"`
		BthNbr string `json:"bthNbr"`
		CcyNbr string `json:"ccyNbr"`
		CnvNbr string `json:"cnvNbr"`
		CopNbr string `json:"copNbr"`
		CouCod string `json:"couCod"`
		CrtAcc string `json:"crtAcc"`
		CrtBbk string `json:"crtBbk"`
		CrtNam string `json:"crtNam"`
		CrtSqn string `json:"crtSqn"`
		CtyCod string `json:"ctyCod"`
		DbtAcc string `json:"dbtAcc"`
		DbtBbk string `json:"dbtBbk"`
		DmaNbr string `json:"dmaNbr"`
		DrpFlg string `json:"drpFlg"`
		EptDat string `json:"eptDat"`
		EptTim string `json:"eptTim"`
		ErrTxt string `json:"errTxt"`
		InpTel string `json:"inpTel"`
		IssRef string `json:"issRef"`
		KjtAcc string `json:"kjtAcc"`
		MsgTxt string `json:"msgTxt"`
		NpsTyp string `json:"npsTyp"`
		NtfCh1 string `json:"ntfCh1"`
		NtfCh2 string `json:"ntfCh2"`
		NusAge string `json:"nusAge"`
		PasNbr string `json:"pasNbr"`
		PayTyp string `json:"payTyp"`
		RcvChk string `json:"rcvChk"`
		RemNbr string `json:"remNbr"`
		ReqSts string `json:"reqSts"`
		RsvAmt string `json:"rsvAmt"`
		RsvNa1 string `json:"rsvNa1"`
		RsvNa2 string `json:"rsvNa2"`
		RsvNb1 string `json:"rsvNb1"`
		RsvNb2 string `json:"rsvNb2"`
		RtnFlg string `json:"rtnFlg"`
		SplC80 string `json:"splC80"`
		StlChn string `json:"stlChn"`
		TrsAmt string `json:"trsAmt"`
		TrsCat string `json:"trsCat"`
		TrsTyp string `json:"trsTyp"`
		TrxAmt string `json:"trxAmt"`
		TrxCod string `json:"trxCod"`
		YurRef string `json:"yurRef"`
	} `json:"bb1qrybdz1"`
}

type SubAccountAddBody struct {
	Ntdmabadz1 []struct {
		Dmanbr string `json:"dmanbr"`
		Errcod string `json:"errcod"`
		Rtnsts string `json:"rtnsts"`
		Errtxt string `json:"errtxt"`
	} `json:"ntdmabadz1"`
}

type SubAccountQueryBody struct {
	Ntdmalstz []struct {
		Accnbr string `json:"accnbr"`
		Bcktyp string `json:"bcktyp"`
		Clstyp string `json:"clstyp"`
		Dmaccy string `json:"dmaccy"`
		Dmanam string `json:"dmanam"`
		Dmanbr string `json:"dmanbr"`
		Eftdat string `json:"eftdat"`
		Ovrctl string `json:"ovrctl"`
		Rltchk string `json:"rltchk"`
		Stscod string `json:"stscod"`
		Tlyopr string `json:"tlyopr"`
	} `json:"ntdmalstz"`
}

type SubAccountUpdateBody struct {
	Ntoprrtnz []struct {
		Errcod string `json:"errcod"`
		Reqnbr string `json:"reqnbr"`
		Reqsts string `json:"reqsts"`
		Rtnflg string `json:"rtnflg"`
	} `json:"ntoprrtnz"`
}

type SubAccountCloseBody struct {
	Ntdmadltz []struct {
		Dmanam string `json:"dmanam"`
		Dmanbr string `json:"dmanbr"`
		Prcsts string `json:"prcsts"`
		Rtncod string `json:"rtncod"`
	} `json:"ntdmadltz"`
	Ntoprrtnz []struct {
		Reqnbr string `json:"reqnbr"`
		Reqsts string `json:"reqsts"`
		Rtnflg string `json:"rtnflg"`
	} `json:"ntoprrtnz"`
}

type SubAccountTransferBody struct {
	Ntdmatrxz1 []struct {
		Accnbr string `json:"accnbr"`
		Dmanbr string `json:"dmanbr"`
		Trxamt string `json:"trxamt"`
		Trxdat string `json:"trxdat"`
		Trxdir string `json:"trxdir"`
		Trxnbr string `json:"trxnbr"`
	} `json:"ntdmatrxz1"`
	Ntoprrtnz []struct {
		Reqnbr string `json:"reqnbr"`
		Reqsts string `json:"reqsts"`
		Rtnflg string `json:"rtnflg"`
	} `json:"ntoprrtnz"`
}

type SubAccountQueryBalanceBody struct {
	Ntdmalstz []struct {
		Accnbr string `json:"accnbr"`
		Actbal string `json:"actbal"`
		Dmaccy string `json:"dmaccy"`
		Dmanam string `json:"dmanam"`
		Dmanbr string `json:"dmanbr"`
		Eftdat string `json:"eftdat"`
		Stscod string `json:"stscod"`
	} `json:"ntdmabalz"`
}

type SubAccountQueryRecordBody struct {
	Ntdmthlsz []struct {
		Accnbr string `json:"accnbr"`
		Autflg string `json:"autflg"`
		Ccynbr string `json:"ccynbr"`
		Dmanam string `json:"dmanam"`
		Dmanbr string `json:"dmanbr"`
		Rpyacc string `json:"rpyacc"`
		Rpynam string `json:"rpynam"`
		Trxamt string `json:"trxamt"`
		Trxdat string `json:"trxdat"`
		Trxdir string `json:"trxdir"`
		Trxnbr string `json:"trxnbr"`
		Trxtim string `json:"trxtim"`
		Trxtxt string `json:"trxtxt"`
	} `json:"ntdmthlsz"`
	Ntdmthlsy []struct {
		Accnbr string `json:"accnbr"`
		Dmanbr string `json:"dmanbr"`
		Begdat string `json:"begdat"`
		Enddat string `json:"enddat"`
		Ctnkey string `json:"ctnkey"`
	} `json:"ntdmthlsy"`
}
