package cmb

import (
	"fmt"
	"strings"
	"wlhy/toolbox/logger"
)

// CmbBank 招商银行银企直连
type CmbBank struct {
	ApiUrl     string            // 请求地址
	Accnbr     string            // 银行账号
	UID        string            // 网银账户uid
	Bbknbr     string            // 招商银行内部银行行号
	Sm4Key     string            // sm4 key
	PrivateKey string            // sm2 私钥
	PublicKey  string            // sm2 公钥
	BusMod     map[string]string // 业务模式
	Logger     *logger.Logger    // 日志
}

// NewCmbBank 创建CMB实例
//
// 参数:
//   - baseUrl 请求地址
//   - accnbr 银行账号
//   - uid 网银账户uid
//   - bbknbr 招商银行内部银行行号
//   - sm4Key sm4 key
//   - privateKey sm2 私钥
//   - publicKey sm2 公钥
//   - busMod 业务模式
func NewCmbBank(apiUrl, accnbr, uid, bbknbr, sm4Key, privateKey, publicKey string, busMod map[string]string) *CmbBank {
	logOpt := logger.DefaultOptions()
	logOpt.FilePrefix = "cmb"
	l, err := logger.NewLogger(logOpt)
	if err != nil {
		panic(err)
	}
	c := &CmbBank{
		ApiUrl:     apiUrl,
		Accnbr:     accnbr,
		UID:        uid,
		Bbknbr:     bbknbr,
		Sm4Key:     sm4Key,
		PrivateKey: privateKey,
		PublicKey:  publicKey,
		BusMod:     busMod,
		Logger:     l,
	}

	return c
}

func QueryRecordsByWaybillNo(c *CmbBank, waybillNo string, queryDay string) ([]string, error) {
	var records []string

	ctnKey := ""
	for {
		rr, err := c.SubAccountQueryRecord("", ctnKey, queryDay, queryDay)
		if err != nil {
			return nil, err
		}

		for _, v := range rr.Ntdmthlsz {
			if strings.Contains(v.Trxtxt, waybillNo) {
				r := fmt.Sprintf("%s,%s,%s,%s,%s,%s,%s,%s\n", v.Dmanam, v.Dmanbr, v.Trxdir, v.Rpynam, v.Rpyacc, v.Trxamt, v.Trxdat+" "+v.Trxtim, v.Trxtxt)
				records = append(records, r)
			}
		}

		if len(rr.Ntdmthlsy) == 0 {
			break
		}
		ctnKey = rr.Ntdmthlsy[0].Ctnkey
		if ctnKey == "" {
			break
		}
		fmt.Println(ctnKey)
	}

	return records, nil
}
