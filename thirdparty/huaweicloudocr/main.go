package huaweicloudocr

import (
	"encoding/base64"
	"os"
	"strings"

	"github.com/huaweicloud/huaweicloud-sdk-go-v3/core/auth/basic"
	ocr "github.com/huaweicloud/huaweicloud-sdk-go-v3/services/ocr/v1"
	"github.com/huaweicloud/huaweicloud-sdk-go-v3/services/ocr/v1/model"
	region "github.com/huaweicloud/huaweicloud-sdk-go-v3/services/ocr/v1/region"
)

func NewOcrClient() (*ocr.OcrClient, error) {
	ak := "AQXETYHWGTI2INYBLLQA"
	sk := "J9WdUkw20uM6fV02JJnmMSaQXugKP8B1oR0OswuQ"

	auth, err := basic.NewCredentialsBuilder().
		WithAk(ak).
		WithSk(sk).
		SafeBuild()

	if err != nil {
		return nil, err
	}

	region, err := region.SafeValueOf("cn-north-4")
	if err != nil {
		return nil, err
	}
	cb, err := ocr.OcrClientBuilder().
		WithRegion(region).
		WithCredential(auth).
		SafeBuild()

	if err != nil {
		return nil, err
	}

	client := ocr.NewOcrClient(cb)
	return client, nil
}

func TransportationLicense(client *ocr.OcrClient, imageFile string) (*model.RecognizeTransportationLicenseResponse, error) {
	var body *model.TransportationLicenseRequestBody

	if strings.Contains(imageFile, "https://") {
		imageUrl := imageFile
		body = &model.TransportationLicenseRequestBody{
			Url: &imageUrl,
		}
	} else {
		if _, err := os.Stat(imageFile); err != nil {
			return nil, err
		}

		cc, _ := os.ReadFile(imageFile)
		base64Image := base64.StdEncoding.EncodeToString(cc)
		body = &model.TransportationLicenseRequestBody{
			Image: &base64Image,
		}
	}

	request := &model.RecognizeTransportationLicenseRequest{}
	request.Body = body
	response, err := client.RecognizeTransportationLicense(request)
	if err != nil {
		return nil, err
	}
	return response, nil
}
