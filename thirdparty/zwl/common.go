package zwl

import (
	"bytes"
	"crypto/hmac"
	"crypto/sha1"
	"crypto/tls"
	"encoding/hex"
	"errors"
	"fmt"
	"io"
	"net/http"
	"sort"
	"strings"
)

// convertMapToString converts a map to a string
func (z *ZWL) convertMapToString(param map[string]string) string {
	var sb strings.Builder
	if len(param) > 0 {
		for key, value := range param {
			sb.WriteString(key)
			sb.WriteString("=")
			sb.WriteString(value)
			sb.WriteString("&")
		}
		// Remove the trailing '&' by slicing the string
		if sb.Len() > 0 {
			str := sb.String()
			return str[:len(str)-1]
		}
	}
	return ""
}

// httpsCall performs an HTTPS call.
func (z *ZWL) httpsCall(url string, reqBody string) ([]byte, error) {
	// Create a new request
	req, err := http.NewRequest("POST", url, bytes.NewBufferString(reqBody))
	if err != nil {
		fmt.Println("Error creating request:", err)
		return nil, err
	}

	// Set headers
	req.Header.Set("Content-Length", fmt.Sprint(len(reqBody)))
	req.Header.Set("charset", "UTF-8")
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8")

	// Disable SSL verification (only for testing, not for production)
	tr := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client := &http.Client{Transport: tr}

	// Send the request
	resp, err := client.Do(req)
	if err != nil {
		fmt.Println("Error performing request:", err)
		return nil, err
	}
	defer resp.Body.Close()

	// Read the response
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Println("Error reading response:", err)
		return nil, err
	}

	return responseBody, nil
}

// processParam processes the parameters and computes a signature.
func (z *ZWL) processParam(param map[string]string) error {
	// Check if the "srt" key exists in the map
	srt, ok := param["srt"]
	if !ok {
		return errors.New("missing 'srt' parameter")
	}

	// Remove the "srt" key from the map
	delete(param, "srt")

	// Create a list of combined key-value strings
	var paramValueList []string
	for key, value := range param {
		paramValueList = append(paramValueList, key+value)
	}

	// Sort the list
	sort.Strings(paramValueList)

	// Concatenate sorted values into a single string
	concatenated := []string{}
	concatenated = append(concatenated, paramValueList...)

	// Compute HMAC-SHA1 signature
	signature, err := z.hmacSha1(concatenated, []byte(srt))
	if err != nil {
		return err
	}

	// Encode signature to hex string
	sign := encodeHexStr(signature)

	// Add the signature to the map
	param["sign"] = sign

	return nil
}

// hmacSha1 calculates HMAC-SHA1.
func (z *ZWL) hmacSha1(data []string, key []byte) ([]byte, error) {
	// Create a new HMAC using SHA-1 and the provided key
	mac := hmac.New(sha1.New, key)

	// Write each data element to the HMAC
	for _, str := range data {
		_, err := mac.Write([]byte(str))
		if err != nil {
			return nil, err
		}
	}

	// Calculate the HMAC and return the result
	return mac.Sum(nil), nil
}

// encodeHexStr encodes a byte array to a hexadecimal string.
func encodeHexStr(data []byte) string {
	return strings.ToUpper(hex.EncodeToString(data))
}
