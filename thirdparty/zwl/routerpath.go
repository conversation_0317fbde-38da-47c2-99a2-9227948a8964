package zwl

import (
	"bytes"
	"encoding/json"
	"fmt"
)

type RouterPathResp struct {
	Status int                  `json:"status"`
	Result RouterPathRespResult `json:"result"`
}

type RouterPathRespResult struct {
	Mileage    string                `json:"mileage"`
	ParkSize   string                `json:"parkSize"`
	ParkArray  []RouterPathRespPark  `json:"parkArray"`
	TrackArray []RouterPathRespTrack `json:"trackArray"`
}

type RouterPathRespTrack struct {
	Agl string `json:"agl"`
	Gtm string `json:"gtm"`
	Hgt string `json:"hgt"`
	Lat string `json:"lat"`
	Lon string `json:"lon"`
	Mlg string `json:"mlg"`
	Spd string `json:"spd"`
}

type RouterPathRespPark struct {
	ParkAdr  string `json:"parkAdr"`
	ParkBte  string `json:"parkBte"`
	ParkEte  string `json:"parkEte"`
	ParkLat  string `json:"parkLat"`
	ParkLon  string `json:"parkLon"`
	ParkMins string `json:"parkMins"`
}

var ErrNotResultRouterPath error = fmt.Errorf("无结果")

// RouterPath 查询车辆轨迹
func (z *ZWL) RouterPath(vclN, qryBtm, qryEtm string) (RouterPathResp, error) {
	var result RouterPathResp

	url := z.BaseUrl + "api/getdata/routerPath"

	// 创建 form-data
	formData := map[string]string{
		"token":  z.AccessToken,
		"cid":    z.CID,
		"srt":    z.Srt,
		"vclN":   vclN,
		"vco":    "2",
		"qryBtm": qryBtm,
		"qryEtm": qryEtm,
	}

	// 处理参数
	z.processParam(formData)

	// 将参数转换为字符串
	reqBody := z.convertMapToString(formData)

	// 发起请求
	responseBody, err := z.httpsCall(url, reqBody)
	if err != nil {
		return result, err
	}

	// token失效时重新获取token
	if string(responseBody) == `{"result":"","status":"1010"}` ||
		string(responseBody) == `{"result":"","status":"1016"}` {
		z.Token()
		return z.RouterPath(vclN, qryBtm, qryEtm)
	}

	if bytes.Contains(responseBody, []byte("无结果")) {
		return result, ErrNotResultRouterPath
	}

	if err := json.Unmarshal(responseBody, &result); err != nil {
		fmt.Println(string(responseBody))
		return result, err
	}

	if result.Status != 1001 {
		return result, fmt.Errorf("error code: %d", result.Status)
	}

	return result, nil
}

// RouterPath 查询车辆轨迹，返回原始数据，不做数据处理
func (z *ZWL) RouterPathRow(vclN, qryBtm, qryEtm string) (string, error) {
	url := z.BaseUrl + "api/getdata/routerPath"

	// 创建 form-data
	formData := map[string]string{
		"token":  z.AccessToken,
		"cid":    z.CID,
		"srt":    z.Srt,
		"vclN":   vclN,
		"vco":    "2",
		"qryBtm": qryBtm,
		"qryEtm": qryEtm,
	}

	// 处理参数
	z.processParam(formData)

	// 将参数转换为字符串
	reqBody := z.convertMapToString(formData)

	// 发起请求
	responseBody, err := z.httpsCall(url, reqBody)
	if err != nil {
		return "", err
	}

	// token失效时重新获取token
	if string(responseBody) == `{"result":"","status":"1010"}` ||
		string(responseBody) == `{"result":"","status":"1016"}` {
		z.Token()
		return z.RouterPathRow(vclN, qryBtm, qryEtm)
	}

	return string(responseBody), nil
}

func (z *ZWL) CheckTruckExist(vclN, vco string) (bool, error) {
	// url := z.BaseUrl + "api/getdata/checkTruckExistV2"
	url := z.BaseUrl + "api/GetData/checkVehicleExistV2"

	// 创建 form-data
	formData := map[string]string{
		"token": z.AccessToken,
		"cid":   z.CID,
		"srt":   z.Srt,
		"vclN":  vclN + "_" + vco,
	}

	// 处理参数
	z.processParam(formData)

	// 将参数转换为字符串
	reqBody := z.convertMapToString(formData)

	// 发起请求
	responseBody, err := z.httpsCall(url, reqBody)
	if err != nil {
		return false, err
	}

	// token失效时重新获取token
	if string(responseBody) == `{"result":"","status":"1010"}` ||
		string(responseBody) == `{"result":"","status":"1016"}` {
		z.Token()
		return z.CheckTruckExist(vclN, vco)
	}

	var result map[string]any
	if err := json.Unmarshal(responseBody, &result); err != nil {
		return false, err
	}

	if result["status"].(float64) != 1001 {
		return false, fmt.Errorf("error code: %s", result["status"].(string))
	}

	if result["result"].(string) == "no" {
		return false, nil
	}

	return true, nil
}
