package zwl

import (
	"encoding/json"
	"fmt"
	"strconv"
)

type ZWL struct {
	User        string
	PWD         string
	Srt         string
	CID         string
	BaseUrl     string
	AccessToken string
}

func NewZWL() *ZWL {
	user := "t9YwRoFN-o9o8-49ZM-N9Bo-090csxAsYwst"
	pwd := "Z8xwxNR0U5E0EN5EEcssdRtY9NMYt8"
	srt := "t9Yo5Bkp-1900-J9EE-s9dg-09UQ8xRsYwEt"
	cid := "t9YgtwRp-x90U-g9Bw-Z9d8-09MQFx9sYwAt"
	baseUrl := "http://56paasopen1.chinawuliu.com.cn/"

	z := &ZWL{
		User:    user,
		PWD:     pwd,
		Srt:     srt,
		CID:     cid,
		BaseUrl: baseUrl,
	}

	token, err := z.Token()
	if err != nil {
		panic(err)
	}
	z.AccessToken = token
	return z
}

// ComBalance 获取余额
func (z *ZWL) ComBalance() (float64, error) {
	type BalanceResp struct {
		Status     string `json:"status"`
		ComBalance string `json:"combalance"`
	}

	// /api/getdata/ComFinanceListDay
	url := z.BaseUrl + "api/getdata/ComFinanceListDay"

	// 创建 form-data
	formData := map[string]string{
		"token": z.AccessToken,
		"cid":   z.CID,
		"Page":  "1",
	}

	// 处理参数
	z.processParam(formData)

	// 将参数转换为字符串
	reqBody := z.convertMapToString(formData)

	// 发起请求
	responseBody, err := z.httpsCall(url, reqBody)
	if err != nil {
		return 0, err
	}

	var result BalanceResp
	if err := json.Unmarshal(responseBody, &result); err != nil {
		return 0, err
	}

	if result.Status != "1001" {
		return 0, fmt.Errorf("error code: %s", result.Status)
	}

	return strconv.ParseFloat(result.ComBalance, 64)
}
