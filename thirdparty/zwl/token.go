package zwl

import (
	"encoding/json"
	"fmt"
)

type LoginResp struct {
	Status string `json:"status"`
	Result string `json:"result"`
}

func (z *ZWL) Token() (string, error) {
	url := z.BaseUrl + "api/getdata/comlogin/"

	// 创建 form-data
	formData := map[string]string{
		"user": z.User,
		"pwd":  z.PWD,
		"cid":  z.CID,
		"srt":  z.Srt,
	}

	// ProcessParam processes the parameters and computes a signature.
	z.processParam(formData)

	// Convert form-data to string
	reqBody := z.convertMapToString(formData)

	// httpsCall performs an HTTPS call.
	responseBody, err := z.httpsCall(url, reqBody)
	if err != nil {
		return "", err
	}

	result := LoginResp{}
	if err := json.Unmarshal(responseBody, &result); err != nil {
		return "", err
	}

	if result.Status != "1001" {
		return "", fmt.Errorf("error code: %s", result.Status)
	}
	z.AccessToken = result.Result

	return result.Result, nil
}
