package niukafu

import (
	"bytes"
	"encoding/json"
	"errors"
	"io"
	"strings"
)

type InvoiceReq struct {
	WaybillNum string `json:"waybillNum"`
}

type InvoiceResp struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data struct {
		PlateNum         string `json:"plateNum"`
		VehicleType      int    `json:"vehicleType"`
		WaybillNum       string `json:"waybillNum"`
		WaybillStatus    int    `json:"waybillStatus"`
		WaybillStartTime string `json:"waybillStartTime"`
		WaybillEndTime   string `json:"waybillEndTime"`
		Result           []struct {
			InvoiceNum         string  `json:"invoiceNum"`
			InvoiceCode        string  `json:"invoiceCode"`
			InvoiceMakeTime    string  `json:"invoiceMakeTime"`
			EnStation          string  `json:"enStation"`
			ExStation          string  `json:"exStation"`
			EnDate             string  `json:"enDate"`
			ExDate             string  `json:"exDate"`
			ExTime             string  `json:"exTime"`
			Fee                int     `json:"fee"`
			TotalTaxAmount     int     `json:"totalTaxAmount"`
			PlateNum           string  `json:"plateNum"`
			VehicleType        int     `json:"vehicleType"`
			SellerName         string  `json:"sellerName"`
			SellerTaxpayerCode string  `json:"sellerTaxpayerCode"`
			WaybillNum         string  `json:"waybillNum"`
			WaybillStartTime   string  `json:"waybillStartTime"`
			WaybillEndTime     string  `json:"waybillEndTime"`
			TotalAmount        int     `json:"totalAmount"`
			TaxRate            float64 `json:"taxRate"`
			InvoiceType        string  `json:"invoiceType"`
			InvoiceUrl         string  `json:"invoiceUrl"`
			InvoiceHtmlUrl     string  `json:"invoiceHtmlUrl"`
		} `json:"result"`
	} `json:"data"`
}

func (n *NiuKaFu) Invoice(req InvoiceReq) (InvoiceResp, error) {
	url := strings.TrimRight(n.BaseUrl, "/") + "/etcwd/v1/invoice/findInvoiceByNum?access_token=" + n.AccessToken

	body := map[string]string{
		"waybillNum": req.WaybillNum,
	}

	bodyJson, _ := json.Marshal(body)

	// 创建一个自定义的 http.Client，并配置 Transport 关闭 TLS 验证
	resp, err := n.HttpClient.Post(url, "application/json", bytes.NewBuffer(bodyJson))
	if err != nil {
		resp.Body.Close()
		return InvoiceResp{}, err
	}
	defer resp.Body.Close()

	var result InvoiceResp
	respBody, _ := io.ReadAll(resp.Body)
	if err := json.Unmarshal(respBody, &result); err != nil {
		return InvoiceResp{}, err
	}

	if result.Code != 0 {
		return result, errors.New(result.Msg)
	}

	return result, nil
}
