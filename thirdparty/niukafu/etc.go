package niukafu

import (
	"crypto/tls"
	"net/http"
	"time"
	"wlhy/toolbox/logger"
)

type NiuKaFu struct {
	BaseUrl     string
	AppID       string
	Secret      string
	CorpID      string
	AccessToken string
	HttpClient  *http.Client
}

func NewEtc() *NiuKaFu {
	// 企业id saas_a1qh0oa756q
	// 应用id corp_a1qhgpexm2o
	// secret *******************************************
	// 沙箱secret bUloQkVtUmxCZjQ3aHhiMjAyMzEyMDQxMDM0MjQ5OTc
	e := &NiuKaFu{
		BaseUrl: "https://api.open.nucarf.net",
		AppID:   "corp_a1qhgpexm2o",
		Secret:  "*******************************************",
		CorpID:  "saas_a1qh0oa756q",
		HttpClient: &http.Client{
			Transport: &http.Transport{
				TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
			},
			Timeout: 20 * time.Second,
		},
	}

	token, err := e.Token()
	if err != nil {
		logger.Stdout.Error(err.Error())
		return nil
	}
	e.AccessToken = token
	return e
}
