package niukafu

import (
	"bytes"
	"crypto/tls"
	"encoding/json"
	"errors"
	"io"
	"net/http"
	"strings"
)

type TokenResp struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    struct {
		AccessToken string `json:"accessToken"`
		ExpiresIn   int    `json:"expiresIn"`
	} `json:"data"`
}

func (n *NiuKaFu) Token() (string, error) {
	url := strings.TrimRight(n.BaseUrl, "/") + "/auth/v1/getToken"

	body := map[string]string{
		"appId":  n.AppID,
		"secret": n.Secret,
		"corpId": n.CorpID,
	}

	bodyJson, _ := json.Marshal(body)

	// 创建一个自定义的 http.Client，并配置 Transport 关闭 TLS 验证
	client := &http.Client{
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
		},
	}

	resp, err := client.Post(url, "application/json", bytes.NewBuffer(bodyJson))
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	var result TokenResp
	respBody, _ := io.ReadAll(resp.Body)
	if err := json.Unmarshal(respBody, &result); err != nil {
		return "", err
	}

	if result.Code != 0 {
		return "", errors.New(result.Message)
	}

	n.AccessToken = result.Data.AccessToken

	return result.Data.AccessToken, nil
}
