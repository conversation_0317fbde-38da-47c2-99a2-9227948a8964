package niukafu

import (
	"bytes"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"
)

type WaybillStartReq struct {
	WaybillNum     string    `json:"waybillNum"`
	PlateNum       string    `json:"plateNum"`
	PlateColor     string    `json:"plateColor"`
	SourceAddr     string    `json:"sourceAddr"`
	DestAddr       string    `json:"destAddr"`
	StartTime      time.Time `json:"startTime"`
	EndTime        time.Time `json:"endTime"`
	Fee            int       `json:"fee"`
	DriverName     string    `json:"driverName"`
	DriverContract string    `json:"driverContract"`
}

type WaybillStartResp struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data struct {
		WaybillType int `json:"waybillType"`
	} `json:"data"`
}

func (n *NiuKaFu) WaybillStart(req WaybillStartReq) (WaybillStartResp, error) {
	url := strings.TrimRight(n.BaseUrl, "/") + "/etcwd/v1/waybill/waybillStart?access_token=" + n.AccessToken

	// 从URL下载合同文件内容
	fileResp, err := http.Get(req.DriverContract)
	if err != nil {
		fmt.Println("Error fetching file:", err)
		return WaybillStartResp{}, err
	}
	defer fileResp.Body.Close()

	// 读取文件内容
	fileData, err := io.ReadAll(fileResp.Body)
	if err != nil {
		fmt.Println("Error reading file data:", err)
		return WaybillStartResp{}, err
	}

	// 将文件内容编码为Base64
	base64Data := base64.StdEncoding.EncodeToString(fileData)

	plateColors := map[string]int{
		"蓝色":    0,
		"黄色":    1,
		"黑色":    2,
		"白色":    3,
		"渐变绿色":  4,
		"黄绿渐变色": 5,
		"蓝白渐变色": 6,
		"未确定":   9,
	}

	body := map[string]any{
		"waybillNum":     req.WaybillNum,
		"plateNum":       req.PlateNum,
		"plateColor":     plateColors[req.PlateColor],
		"sourceAddr":     req.SourceAddr,
		"destAddr":       req.DestAddr,
		"startTime":      req.StartTime.Format("2006-01-02T15:04:05"),
		"endTime":        req.EndTime.Format("2006-01-02T15:04:05"),
		"fee":            req.Fee,
		"driverName":     req.DriverName,
		"driverContract": base64Data,
	}

	bodyJson, _ := json.Marshal(body)

	// 创建一个自定义的 http.Client，并配置 Transport 关闭 TLS 验证
	resp, err := n.HttpClient.Post(url, "application/json", bytes.NewBuffer(bodyJson))
	if err != nil {
		return WaybillStartResp{}, err
	}
	defer resp.Body.Close()

	var result WaybillStartResp
	respBody, _ := io.ReadAll(resp.Body)
	if err := json.Unmarshal(respBody, &result); err != nil {
		return WaybillStartResp{}, err
	}

	if result.Code != 0 {
		return result, errors.New(result.Msg)
	}
	return result, nil
}

type WaybillEndReq struct {
	WaybillNum   string    `json:"waybillNum"`
	RealDestAddr string    `json:"realDestAddr"`
	EndTime      time.Time `json:"endTime"`
}

type WaybillEndResp struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data struct {
		ReceiveTime string `json:"receiveTime"`
	} `json:"data"`
}

func (n *NiuKaFu) WaybillEnd(req WaybillEndReq) (WaybillEndResp, error) {
	url := strings.TrimRight(n.BaseUrl, "/") + "/etcwd/v1/waybill/waybillEnd?access_token=" + n.AccessToken

	body := map[string]any{
		"waybillNum":   req.WaybillNum,
		"realDestAddr": req.RealDestAddr,
		"endTime":      req.EndTime.Format("2006-01-02T15:04:05"),
	}

	bodyJson, _ := json.Marshal(body)

	// 创建一个自定义的 http.Client，并配置 Transport 关闭 TLS 验证
	resp, err := n.HttpClient.Post(url, "application/json", bytes.NewBuffer(bodyJson))
	if err != nil {
		return WaybillEndResp{}, err
	}
	defer resp.Body.Close()

	var result WaybillEndResp
	respBody, _ := io.ReadAll(resp.Body)
	if err := json.Unmarshal(respBody, &result); err != nil {
		return WaybillEndResp{}, err
	}

	if result.Code != 0 {
		return result, errors.New(result.Msg)
	}

	return result, nil
}
