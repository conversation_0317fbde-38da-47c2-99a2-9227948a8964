package oss

import (
	"errors"
	"wlhy/toolbox/logger"

	"github.com/aliyun/aliyun-oss-go-sdk/oss"
)

type OSS struct {
	Bucket *oss.Bucket
}

func NewOSSByBucket(bucketName string) (*OSS, error) {
	if bucketName == "" {
		return nil, errors.New("bucketName is empty")
	}

	accessKeyID := "LTAI5t7t2tg58yCVGg41WTYZ"
	accessKeySecret := "******************************"

	endPoint := ""
	if bucketName == "cfhszy" || bucketName == "cfwlhy" || bucketName == "cfhswlhy" {
		endPoint = "oss-cn-zhangjiakou.aliyuncs.com"
	}
	if bucketName == "cfhswlhyhz" {
		endPoint = "oss-cn-hangzhou.aliyuncs.com"
	}

	return NewOSS(endPoint, accessKeyID, accessKeySecret, bucketName)
}

func NewOSS(endPoint, accessKeyID, accessKeySecret, bucketName string) (*OSS, error) {
	client, err := oss.New(endPoint, accessKeyID, accessKeySecret)
	if err != nil {
		logger.Stdout.Error(err.Error())
		return nil, err
	}

	// 填写存储空间名称，例如examplebucket。
	bucket, err := client.Bucket(bucketName)
	if err != nil {
		logger.Stdout.Error(err.Error())
		return nil, err
	}

	return &OSS{
		Bucket: bucket,
	}, nil
}

func (o *OSS) PutObjectFromFile(object, filename string) error {
	err := o.Bucket.PutObjectFromFile(object, filename)
	if err != nil {
		return err
	}

	return nil
}

func (o *OSS) SignObjectUrl(object string) (string, error) {
	url, err := o.Bucket.SignURL(object, oss.HTTPGet, 86400)
	if err != nil {
		return "", err
	}
	return url, nil
}
