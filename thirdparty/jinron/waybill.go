package jinron

import (
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"
)

// WaybillStartReq 运单开始请求体结构体
type WaybillStartReq struct {
	Num               string    `json:"num"`               // 运单编号
	PlateNum          string    `json:"plateNum"`          // 车牌号
	PlateColor        string    `json:"plateColor"`        // 车牌颜色
	StartTime         time.Time `json:"startTime"`         // 运单开始时间
	SourceAddr        string    `json:"sourceAddr"`        // 起始地址
	DestAddr          string    `json:"destAddr"`          // 目标地址
	PredictEndTime    time.Time `json:"predictEndTime"`    // 预计结束时间
	Fee               int       `json:"fee"`               // 运费，单位：分
	DriverContractUrl string    `json:"driverContractUrl"` // 承运合同URL
}

// WaybillStartResp 运单开始响应体结构体
type WaybillStartResp struct {
	Code    int    `json:"code"`    // 响应状态 参考附录4.4
	Message string `json:"message"` // 文本描述
}

// WaybillStart 实时运单开始
func (e *ETC) WaybillStart(req *WaybillStartReq) (*WaybillStartResp, error) {
	formData := map[string]string{
		"num":            req.Num,
		"plateNum":       req.PlateNum,
		"plateColor":     plateColor(req.PlateColor),
		"startTime":      req.StartTime.Format("2006-01-02 15:04:05"),
		"sourceAddr":     req.SourceAddr,
		"destAddr":       req.DestAddr,
		"predictEndTime": req.PredictEndTime.Format("2006-01-02 15:04:05"),
		"fee":            fmt.Sprintf("%d", req.Fee),
		"taxPlayerCode":  e.TaxPlayerCode,
		"apiKey":         e.ApiKey,
		"url":            req.DriverContractUrl,
	}

	e.Logger.Info().Msg(fmt.Sprintf("%s %s %v", "调用实时运单开始接口", req.Num, formData))
	request, err := generateRequest("https://tms.jrerdangjia.com/api/v2.0/waybill/start", formData)
	if err != nil {
		e.Logger.Error().Msg(fmt.Sprintf("%s %s %v", "调用实时运单开始接口失败", req.Num, err))
		return nil, err
	}

	body, err := httpCall(request)
	if err != nil {
		e.Logger.Error().Msg(fmt.Sprintf("%s %s %v", "调用实时运单开始接口失败", req.Num, err))
		return nil, err
	}
	e.Logger.Info().Msg(fmt.Sprintf("%s %s %v", "调用实时运单开始接口完成", req.Num, string(body)))

	var result WaybillStartResp
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, err
	}

	if result.Code != 200 {
		if strings.Contains(result.Message, "未备案") {
			_, err := e.TruckRegister(&TruckRegisterReq{
				PlateNum:   req.PlateNum,
				PlateColor: req.PlateColor,
			})
			if err != nil {
				result.Message = err.Error()
				return &result, err
			}
			return e.WaybillStart(req)
		}
		return &result, errors.New(result.Message)
	}

	return &result, nil
}

// HistoryWaybillStart 历史运单开始
func (e *ETC) HistoryWaybillStart(req *WaybillStartReq) (*WaybillStartResp, error) {
	formData := map[string]string{
		"num":            req.Num,
		"plateNum":       req.PlateNum,
		"plateColor":     plateColor(req.PlateColor),
		"startTime":      req.StartTime.Format("2006-01-02 15:04:05"),
		"sourceAddr":     req.SourceAddr,
		"destAddr":       req.DestAddr,
		"predictEndTime": req.PredictEndTime.Format("2006-01-02 15:04:05"),
		"fee":            fmt.Sprintf("%d", req.Fee),
		"taxPlayerCode":  e.TaxPlayerCode,
		"apiKey":         e.ApiKey,
		"url":            req.DriverContractUrl,
	}

	e.Logger.Info().Msg(fmt.Sprintf("%s %s %v", "调用历史运单开始接口", req.Num, formData))
	request, err := generateRequest("https://tms.jrerdangjia.com/api/v2.0/waybill/startHistory", formData)
	if err != nil {
		e.Logger.Error().Msg(fmt.Sprintf("%s %s %v", "调用历史运单开始接口失败", req.Num, err))
		return nil, err
	}

	body, err := httpCall(request)
	if err != nil {
		e.Logger.Error().Msg(fmt.Sprintf("%s %s %v", "调用历史运单开始接口失败", req.Num, err))
		return nil, err
	}
	e.Logger.Info().Msg(fmt.Sprintf("%s %s %v", "调用历史运单开始接口完成", req.Num, string(body)))

	var result WaybillStartResp
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, err
	}

	if result.Code != 200 {
		if strings.Contains(result.Message, "未备案") {
			_, err := e.TruckRegister(&TruckRegisterReq{
				PlateNum:   req.PlateNum,
				PlateColor: req.PlateColor,
			})
			if err != nil {
				result.Message = err.Error()
				return &result, err
			}
			return e.HistoryWaybillStart(req)
		}

		return &result, errors.New(result.Message)
	}
	return &result, nil
}

// WaybillEndReq 运单结束请求体结构体
type WaybillEndReq struct {
	Num          string    `json:"num"`          // 运单编号
	RealDestAddr string    `json:"realDestAddr"` // 实际目标地址
	EndTime      time.Time `json:"endTime"`      // 结束时间
}

// WaybillEndResp 运单结束响应体结构体
type WaybillEndResp struct {
	Code    int    `json:"code"`    // 响应状态 参考附录4.4
	Message string `json:"message"` // 文本描述
}

// WaybillEnd 实时运单结束
func (e *ETC) WaybillEnd(req *WaybillEndReq) (*WaybillEndResp, error) {
	formData := map[string]string{
		"num":          req.Num,
		"realDestAddr": req.RealDestAddr,
		"endTime":      req.EndTime.Format("2006-01-02 15:04:05"),
		"apiKey":       e.ApiKey,
	}

	e.Logger.Info().Msg(fmt.Sprintf("%s %s %v", "调用实时运单结束接口", req.Num, formData))
	request, err := generateRequest("https://tms.jrerdangjia.com/api/v2.0/waybill/end", formData)
	if err != nil {
		e.Logger.Error().Msg(fmt.Sprintf("%s %s %v", "调用实时运单结束接口失败", req.Num, err))
		return nil, err
	}

	body, err := httpCall(request)
	if err != nil {
		e.Logger.Error().Msg(fmt.Sprintf("%s %s %v", "调用实时运单结束接口失败", req.Num, err))
		return nil, err
	}
	e.Logger.Info().Msg(fmt.Sprintf("%s %s %v", "调用实时运单结束接口完成", req.Num, string(body)))

	var result WaybillEndResp
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, err
	}

	if result.Code != 200 {
		return &result, errors.New(result.Message)
	}
	return &result, nil
}

// HistoryWaybillEnd 历史运单结束
func (e *ETC) HistoryWaybillEnd(req *WaybillEndReq) (*WaybillEndResp, error) {
	formData := map[string]string{
		"num":          req.Num,
		"realDestAddr": req.RealDestAddr,
		"endTime":      req.EndTime.Format("2006-01-02 15:04:05"),
		"apiKey":       e.ApiKey,
	}

	e.Logger.Info().Msg(fmt.Sprintf("%s %s %v", "调用历史运单结束接口", req.Num, formData))
	request, err := generateRequest("https://tms.jrerdangjia.com/api/v2.0/waybill/endHistory", formData)
	if err != nil {
		e.Logger.Error().Msg(fmt.Sprintf("%s %s %v", "调用历史运单结束接口失败", req.Num, err))
		return nil, err
	}

	body, err := httpCall(request)
	if err != nil {
		e.Logger.Error().Msg(fmt.Sprintf("%s %s %v", "调用历史运单结束接口失败", req.Num, err))
		return nil, err
	}
	e.Logger.Info().Msg(fmt.Sprintf("%s %s %v", "调用历史运单结束接口完成", req.Num, string(body)))

	var result WaybillEndResp
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, err
	}

	if result.Code != 200 {
		return &result, errors.New(result.Message)
	}
	return &result, nil
}

// WaybillActualFullReq 实时完整运单请求体结构体
type WaybillActualFullReq struct {
	Num               string    `json:"num"`
	PlateNum          string    `json:"plateNum"`
	PlateColor        string    `json:"plateColor"`
	StartTime         time.Time `json:"startTime"`
	SourceAddr        string    `json:"sourceAddr"`
	DestAddr          string    `json:"destAddr"`
	PredictEndTime    time.Time `json:"predictEndTime"`
	Fee               int       `json:"fee"`
	RealDestAddr      string    `json:"realDestAddr"`
	EndTime           time.Time `json:"endTime"`
	DriverContractUrl string    `json:"driverContractUrl"`
}

// WaybillActualFullResp 实时完整运单响应体结构体
type WaybillActualFullResp struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

// WaybillActualFull 实时完整运单
func (e *ETC) WaybillActualFull(req *WaybillActualFullReq) (*WaybillActualFullResp, error) {
	formData := map[string]string{
		"num":            req.Num,
		"plateNum":       req.PlateNum,
		"plateColor":     plateColor(req.PlateColor),
		"startTime":      req.StartTime.Format("2006-01-02 15:04:05"),
		"sourceAddr":     req.SourceAddr,
		"destAddr":       req.DestAddr,
		"predictEndTime": req.PredictEndTime.Format("2006-01-02 15:04:05"),
		"fee":            fmt.Sprintf("%d", req.Fee),
		"taxPlayerCode":  e.TaxPlayerCode,
		"realDestAddr":   req.RealDestAddr,
		"endTime":        req.EndTime.Format("2006-01-02 15:04:05"),
		"url":            req.DriverContractUrl,
		"apiKey":         e.ApiKey,
	}

	e.Logger.Info().Msg(fmt.Sprintf("%s %s %v", "调用实时完整运单接口", req.Num, formData))
	request, err := generateRequest("https://tms.jrerdangjia.com/api/v2.0/waybill/actualFull", formData)
	if err != nil {
		e.Logger.Error().Msg(fmt.Sprintf("%s %s %v", "调用实时完整运单接口失败", req.Num, err))
		return nil, err
	}

	body, err := httpCall(request)
	if err != nil {
		e.Logger.Error().Msg(fmt.Sprintf("%s %s %v", "调用实时完整运单接口失败", req.Num, err))
		return nil, err
	}
	e.Logger.Info().Msg(fmt.Sprintf("%s %s %v", "调用实时完整运单接口完成", req.Num, string(body)))

	var result WaybillActualFullResp
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, err
	}

	if result.Code != 200 {
		if strings.Contains(result.Message, "未备案") {
			_, err := e.TruckRegister(&TruckRegisterReq{
				PlateNum:   req.PlateNum,
				PlateColor: req.PlateColor,
			})
			if err != nil {
				result.Message = err.Error()
				return &result, err
			}
			return e.WaybillActualFull(req)
		}
		return &result, errors.New(result.Message)
	}
	return &result, nil
}

// CancelWaybillResp 取消运单响应体结构体
type CancelWaybillResp struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

// CancelWaybill 取消运单
func (e *ETC) CancelWaybill(waybillNo string) (*CancelWaybillResp, error) {
	formData := map[string]string{
		"num":    waybillNo,
		"apiKey": e.ApiKey,
	}

	e.Logger.Info().Msg(fmt.Sprintf("%s %s %v", "调用取消运单接口", waybillNo, formData))
	request, err := generateRequest("https://tms.jrerdangjia.com/api/v2.0/waybill/cancelWaybill", formData)
	if err != nil {
		e.Logger.Error().Msg(fmt.Sprintf("%s %s %v", "调用取消运单接口失败", waybillNo, err))
		return nil, err
	}

	body, err := httpCall(request)
	if err != nil {
		e.Logger.Error().Msg(fmt.Sprintf("%s %s %v", "调用取消运单接口失败", waybillNo, err))
		return nil, err
	}
	e.Logger.Info().Msg(fmt.Sprintf("%s %s %v", "调用取消运单接口完成", waybillNo, string(body)))

	var result CancelWaybillResp
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, err
	}

	if result.Code != 200 {
		return &result, errors.New(result.Message)
	}
	return &result, nil
}
