package jinron

import (
	"encoding/json"
	"errors"
	"fmt"
)

// QueryInvoiceResp 查询发票响应体结构体
type QueryInvoiceResp struct {
	Code    int              `json:"code"`    // 响应状态 参考附录4.4
	Message string           `json:"message"` // 文本描述
	Data    QueryInvoiceData `json:"data"`    // 数据
}

// QueryInvoiceData 查询发票数据结构体
type QueryInvoiceData struct {
	PlateNum         string               `json:"plateNum"`         // 车牌号
	WaybillNum       string               `json:"waybillNum"`       // 运单单号
	WaybillStatus    int                  `json:"waybillStatus"`    // 运单状态 参考附录4.3
	WaybillStartTime string               `json:"waybillStartTime"` // 运单开始时间
	WaybillEndTime   string               `json:"waybillEndTime"`   // 运单结束时间
	ReceiveTime      string               `json:"receiveTime"`      // 接收时间
	Type             string               `json:"type"`             // 运单类型（NORMAL实时 和 HISTORY 历史）
	Result           []QueryInvoiceResult `json:"result"`           // 发票数据集
}

// QueryInvoiceResult 查询发票结果结构体
type QueryInvoiceResult struct {
	WaybillNum         string  `json:"waybillNum"`         // 运单编号
	WaybillStatus      int     `json:"waybillStatus"`      // 运单状态 参考 附录4.3
	WaybillStartTime   string  `json:"waybillStartTime"`   // 运单开始时间
	WaybillEndTime     string  `json:"waybillEndTime"`     // 运单结束时间
	PlateNum           string  `json:"plateNum"`           // 车牌号
	VehicleType        string  `json:"vehicleType"`        // 车型
	TransactionId      string  `json:"transactionId"`      // 交易ID
	EnStation          string  `json:"enStation"`          // 入口收费站
	ExStation          string  `json:"exStation"`          // 出口收费站
	ExTime             string  `json:"exTime"`             // 交易时间
	TradeMatchTime     string  `json:"tradeMatchTime"`     // 交易匹配时间
	Fee                int     `json:"fee"`                // 交易金额，单位：分
	InvoiceCode        string  `json:"invoiceCode"`        // 发票代码
	InvoiceNum         string  `json:"invoiceNum"`         // 发票号码
	InvoiceMakeTime    string  `json:"invoiceMakeTime"`    // 开票时间
	SellerName         string  `json:"sellerName"`         // 销方名称
	SellerTaxpayerCode string  `json:"sellerTaxpayerCode"` // 销方税号
	Amount             int     `json:"amount"`             // 金额，单位：分
	TaxRate            float64 `json:"taxRate"`            // 税率
	TotalTaxAmount     int     `json:"totalTaxAmount"`     // 税额（可抵扣金额），单位：分
	TotalAmount        int     `json:"totalAmount"`        // 价税合计，单位：分
	InvoiceUrl         string  `json:"invoiceUrl"`         // 发票PDF地址
	InvoiceHtmlUrl     string  `json:"invoiceHtmlUrl"`     // 发票查看地址
	InvoiceType        string  `json:"invoiceType"`        // 发票种类
	BuyerName          string  `json:"buyerName"`          // 购方名称
	BuyerTaxpayerCode  string  `json:"buyerTaxpayerCode"`  // 购方税号
}

// QueryInvoice 查询发票
func (e *ETC) QueryInvoice(waybillNo string) (*QueryInvoiceResp, error) {
	formData := map[string]string{
		"num":    waybillNo,
		"apiKey": e.ApiKey,
	}

	request, err := generateRequest("https://tms.jrerdangjia.com/api/v2.0/waybill/queryInvoice", formData)
	if err != nil {
		return nil, err
	}

	e.Logger.Info().Msg(fmt.Sprintf("%s %s %v", "调用查询发票接口", waybillNo, formData))
	body, err := httpCall(request)
	if err != nil {
		e.Logger.Error().Msg(fmt.Sprintf("%s %s %v", "调用查询发票接口失败", waybillNo, err))
		return nil, err
	}
	e.Logger.Info().Msg(fmt.Sprintf("%s %s %v", "调用查询发票接口完成", waybillNo, string(body)))

	var result QueryInvoiceResp
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, err
	}

	if result.Code != 200 {
		return nil, errors.New(result.Message)
	}

	return &result, nil
}
