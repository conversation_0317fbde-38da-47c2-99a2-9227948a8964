package jinron

import (
	"bytes"
	"io"
	"mime/multipart"
	"net/http"
)

// plateColor 转换车牌颜色
func plateColor(color string) string {
	m := map[string]string{
		"蓝色":    "0",
		"黄色":    "1",
		"黑色":    "2",
		"白色":    "3",
		"渐变绿色":  "4",
		"黄绿渐变色": "5",
		"黄绿色":   "5",
		"蓝白渐变色": "6",
		"临时牌照":  "7",
		"未确定":   "9",
		"绿色":    "11",
		"红色":    "12",
	}
	return m[color]
}

// generateRequest 生成请求
func generateRequest(apiUrl string, fields map[string]string) (*http.Request, error) {
	var buffer bytes.Buffer
	writer := multipart.NewWriter(&buffer)

	for k, v := range fields {
		if err := writer.WriteField(k, v); err != nil {
			return nil, err
		}
	}

	if err := writer.Close(); err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", apiUrl, &buffer)
	if err != nil {
		return nil, err
	}

	req.Header.Set("Content-Type", writer.FormDataContentType())

	return req, nil
}

// httpCall HTTP请求
func httpCall(request *http.Request) ([]byte, error) {
	client := &http.Client{}
	resp, err := client.Do(request)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	return body, nil
}
