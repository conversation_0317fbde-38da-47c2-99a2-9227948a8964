package jinron

import (
	"encoding/json"
	"errors"
	"fmt"
)

// TruckRegisterReq 车辆备案请求结构体
type TruckRegisterReq struct {
	PlateNum   string `json:"plateNum"`
	PlateColor string `json:"plateColor"`
}

// TruckRegisterResp 车辆备案返回结果结构体
type TruckRegisterResp struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

// TruckRegister 车辆备案
func (e *ETC) TruckRegister(req *TruckRegisterReq) (*TruckRegisterResp, error) {
	formData := map[string]string{
		"plateNum":   req.PlateNum,
		"plateColor": plateColor(req.PlateColor),
		"apiKey":     e.<PERSON><PERSON>,
	}

	e.Logger.Info().Msg(fmt.Sprintf("%s %s %v", "调用车辆备案接口", req.PlateNum, formData))
	request, err := generateRequest("https://tms.jrerdangjia.com/api/v2.0/waybill/truckRegister", formData)
	if err != nil {
		return nil, err
	}

	body, err := httpCall(request)
	if err != nil {
		e.Logger.Error().Msg(fmt.Sprintf("%s %s %v", "调用车辆备案接口失败", req.PlateNum, err))
		return nil, err
	}
	e.Logger.Info().Msg(fmt.Sprintf("%s %s %v", "调用车辆备案接口完成", req.PlateNum, string(body)))

	var result TruckRegisterResp
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, err
	}

	if result.Code != 200 {
		return &result, errors.New(result.Message)
	}
	return &result, nil
}
