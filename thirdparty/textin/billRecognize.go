package textin

import (
	"encoding/json"
)

type Bill<PERSON>ecognizeResp struct {
	Message string              `json:"message"`
	Code    int                 `json:"code"`
	Pages   []BillRecognizePage `json:"pages"`
}

type BillRecognizePage struct {
	PageNumber int                 `json:"page_number"`
	Result     BillRecognizeResult `json:"result"`
}

type BillRecognizeResult struct {
	ObjectList []BillRecognizeObjectList `json:"object_list"`
}

type BillRecognizeObjectList struct {
	ItemList []BillRecognizeItemList `json:"item_list"`
}

type BillRecognizeItemList struct {
	Value       string `json:"value"`
	Key         string `json:"key"`
	Description string `json:"description"`
}

func BillRecognize(filename string) (BillRecognizeResp, error) {
	apiUrl := "https://api.textin.com/ai/service/v1/bill_recognize_v2"

	resp, err := request(apiUrl, filename)
	if err != nil {
		return BillRecognizeResp{}, err
	}

	var result BillRecognizeResp
	err = json.Unmarshal(resp, &result)
	if err != nil {
		return BillRecognizeResp{}, err
	}

	return result, nil
}
