package textin

import (
	"bytes"
	"io"
	"net/http"
	"os"
)

func request(apiUrl string, filename string) ([]byte, error) {
	// 假设这是你要上传的二进制数据
	f, err := os.Open(filename)
	if err != nil {
		return nil, err
	}
	defer f.Close()

	data, err := io.ReadAll(f)
	if err != nil {
		return nil, err
	}

	// 创建一个 bytes.Buffer 来存储数据
	body := bytes.NewBuffer(data)

	// 创建 POST 请求
	url := apiUrl // 替换为实际的目标 URL
	req, err := http.NewRequest("POST", url, body)
	if err != nil {
		return nil, err
	}

	// 设置 Content-Type 为 application/octet-stream
	req.Header.Set("Content-Type", "application/octet-stream")
	req.Header.Set("x-ti-app-id", AppID)
	req.Header.Set("x-ti-secret-code", AppSecret)

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	// 读取响应
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	return respBody, nil
}
