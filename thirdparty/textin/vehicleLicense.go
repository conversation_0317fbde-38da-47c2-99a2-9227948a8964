package textin

import (
	"encoding/json"
)

type VehicleLicenseResponse struct {
	Message  string               `json:"message"`
	Duration int                  `json:"duration"`
	Result   VehicleLicenseResult `json:"result"`
	Code     int                  `json:"code"`
}

type VehicleLicenseResult struct {
	RotatedImageHeight int                  `json:"rotated_image_height"`
	ImageAngle         int                  `json:"image_angle"`
	RotatedImageWidth  int                  `json:"rotated_image_width"`
	ItemList           []VehicleLicenseItem `json:"item_list"`
	Type               string               `json:"type"`
}

type VehicleLicenseItem struct {
	Value       string  `json:"value"`
	Description string  `json:"description"`
	Position    []int   `json:"position"`
	Key         string  `json:"key"`
	Confidence  float64 `json:"confidence"`
}

func VehicleLicense(filename string) (VehicleLicenseResult, error) {
	apiUrl := "https://api.textin.com/robot/v1.0/api/vehicle_license"

	resp, err := request(apiUrl, filename)
	if err != nil {
		return VehicleLicenseResult{}, err
	}

	var result VehicleLicenseResponse
	err = json.Unmarshal(resp, &result)
	if err != nil {
		return VehicleLicenseResult{}, err
	}

	return result.Result, nil

}
