package textin

import (
	"encoding/json"
)

type DriverLicenseItem struct {
	Value       string  `json:"value"`       // 值
	Description string  `json:"description"` // 描述
	Position    []int   `json:"position"`    // 位置坐标
	Key         string  `json:"key"`         // 键名
	Confidence  float64 `json:"confidence"`  // 置信度
}

type DriverLicenseResult struct {
	RotatedImageHeight int                 `json:"rotated_image_height"` // 旋转后图片高度
	ImageAngle         int                 `json:"image_angle"`          // 图片角度
	RotatedImageWidth  int                 `json:"rotated_image_width"`  // 旋转后图片宽度
	ItemList           []DriverLicenseItem `json:"item_list"`            // 项目列表
	Type               string              `json:"type"`                 // 类型
}

type DriverLicenseResponse struct {
	Message  string              `json:"message"`  // 消息
	Duration int                 `json:"duration"` // 持续时间
	Result   DriverLicenseResult `json:"result"`   // 结果
	Code     int                 `json:"code"`     // 状态码
}

func DriverLicense(filename string) (DriverLicenseResponse, error) {
	apiUrl := "https://api.textin.com/robot/v1.0/api/driver_license"

	resp, err := request(apiUrl, filename)
	if err != nil {
		return DriverLicenseResponse{}, err
	}

	var result DriverLicenseResponse
	err = json.Unmarshal(resp, &result)
	if err != nil {
		return DriverLicenseResponse{}, err
	}

	return result, nil
}
